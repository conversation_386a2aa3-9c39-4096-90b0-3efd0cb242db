<?php include APP_ROOT . '/app/View/layout/header.php'; ?>

<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <div class="flex justify-center">
                <div class="w-16 h-16 gradient-bg rounded-lg flex items-center justify-center">
                    <i class="fas fa-search text-white text-2xl"></i>
                </div>
            </div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                登入您的帳戶
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
                或
                <a href="/auth/register" class="font-medium text-blue-600 hover:text-blue-500">
                    註冊新帳戶
                </a>
            </p>
        </div>

        <!-- 顯示錯誤訊息 -->
        <?php if (flash('error')): ?>
            <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative alert-auto-hide">
                <span class="block sm:inline"><?= htmlspecialchars(flash('error')) ?></span>
            </div>
        <?php endif; ?>

        <!-- 顯示成功訊息 -->
        <?php if (flash('success')): ?>
            <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded relative alert-auto-hide">
                <span class="block sm:inline"><?= htmlspecialchars(flash('success')) ?></span>
            </div>
        <?php endif; ?>

        <form class="mt-8 space-y-6" action="/auth/login" method="POST">
            <?= csrf_field() ?>
            
            <div class="rounded-md shadow-sm -space-y-px">
                <div>
                    <label for="email" class="sr-only">電子郵件地址</label>
                    <input id="email" 
                           name="email" 
                           type="email" 
                           autocomplete="email" 
                           required 
                           value="<?= htmlspecialchars(old('email')) ?>"
                           class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm" 
                           placeholder="電子郵件地址">
                </div>
                <div>
                    <label for="password" class="sr-only">密碼</label>
                    <input id="password" 
                           name="password" 
                           type="password" 
                           autocomplete="current-password" 
                           required 
                           class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm" 
                           placeholder="密碼">
                </div>
            </div>

            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <input id="remember" 
                           name="remember" 
                           type="checkbox" 
                           value="1"
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <label for="remember" class="ml-2 block text-sm text-gray-900">
                        記住我
                    </label>
                </div>

                <div class="text-sm">
                    <a href="/auth/forgot-password" class="font-medium text-blue-600 hover:text-blue-500">
                        忘記密碼？
                    </a>
                </div>
            </div>

            <div>
                <button type="submit" 
                        class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white gradient-bg hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                        <i class="fas fa-sign-in-alt text-blue-300 group-hover:text-blue-200"></i>
                    </span>
                    登入
                </button>
            </div>

            <div class="text-center">
                <p class="text-sm text-gray-600">
                    測試帳戶：<EMAIL> / admin123
                </p>
            </div>
        </form>
    </div>
</div>

<?php include APP_ROOT . '/app/View/layout/footer.php'; ?>