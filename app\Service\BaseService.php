<?php

namespace App\Service;

use App\Database\Database;
use Exception;

/**
 * 基礎服務類
 * 提供所有服務的共用功能
 */
abstract class BaseService
{
    protected Database $db;
    protected array $config;
    
    public function __construct()
    {
        $this->db = Database::getInstance();
        $this->config = require __DIR__ . '/../../config/app.php';
    }

    /**
     * 記錄日誌
     * 
     * @param string $level
     * @param string $message
     * @param array $context
     */
    protected function log(string $level, string $message, array $context = []): void
    {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'level' => $level,
            'message' => $message,
            'context' => $context,
            'service' => static::class
        ];

        $logFile = $this->config['log']['path'] . '/service.log';
        $logDir = dirname($logFile);
        
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }

        file_put_contents($logFile, json_encode($logData, JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND | LOCK_EX);
    }

    /**
     * 驗證必要參數
     * 
     * @param array $data
     * @param array $required
     * @throws Exception
     */
    protected function validateRequired(array $data, array $required): void
    {
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new Exception("必要參數缺失: {$field}");
            }
        }
    }

    /**
     * 清理和驗證輸入資料
     * 
     * @param array $data
     * @return array
     */
    protected function sanitizeInput(array $data): array
    {
        $sanitized = [];
        
        foreach ($data as $key => $value) {
            if (is_string($value)) {
                $sanitized[$key] = trim(htmlspecialchars($value, ENT_QUOTES, 'UTF-8'));
            } elseif (is_array($value)) {
                $sanitized[$key] = $this->sanitizeInput($value);
            } else {
                $sanitized[$key] = $value;
            }
        }
        
        return $sanitized;
    }

    /**
     * 處理分頁參數
     * 
     * @param int $page
     * @param int $perPage
     * @return array
     */
    protected function processPagination(int $page, int $perPage): array
    {
        $page = max(1, $page);
        $perPage = min(max(1, $perPage), 100); // 限制最大每頁數量
        
        return [
            'page' => $page,
            'per_page' => $perPage,
            'offset' => ($page - 1) * $perPage
        ];
    }

    /**
     * 格式化API響應
     * 
     * @param bool $success
     * @param mixed $data
     * @param string|null $message
     * @param array $meta
     * @return array
     */
    protected function formatResponse(bool $success, $data = null, ?string $message = null, array $meta = []): array
    {
        $response = [
            'success' => $success,
            'timestamp' => date('c')
        ];
        
        if ($message !== null) {
            $response['message'] = $message;
        }
        
        if ($data !== null) {
            $response['data'] = $data;
        }
        
        if (!empty($meta)) {
            $response['meta'] = $meta;
        }
        
        return $response;
    }

    /**
     * 處理服務異常
     * 
     * @param Exception $e
     * @param string $operation
     * @return array
     */
    protected function handleException(Exception $e, string $operation): array
    {
        $this->log('error', "服務異常: {$operation}", [
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]);
        
        return $this->formatResponse(false, null, "操作失敗: {$operation}");
    }
}