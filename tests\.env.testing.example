# 測試環境配置範例
# 複製此文件為 .env.testing 並修改相應的值

# 資料庫配置
TEST_DB_HOST=localhost
TEST_DB_PORT=3306
TEST_DB_DATABASE=techvg_test
TEST_DB_USERNAME=test_user
TEST_DB_PASSWORD=test_password

# API 配置
TEST_API_BASE_URL=http://localhost:8000
TEST_API_KEY=test_key_123456

# 應用程式配置
TEST_APP_BASE_URL=http://localhost:8000
TEST_APP_DEBUG=true

# 快取配置
TEST_CACHE_DRIVER=file
TEST_CACHE_TTL=60

# 郵件配置（測試用）
TEST_MAIL_DRIVER=log
TEST_MAIL_FROM=<EMAIL>

# 檔案儲存配置
TEST_STORAGE_DRIVER=local
TEST_MAX_FILE_SIZE=10485760

# 外部服務配置（測試時通常停用）
TEST_EXTERNAL_API_ENABLED=false
TEST_EMAIL_ENABLED=false

# 效能測試配置
TEST_PERFORMANCE_MAX_RESPONSE_TIME=1000
TEST_PERFORMANCE_CONCURRENT_USERS=10
TEST_PERFORMANCE_REQUESTS_PER_USER=20

# 安全測試配置
TEST_SECURITY_XSS_ENABLED=true
TEST_SECURITY_SQL_INJECTION_ENABLED=true
TEST_SECURITY_CSRF_ENABLED=true

# 日誌配置
TEST_LOG_LEVEL=debug
TEST_LOG_CHANNEL=testing