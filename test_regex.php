<?php
/**
 * 正則表達式測試腳本
 */

// 測試正則表達式
$patterns = [
    '/api/' => '/^\/api\/$/',
    '/api/health' => '/^\/api\/health$/',
    '/' => '/^\/$/',
    '/home' => '/^\/home$/'
];

$testUris = ['/api/', '/api/health', '/', '/home', '/api'];

echo "=== 正則表達式測試 ===\n\n";

foreach ($patterns as $pattern => $regex) {
    echo "模式: {$pattern}\n";
    echo "正則: {$regex}\n";
    
    foreach ($testUris as $uri) {
        $result = @preg_match($regex, $uri);
        if ($result === false) {
            echo "  {$uri} -> ❌ 正則表達式錯誤\n";
        } elseif ($result === 1) {
            echo "  {$uri} -> ✅ 匹配\n";
        } else {
            echo "  {$uri} -> ❌ 不匹配\n";
        }
    }
    echo "\n";
}

// 測試Router的convertPatternToRegex方法
echo "=== Router 正則轉換測試 ===\n\n";

function convertPatternToRegex(string $pattern): string
{
    // 轉義特殊字符
    $pattern = preg_quote($pattern, '/');
    
    // 替換參數佔位符 - 修正字符類的轉義問題
    $pattern = preg_replace('/\\\\{([^}]+)\\\\}/', '([^\/]+)', $pattern);
    
    return '/^' . $pattern . '$/';
}

$routePatterns = ['/api/', '/api/health', '/api/documents/{id}', '/'];

foreach ($routePatterns as $routePattern) {
    $regex = convertPatternToRegex($routePattern);
    echo "路由模式: {$routePattern}\n";
    echo "轉換後正則: {$regex}\n";
    
    foreach ($testUris as $uri) {
        $result = @preg_match($regex, $uri);
        if ($result === false) {
            echo "  {$uri} -> ❌ 正則表達式錯誤: " . error_get_last()['message'] . "\n";
        } elseif ($result === 1) {
            echo "  {$uri} -> ✅ 匹配\n";
        } else {
            echo "  {$uri} -> ❌ 不匹配\n";
        }
    }
    echo "\n";
}
?>
