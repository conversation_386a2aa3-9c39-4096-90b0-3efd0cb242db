<?php
/**
 * Tech.VG API 入口點
 * 處理所有 /api/* 請求
 */

// 錯誤報告設定
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 設定時區
date_default_timezone_set('Asia/Taipei');

// 定義應用程式根目錄
define('APP_ROOT', dirname(__DIR__));

// 載入自動載入器
require_once __DIR__ . '/vendor/autoload.php';

// 載入環境變數 (如果存在 .env 文件)
if (file_exists(APP_ROOT . '/.env')) {
    $lines = file(APP_ROOT . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            [$key, $value] = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

// 設定 CORS 標頭
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-API-Key');

// 處理 OPTIONS 請求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // 載入配置
    $config = require APP_ROOT . '/config/app.php';
    
    // 設定錯誤處理
    if (!$config['debug']) {
        error_reporting(0);
        ini_set('display_errors', 0);
    }
    
    // 載入路由
    $router = require APP_ROOT . '/config/routes.php';
    
    // 取得當前 URI
    $requestUri = $_SERVER['REQUEST_URI'] ?? '/';
    $requestMethod = $_SERVER['REQUEST_METHOD'] ?? 'GET';

    // 移除查詢字串
    $uri = parse_url($requestUri, PHP_URL_PATH);

    // 處理 .htaccess 重寫的情況
    if ($uri === '/api.php') {
        $uri = '/api/';  // 添加尾隨斜線
    } elseif (strpos($uri, '/api.php/') === 0) {
        // 處理 /api.php/something 的情況
        $uri = '/api' . substr($uri, 8);
    } elseif (strpos($uri, '/api') !== 0) {
        // 確保 URI 以 /api 開頭
        $uri = '/api' . $uri;
    }
    
    // 分發請求
    $router->dispatch($requestMethod, $uri);
    
} catch (Exception $e) {
    // API 錯誤響應
    $errorCode = $e->getCode() ?: 500;
    http_response_code($errorCode);
    header('Content-Type: application/json; charset=utf-8');
    
    $response = [
        'success' => false,
        'message' => '伺服器內部錯誤',
        'timestamp' => date('c')
    ];
    
    if (isset($config) && $config['debug']) {
        $response['debug'] = [
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ];
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    
    // 記錄錯誤日誌
    if (isset($config)) {
        $logFile = $config['log']['path'] . '/api_error.log';
        $logDir = dirname($logFile);
        
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString(),
            'request_uri' => $_SERVER['REQUEST_URI'] ?? '',
            'request_method' => $_SERVER['REQUEST_METHOD'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'ip' => $_SERVER['REMOTE_ADDR'] ?? ''
        ];
        
        file_put_contents($logFile, json_encode($logData, JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND | LOCK_EX);
    }
}
?>