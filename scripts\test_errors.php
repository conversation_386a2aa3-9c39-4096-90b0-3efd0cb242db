<?php

/**
 * 錯誤頁面測試腳本
 * 用於測試各種錯誤頁面的顯示
 */

// 確保只能從命令行執行
if (php_sapi_name() !== 'cli') {
    die('此腳本只能從命令行執行');
}

// 設定應用程式根目錄
define('APP_ROOT', dirname(__DIR__));

echo "錯誤頁面測試工具\n";
echo "================\n\n";

/**
 * 測試錯誤頁面文件是否存在
 */
function testErrorPageFiles() {
    echo "1. 檢查錯誤頁面文件...\n";
    
    $errorPages = [
        '404' => APP_ROOT . '/app/View/errors/404.php',
        '403' => APP_ROOT . '/app/View/errors/403.php',
        '500' => APP_ROOT . '/app/View/errors/500.php',
        '503' => APP_ROOT . '/app/View/errors/503.php',
        'layout' => APP_ROOT . '/app/View/errors/layout.php',
        'logs' => APP_ROOT . '/app/View/errors/logs.php'
    ];
    
    foreach ($errorPages as $name => $path) {
        if (file_exists($path)) {
            echo "   ✓ {$name}.php 存在\n";
        } else {
            echo "   ✗ {$name}.php 不存在\n";
        }
    }
    
    echo "\n";
}

/**
 * 測試ErrorController是否存在
 */
function testErrorController() {
    echo "2. 檢查ErrorController...\n";
    
    $controllerPath = APP_ROOT . '/app/Controller/ErrorController.php';
    
    if (file_exists($controllerPath)) {
        echo "   ✓ ErrorController.php 存在\n";
        
        // 檢查類別是否可以載入
        require_once APP_ROOT . '/app/Controller/BaseController.php';
        require_once $controllerPath;
        
        if (class_exists('App\Controller\ErrorController')) {
            echo "   ✓ ErrorController 類別可以載入\n";
            
            $controller = new \App\Controller\ErrorController();
            
            $methods = ['notFound', 'forbidden', 'internalServerError', 'serviceUnavailable', 'handleError'];
            foreach ($methods as $method) {
                if (method_exists($controller, $method)) {
                    echo "   ✓ 方法 {$method} 存在\n";
                } else {
                    echo "   ✗ 方法 {$method} 不存在\n";
                }
            }
        } else {
            echo "   ✗ ErrorController 類別無法載入\n";
        }
    } else {
        echo "   ✗ ErrorController.php 不存在\n";
    }
    
    echo "\n";
}

/**
 * 測試維護模式功能
 */
function testMaintenanceMode() {
    echo "3. 檢查維護模式功能...\n";
    
    $maintenanceScript = APP_ROOT . '/scripts/maintenance.php';
    $maintenanceMiddleware = APP_ROOT . '/app/Middleware/MaintenanceMiddleware.php';
    
    if (file_exists($maintenanceScript)) {
        echo "   ✓ 維護模式腳本存在\n";
    } else {
        echo "   ✗ 維護模式腳本不存在\n";
    }
    
    if (file_exists($maintenanceMiddleware)) {
        echo "   ✓ 維護模式中間件存在\n";
    } else {
        echo "   ✗ 維護模式中間件不存在\n";
    }
    
    echo "\n";
}

/**
 * 測試日誌目錄
 */
function testLogDirectory() {
    echo "4. 檢查日誌目錄...\n";
    
    $logDir = APP_ROOT . '/storage/logs';
    
    if (!is_dir($logDir)) {
        if (mkdir($logDir, 0755, true)) {
            echo "   ✓ 日誌目錄已建立\n";
        } else {
            echo "   ✗ 無法建立日誌目錄\n";
        }
    } else {
        echo "   ✓ 日誌目錄存在\n";
    }
    
    // 檢查寫入權限
    $testFile = $logDir . '/test.log';
    if (file_put_contents($testFile, 'test') !== false) {
        echo "   ✓ 日誌目錄可寫入\n";
        unlink($testFile);
    } else {
        echo "   ✗ 日誌目錄無法寫入\n";
    }
    
    echo "\n";
}

/**
 * 測試路由配置
 */
function testRoutes() {
    echo "5. 檢查路由配置...\n";
    
    $routesPath = APP_ROOT . '/config/routes.php';
    
    if (file_exists($routesPath)) {
        $content = file_get_contents($routesPath);
        
        $routes = [
            '/error/404' => 'ErrorController@notFound',
            '/error/403' => 'ErrorController@forbidden',
            '/error/500' => 'ErrorController@internalServerError',
            '/error/503' => 'ErrorController@serviceUnavailable',
            '/error/logs' => 'ErrorController@logs'
        ];
        
        foreach ($routes as $route => $handler) {
            if (strpos($content, $route) !== false && strpos($content, $handler) !== false) {
                echo "   ✓ 路由 {$route} 已配置\n";
            } else {
                echo "   ✗ 路由 {$route} 未配置\n";
            }
        }
    } else {
        echo "   ✗ 路由配置文件不存在\n";
    }
    
    echo "\n";
}

/**
 * 生成測試錯誤日誌
 */
function generateTestLogs() {
    echo "6. 生成測試錯誤日誌...\n";
    
    $logFile = APP_ROOT . '/storage/logs/error.log';
    $logDir = dirname($logFile);
    
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $testLogs = [
        [
            'timestamp' => date('Y-m-d H:i:s'),
            'level' => 'error',
            'message' => '測試錯誤訊息',
            'file' => '/test/file.php',
            'line' => 123,
            'request_uri' => '/test',
            'request_method' => 'GET'
        ],
        [
            'timestamp' => date('Y-m-d H:i:s', strtotime('-1 hour')),
            'level' => 'warning',
            'message' => '測試警告訊息',
            'context' => ['test' => 'data']
        ],
        [
            'timestamp' => date('Y-m-d H:i:s', strtotime('-2 hours')),
            'level' => 'info',
            'message' => '測試資訊訊息'
        ]
    ];
    
    foreach ($testLogs as $log) {
        file_put_contents($logFile, json_encode($log, JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND | LOCK_EX);
    }
    
    echo "   ✓ 測試日誌已生成\n\n";
}

/**
 * 顯示測試結果摘要
 */
function showSummary() {
    echo "測試完成！\n";
    echo "===========\n\n";
    echo "您可以通過以下方式測試錯誤頁面：\n\n";
    echo "1. 訪問不存在的頁面測試404錯誤\n";
    echo "2. 訪問 /error/404 查看404錯誤頁面\n";
    echo "3. 訪問 /error/403 查看403錯誤頁面\n";
    echo "4. 訪問 /error/500 查看500錯誤頁面\n";
    echo "5. 訪問 /error/503 查看503錯誤頁面\n";
    echo "6. 訪問 /error/logs 查看錯誤日誌（僅開發模式）\n\n";
    echo "維護模式命令：\n";
    echo "- php scripts/maintenance.php up    # 啟用維護模式\n";
    echo "- php scripts/maintenance.php down  # 停用維護模式\n";
    echo "- php scripts/maintenance.php status # 查看狀態\n\n";
}

// 執行測試
testErrorPageFiles();
testErrorController();
testMaintenanceMode();
testLogDirectory();
testRoutes();
generateTestLogs();
showSummary();