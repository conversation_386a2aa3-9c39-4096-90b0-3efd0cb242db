<?php
/**
 * 資料庫連接診斷腳本 - 正式環境版本
 */

// 載入環境變數
if (file_exists('.env')) {
    $lines = file('.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        $line = trim($line);
        if (!empty($line) && strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            [$key, $value] = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

$host = $_ENV['DB_HOST'] ?? 'localhost';
$port = $_ENV['DB_PORT'] ?? '3306';
$database = $_ENV['DB_DATABASE'] ?? 'techvg';
$username = $_ENV['DB_USERNAME'] ?? 'root';
$password = $_ENV['DB_PASSWORD'] ?? '';

echo "=== 資料庫連接診斷 ===\n";
echo "時間: " . date('Y-m-d H:i:s') . "\n";
echo "主機: {$host}:{$port}\n";
echo "資料庫: {$database}\n";
echo "用戶: {$username}\n\n";

// 1. 檢查伺服器IP
echo "1. 檢查伺服器IP地址:\n";
$serverIp = $_SERVER['SERVER_ADDR'] ?? 'unknown';
echo "   伺服器IP: {$serverIp}\n";

// 獲取外部IP
$externalIp = @file_get_contents('http://ipinfo.io/ip');
if ($externalIp) {
    echo "   外部IP: " . trim($externalIp) . "\n";
} else {
    echo "   外部IP: 無法獲取\n";
}
echo "\n";

// 2. DNS解析測試
echo "2. DNS解析測試:\n";
$ip = gethostbyname($host);
if ($ip !== $host) {
    echo "   ✅ DNS解析成功: {$host} -> {$ip}\n";
} else {
    echo "   ❌ DNS解析失敗: {$host}\n";
}
echo "\n";

// 3. 網路連接測試
echo "3. 網路連接測試:\n";
$startTime = microtime(true);
$socket = @fsockopen($host, $port, $errno, $errstr, 30);
$endTime = microtime(true);
$responseTime = round(($endTime - $startTime) * 1000, 2);

if ($socket) {
    echo "   ✅ 網路連接成功！\n";
    echo "   響應時間: {$responseTime}ms\n";
    fclose($socket);
} else {
    echo "   ❌ 網路連接失敗: {$errstr} (錯誤代碼: {$errno})\n";
    echo "   響應時間: {$responseTime}ms\n";
}
echo "\n";

// 4. 資料庫連接測試
echo "4. 資料庫連接測試:\n";
try {
    $dsn = "mysql:host={$host};port={$port};dbname={$database};charset=utf8mb4";
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
        PDO::ATTR_TIMEOUT => 30,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
    ];
    
    $startTime = microtime(true);
    $pdo = new PDO($dsn, $username, $password, $options);
    $endTime = microtime(true);
    $dbResponseTime = round(($endTime - $startTime) * 1000, 2);
    
    echo "   ✅ 資料庫連接成功！\n";
    echo "   連接時間: {$dbResponseTime}ms\n";
    
    // 測試查詢
    $stmt = $pdo->query("SELECT VERSION() as version");
    $result = $stmt->fetch();
    echo "   MySQL版本: {$result['version']}\n";
    
    // 測試資料表
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM document");
        $result = $stmt->fetch();
        echo "   ✅ 資料表測試成功！文檔表有 {$result['count']} 筆記錄\n";
    } catch (PDOException $e) {
        echo "   ⚠️  資料表測試失敗: " . $e->getMessage() . "\n";
    }
    
} catch (PDOException $e) {
    echo "   ❌ 資料庫連接失敗: " . $e->getMessage() . "\n";
    echo "   錯誤代碼: " . $e->getCode() . "\n";
}

echo "\n=== 診斷完成 ===\n";

// 5. 建議解決方案
if ($socket === false) {
    echo "\n=== 建議解決方案 ===\n";
    echo "1. 聯繫主機商確認是否允許外部資料庫連接\n";
    echo "2. 檢查資料庫主機是否將您的IP加入白名單\n";
    echo "3. 確認防火牆設定允許端口3306的出站連接\n";
    echo "4. 嘗試使用不同的連接端口（如果資料庫提供商支援）\n";
    echo "5. 考慮使用SSH隧道或VPN連接\n";
}
?>
