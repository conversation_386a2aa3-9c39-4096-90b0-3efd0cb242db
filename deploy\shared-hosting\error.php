<?php
/**
 * 共享主機錯誤處理頁面
 */

$errorCode = $_GET['code'] ?? '500';
$errorMessages = [
    '403' => '禁止存取',
    '404' => '頁面不存在',
    '500' => '伺服器內部錯誤'
];

$errorMessage = $errorMessages[$errorCode] ?? '未知錯誤';

// 設定 HTTP 狀態碼
http_response_code((int)$errorCode);

// 檢查是否為 API 請求
$isApiRequest = strpos($_SERVER['REQUEST_URI'] ?? '', '/api/') === 0 || 
                strpos($_SERVER['HTTP_ACCEPT'] ?? '', 'application/json') !== false;

if ($isApiRequest) {
    // API 錯誤響應
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => $errorMessage,
        'error_code' => $errorCode,
        'timestamp' => date('c')
    ], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    exit;
}

// HTML 錯誤頁面
?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>錯誤 <?= htmlspecialchars($errorCode) ?> - Tech.VG</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        .error-container {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 3rem;
            text-align: center;
            max-width: 500px;
            margin: 0 auto;
        }
        .error-code {
            font-size: 6rem;
            font-weight: 700;
            color: #dc3545;
            margin-bottom: 1rem;
        }
        .error-message {
            font-size: 1.5rem;
            color: #6c757d;
            margin-bottom: 2rem;
        }
        .error-description {
            color: #6c757d;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="error-container">
            <div class="error-code"><?= htmlspecialchars($errorCode) ?></div>
            <div class="error-message"><?= htmlspecialchars($errorMessage) ?></div>
            
            <?php if ($errorCode === '404'): ?>
                <div class="error-description">
                    您要尋找的頁面可能已被移動、刪除或不存在。
                </div>
            <?php elseif ($errorCode === '403'): ?>
                <div class="error-description">
                    您沒有權限存取此資源。
                </div>
            <?php elseif ($errorCode === '500'): ?>
                <div class="error-description">
                    伺服器發生內部錯誤，請稍後再試。
                </div>
            <?php endif; ?>
            
            <div class="d-flex gap-2 justify-content-center">
                <a href="/" class="btn btn-primary">
                    <i class="fas fa-home"></i> 回到首頁
                </a>
                <a href="/api/docs?format=html" class="btn btn-outline-primary">
                    <i class="fas fa-book"></i> API 文檔
                </a>
            </div>
        </div>
    </div>
</body>
</html>