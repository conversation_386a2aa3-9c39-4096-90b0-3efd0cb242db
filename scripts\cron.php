#!/usr/bin/env php
<?php

/**
 * 定時任務執行器
 * 用於執行排程任務，可通過cron定時執行
 */

// 設定錯誤報告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 設定時區
date_default_timezone_set('Asia/Taipei');

// 定義應用程式根目錄
define('APP_ROOT', dirname(__DIR__));

// 載入自動載入器
require_once APP_ROOT . '/htdocs/vendor/autoload.php';

// 載入環境變數
if (file_exists(APP_ROOT . '/.env')) {
    $lines = file(APP_ROOT . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            [$key, $value] = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

use App\Service\ScheduleService;

try {
    echo "開始執行定時任務...\n";
    echo "執行時間: " . date('Y-m-d H:i:s') . "\n";
    echo str_repeat('-', 50) . "\n";

    $scheduleService = new ScheduleService();
    
    // 取得命令行參數
    $task = $argv[1] ?? 'all';
    
    switch ($task) {
        case 'news':
            echo "執行新聞抓取任務...\n";
            $result = $scheduleService->newsCrawl();
            break;
            
        case 'index':
            echo "執行文檔索引任務...\n";
            $result = $scheduleService->indexDocuments();
            break;
            
        case 'cleanup':
            echo "執行清理任務...\n";
            $result = $scheduleService->cleanupOldNews();
            break;
            
        case 'optimize':
            echo "執行索引優化任務...\n";
            $result = $scheduleService->optimizeIndex();
            break;
            
        case 'all':
        default:
            echo "執行所有排程任務...\n";
            $result = $scheduleService->runScheduledTasks();
            break;
    }
    
    // 輸出結果
    if ($result['success']) {
        echo "✓ 任務執行成功\n";
        
        if (isset($result['data'])) {
            $data = $result['data'];
            
            if (isset($data['total_tasks'])) {
                echo "總任務數: {$data['total_tasks']}\n";
                echo "成功: {$data['successful_tasks']}\n";
                echo "失敗: {$data['failed_tasks']}\n";
            }
            
            if (isset($data['new_articles'])) {
                echo "新文章: {$data['new_articles']}\n";
                echo "更新文章: {$data['updated_articles']}\n";
            }
            
            if (isset($data['success'])) {
                echo "成功處理: {$data['success']}\n";
            }
        }
    } else {
        echo "✗ 任務執行失敗\n";
        echo "錯誤: " . ($result['message'] ?? '未知錯誤') . "\n";
        exit(1);
    }
    
    echo str_repeat('-', 50) . "\n";
    echo "任務完成時間: " . date('Y-m-d H:i:s') . "\n";
    
} catch (Exception $e) {
    echo "✗ 執行異常: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行號: " . $e->getLine() . "\n";
    exit(1);
}

echo "\n使用方法:\n";
echo "php cron.php [task]\n";
echo "可用任務:\n";
echo "  all      - 執行所有任務 (預設)\n";
echo "  news     - 新聞抓取\n";
echo "  index    - 文檔索引\n";
echo "  cleanup  - 清理舊資料\n";
echo "  optimize - 優化索引\n";