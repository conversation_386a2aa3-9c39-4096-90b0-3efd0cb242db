<?php

namespace App\Service;

/**
 * API 代碼範例生成器
 * 為不同程式語言生成 API 呼叫範例
 */
class ApiCodeGenerator
{
    /**
     * 生成代碼範例
     * 
     * @param string $language 程式語言 (curl, javascript, python, php, java)
     * @param string $method HTTP 方法
     * @param string $url API URL
     * @param array $params 參數
     * @param array $headers 標頭
     * @param string $body 請求主體
     * @return string
     */
    public function generateCode(
        string $language, 
        string $method, 
        string $url, 
        array $params = [], 
        array $headers = [], 
        string $body = ''
    ): string {
        switch (strtolower($language)) {
            case 'curl':
                return $this->generateCurl($method, $url, $params, $headers, $body);
            case 'javascript':
                return $this->generateJavaScript($method, $url, $params, $headers, $body);
            case 'python':
                return $this->generatePython($method, $url, $params, $headers, $body);
            case 'php':
                return $this->generatePhp($method, $url, $params, $headers, $body);
            case 'java':
                return $this->generateJava($method, $url, $params, $headers, $body);
            case 'csharp':
                return $this->generateCSharp($method, $url, $params, $headers, $body);
            default:
                throw new \InvalidArgumentException("不支援的程式語言: {$language}");
        }
    }

    /**
     * 生成 cURL 範例
     */
    private function generateCurl(string $method, string $url, array $params, array $headers, string $body): string
    {
        $curl = "curl -X {$method}";
        
        // 添加 URL 和參數
        if ($method === 'GET' && !empty($params)) {
            $queryString = http_build_query($params);
            $url .= (strpos($url, '?') !== false ? '&' : '?') . $queryString;
        }
        
        $curl .= " \"{$url}\"";
        
        // 添加標頭
        foreach ($headers as $name => $value) {
            $curl .= " \\\n  -H \"{$name}: {$value}\"";
        }
        
        // 添加請求主體
        if ($method !== 'GET' && !empty($body)) {
            $curl .= " \\\n  -d '{$body}'";
        } elseif ($method !== 'GET' && !empty($params)) {
            $data = http_build_query($params);
            $curl .= " \\\n  -d \"{$data}\"";
        }
        
        return $curl;
    }

    /**
     * 生成 JavaScript 範例
     */
    private function generateJavaScript(string $method, string $url, array $params, array $headers, string $body): string
    {
        $code = "// 使用 fetch API\n";
        
        // 處理 URL 和參數
        if ($method === 'GET' && !empty($params)) {
            $code .= "const params = new URLSearchParams({\n";
            foreach ($params as $key => $value) {
                $code .= "  '{$key}': '{$value}',\n";
            }
            $code = rtrim($code, ",\n") . "\n";
            $code .= "});\n\n";
            $code .= "const url = '{$url}?' + params.toString();\n\n";
        } else {
            $code .= "const url = '{$url}';\n\n";
        }
        
        // 設定選項
        $code .= "const options = {\n";
        $code .= "  method: '{$method}',\n";
        
        // 添加標頭
        if (!empty($headers) || ($method !== 'GET' && !empty($body))) {
            $code .= "  headers: {\n";
            if ($method !== 'GET' && !empty($body) && !isset($headers['Content-Type'])) {
                $code .= "    'Content-Type': 'application/json',\n";
            }
            foreach ($headers as $name => $value) {
                $code .= "    '{$name}': '{$value}',\n";
            }
            $code = rtrim($code, ",\n") . "\n";
            $code .= "  },\n";
        }
        
        // 添加請求主體
        if ($method !== 'GET') {
            if (!empty($body)) {
                $code .= "  body: JSON.stringify({$body}),\n";
            } elseif (!empty($params)) {
                $code .= "  body: JSON.stringify({\n";
                foreach ($params as $key => $value) {
                    $code .= "    '{$key}': '{$value}',\n";
                }
                $code = rtrim($code, ",\n") . "\n";
                $code .= "  }),\n";
            }
        }
        
        $code = rtrim($code, ",\n") . "\n";
        $code .= "};\n\n";
        
        // 發送請求
        $code .= "fetch(url, options)\n";
        $code .= "  .then(response => response.json())\n";
        $code .= "  .then(data => {\n";
        $code .= "    console.log('Success:', data);\n";
        $code .= "  })\n";
        $code .= "  .catch(error => {\n";
        $code .= "    console.error('Error:', error);\n";
        $code .= "  });";
        
        return $code;
    }

    /**
     * 生成 Python 範例
     */
    private function generatePython(string $method, string $url, array $params, array $headers, string $body): string
    {
        $code = "import requests\nimport json\n\n";
        
        // URL
        $code .= "url = '{$url}'\n";
        
        // 參數
        if (!empty($params)) {
            $code .= "params = {\n";
            foreach ($params as $key => $value) {
                $code .= "    '{$key}': '{$value}',\n";
            }
            $code = rtrim($code, ",\n") . "\n";
            $code .= "}\n";
        }
        
        // 標頭
        if (!empty($headers) || ($method !== 'GET' && !empty($body))) {
            $code .= "headers = {\n";
            if ($method !== 'GET' && !empty($body) && !isset($headers['Content-Type'])) {
                $code .= "    'Content-Type': 'application/json',\n";
            }
            foreach ($headers as $name => $value) {
                $code .= "    '{$name}': '{$value}',\n";
            }
            $code = rtrim($code, ",\n") . "\n";
            $code .= "}\n";
        }
        
        // 請求主體
        if ($method !== 'GET' && !empty($body)) {
            $code .= "data = {$body}\n";
        }
        
        $code .= "\n";
        
        // 發送請求
        $requestArgs = ["url"];
        
        if (!empty($params) && $method === 'GET') {
            $requestArgs[] = "params=params";
        }
        
        if (!empty($headers)) {
            $requestArgs[] = "headers=headers";
        }
        
        if ($method !== 'GET') {
            if (!empty($body)) {
                $requestArgs[] = "json=data";
            } elseif (!empty($params)) {
                $requestArgs[] = "data=params";
            }
        }
        
        $code .= "response = requests.{$method}(" . implode(', ', $requestArgs) . ")\n\n";
        $code .= "if response.status_code == 200:\n";
        $code .= "    data = response.json()\n";
        $code .= "    print('Success:', data)\n";
        $code .= "else:\n";
        $code .= "    print('Error:', response.status_code, response.text)";
        
        return strtolower($code);
    }

    /**
     * 生成 PHP 範例
     */
    private function generatePhp(string $method, string $url, array $params, array $headers, string $body): string
    {
        $code = "<?php\n\n";
        
        // URL
        $code .= "\$url = '{$url}';\n";
        
        // 參數
        if (!empty($params)) {
            $code .= "\$params = [\n";
            foreach ($params as $key => $value) {
                $code .= "    '{$key}' => '{$value}',\n";
            }
            $code = rtrim($code, ",\n") . "\n";
            $code .= "];\n";
        }
        
        // 使用 cURL
        $code .= "\n\$ch = curl_init();\n\n";
        
        // 設定 cURL 選項
        $code .= "curl_setopt_array(\$ch, [\n";
        $code .= "    CURLOPT_URL => \$url,\n";
        $code .= "    CURLOPT_RETURNTRANSFER => true,\n";
        $code .= "    CURLOPT_CUSTOMREQUEST => '{$method}',\n";
        
        // 標頭
        if (!empty($headers) || ($method !== 'GET' && !empty($body))) {
            $code .= "    CURLOPT_HTTPHEADER => [\n";
            if ($method !== 'GET' && !empty($body) && !isset($headers['Content-Type'])) {
                $code .= "        'Content-Type: application/json',\n";
            }
            foreach ($headers as $name => $value) {
                $code .= "        '{$name}: {$value}',\n";
            }
            $code = rtrim($code, ",\n") . "\n";
            $code .= "    ],\n";
        }
        
        // 請求主體
        if ($method !== 'GET') {
            if (!empty($body)) {
                $code .= "    CURLOPT_POSTFIELDS => json_encode({$body}),\n";
            } elseif (!empty($params)) {
                $code .= "    CURLOPT_POSTFIELDS => http_build_query(\$params),\n";
            }
        } elseif (!empty($params)) {
            $code .= "    CURLOPT_URL => \$url . '?' . http_build_query(\$params),\n";
        }
        
        $code = rtrim($code, ",\n") . "\n";
        $code .= "]);\n\n";
        
        // 執行請求
        $code .= "\$response = curl_exec(\$ch);\n";
        $code .= "\$httpCode = curl_getinfo(\$ch, CURLINFO_HTTP_CODE);\n";
        $code .= "curl_close(\$ch);\n\n";
        
        // 處理響應
        $code .= "if (\$httpCode === 200) {\n";
        $code .= "    \$data = json_decode(\$response, true);\n";
        $code .= "    echo 'Success: ' . print_r(\$data, true);\n";
        $code .= "} else {\n";
        $code .= "    echo 'Error: ' . \$httpCode . ' ' . \$response;\n";
        $code .= "}\n\n";
        $code .= "?>";
        
        return $code;
    }

    /**
     * 生成 Java 範例
     */
    private function generateJava(string $method, string $url, array $params, array $headers, string $body): string
    {
        $code = "import java.net.http.*;\nimport java.net.URI;\nimport java.time.Duration;\n\n";
        $code .= "public class ApiExample {\n";
        $code .= "    public static void main(String[] args) {\n";
        $code .= "        try {\n";
        
        // URL
        if ($method === 'GET' && !empty($params)) {
            $code .= "            String url = \"{$url}?\";\n";
            foreach ($params as $key => $value) {
                $code .= "            url += \"{$key}={$value}&\";\n";
            }
            $code .= "            url = url.substring(0, url.length() - 1);\n";
        } else {
            $code .= "            String url = \"{$url}\";\n";
        }
        
        // 建立 HTTP 客戶端
        $code .= "\n            HttpClient client = HttpClient.newBuilder()\n";
        $code .= "                .timeout(Duration.ofSeconds(30))\n";
        $code .= "                .build();\n\n";
        
        // 建立請求
        $code .= "            HttpRequest.Builder requestBuilder = HttpRequest.newBuilder()\n";
        $code .= "                .uri(URI.create(url))\n";
        $code .= "                .timeout(Duration.ofSeconds(30));\n\n";
        
        // 設定方法和主體
        if ($method === 'GET') {
            $code .= "            requestBuilder.GET();\n";
        } else {
            if (!empty($body)) {
                $code .= "            String requestBody = \"{$body}\";\n";
                $code .= "            requestBuilder.{$method}(HttpRequest.BodyPublishers.ofString(requestBody));\n";
            } elseif (!empty($params)) {
                $code .= "            String requestBody = \"\";\n";
                foreach ($params as $key => $value) {
                    $code .= "            requestBody += \"{$key}={$value}&\";\n";
                }
                $code .= "            requestBody = requestBody.substring(0, requestBody.length() - 1);\n";
                $code .= "            requestBuilder.{$method}(HttpRequest.BodyPublishers.ofString(requestBody));\n";
            } else {
                $code .= "            requestBuilder.{$method}(HttpRequest.BodyPublishers.noBody());\n";
            }
        }
        
        // 添加標頭
        if (!empty($headers) || ($method !== 'GET' && !empty($body))) {
            if ($method !== 'GET' && !empty($body) && !isset($headers['Content-Type'])) {
                $code .= "            requestBuilder.header(\"Content-Type\", \"application/json\");\n";
            }
            foreach ($headers as $name => $value) {
                $code .= "            requestBuilder.header(\"{$name}\", \"{$value}\");\n";
            }
        }
        
        $code .= "\n            HttpRequest request = requestBuilder.build();\n\n";
        
        // 發送請求
        $code .= "            HttpResponse<String> response = client.send(request,\n";
        $code .= "                HttpResponse.BodyHandlers.ofString());\n\n";
        
        // 處理響應
        $code .= "            if (response.statusCode() == 200) {\n";
        $code .= "                System.out.println(\"Success: \" + response.body());\n";
        $code .= "            } else {\n";
        $code .= "                System.out.println(\"Error: \" + response.statusCode() + \" \" + response.body());\n";
        $code .= "            }\n\n";
        
        $code .= "        } catch (Exception e) {\n";
        $code .= "            e.printStackTrace();\n";
        $code .= "        }\n";
        $code .= "    }\n";
        $code .= "}";
        
        return $code;
    }

    /**
     * 生成 C# 範例
     */
    private function generateCSharp(string $method, string $url, array $params, array $headers, string $body): string
    {
        $code = "using System;\nusing System.Net.Http;\nusing System.Text;\nusing System.Threading.Tasks;\n\n";
        $code .= "class Program\n{\n";
        $code .= "    static async Task Main(string[] args)\n    {\n";
        $code .= "        using (var client = new HttpClient())\n        {\n";
        $code .= "            try\n            {\n";
        
        // URL
        if ($method === 'GET' && !empty($params)) {
            $code .= "                string url = \"{$url}?\";\n";
            foreach ($params as $key => $value) {
                $code .= "                url += \"{$key}={$value}&\";\n";
            }
            $code .= "                url = url.TrimEnd('&');\n";
        } else {
            $code .= "                string url = \"{$url}\";\n";
        }
        
        // 添加標頭
        if (!empty($headers)) {
            foreach ($headers as $name => $value) {
                $code .= "                client.DefaultRequestHeaders.Add(\"{$name}\", \"{$value}\");\n";
            }
        }
        
        $code .= "\n";
        
        // 發送請求
        if ($method === 'GET') {
            $code .= "                HttpResponseMessage response = await client.GetAsync(url);\n";
        } else {
            if (!empty($body)) {
                $code .= "                string json = \"{$body}\";\n";
                $code .= "                StringContent content = new StringContent(json, Encoding.UTF8, \"application/json\");\n";
                $code .= "                HttpResponseMessage response = await client.{$method}Async(url, content);\n";
            } elseif (!empty($params)) {
                $code .= "                string formData = \"\";\n";
                foreach ($params as $key => $value) {
                    $code .= "                formData += \"{$key}={$value}&\";\n";
                }
                $code .= "                formData = formData.TrimEnd('&');\n";
                $code .= "                StringContent content = new StringContent(formData, Encoding.UTF8, \"application/x-www-form-urlencoded\");\n";
                $code .= "                HttpResponseMessage response = await client.{$method}Async(url, content);\n";
            } else {
                $code .= "                HttpResponseMessage response = await client.{$method}Async(url);\n";
            }
        }
        
        // 處理響應
        $code .= "\n                if (response.IsSuccessStatusCode)\n                {\n";
        $code .= "                    string responseBody = await response.Content.ReadAsStringAsync();\n";
        $code .= "                    Console.WriteLine(\"Success: \" + responseBody);\n";
        $code .= "                }\n                else\n                {\n";
        $code .= "                    Console.WriteLine(\"Error: \" + response.StatusCode + \" \" + response.ReasonPhrase);\n";
        $code .= "                }\n";
        
        $code .= "            }\n            catch (Exception ex)\n            {\n";
        $code .= "                Console.WriteLine(\"Exception: \" + ex.Message);\n";
        $code .= "            }\n";
        $code .= "        }\n    }\n}";
        
        return $code;
    }

    /**
     * 取得支援的程式語言列表
     * 
     * @return array
     */
    public function getSupportedLanguages(): array
    {
        return [
            'curl' => 'cURL',
            'javascript' => 'JavaScript',
            'python' => 'Python',
            'php' => 'PHP',
            'java' => 'Java',
            'csharp' => 'C#'
        ];
    }
}