<?php

namespace App\Model;

/**
 * 新聞來源模型
 * 處理新聞來源相關的資料操作
 */
class NewsSourceModel extends BaseModel
{
    protected string $table = 'newsSource';
    
    protected array $fillable = [
        'name',
        'url',
        'rssUrl',
        'category',
        'language',
        'isActive',
        'crawlInterval',
        'lastCrawlAt'
    ];
    
    protected array $casts = [
        'id' => 'int',
        'isActive' => 'bool',
        'crawlInterval' => 'int',
        'lastCrawlAt' => 'datetime',
        'createdAt' => 'datetime',
        'updatedAt' => 'datetime'
    ];

    /**
     * 取得活躍的新聞源
     * 
     * @return array
     */
    public function getActiveSources(): array
    {
        return $this->findAll(['isActive' => 1], 'name ASC');
    }

    /**
     * 根據分類取得新聞源
     * 
     * @param string $category
     * @return array
     */
    public function getByCategory(string $category): array
    {
        return $this->findAll(['category' => $category, 'isActive' => 1], 'name ASC');
    }

    /**
     * 取得需要抓取的新聞源
     * 
     * @return array
     */
    public function getSourcesForCrawling(): array
    {
        $sql = "
            SELECT * FROM {$this->table}
            WHERE isActive = 1
            AND (
                lastCrawlAt IS NULL 
                OR lastCrawlAt < DATE_SUB(NOW(), INTERVAL crawlInterval SECOND)
            )
            ORDER BY lastCrawlAt ASC
        ";
        
        return $this->db->fetchAll($sql);
    }

    /**
     * 更新最後抓取時間
     * 
     * @param int $sourceId
     * @return int
     */
    public function updateLastCrawlTime(int $sourceId): int
    {
        return $this->update($sourceId, ['lastCrawlAt' => date('Y-m-d H:i:s')]);
    }

    /**
     * 啟用/停用新聞源
     * 
     * @param int $sourceId
     * @param bool $isActive
     * @return int
     */
    public function setActive(int $sourceId, bool $isActive): int
    {
        return $this->update($sourceId, ['isActive' => $isActive]);
    }

    /**
     * 取得新聞源統計
     * 
     * @return array
     */
    public function getSourceStatistics(): array
    {
        $sql = "
            SELECT 
                COUNT(*) as total_sources,
                COUNT(CASE WHEN isActive = 1 THEN 1 END) as active_sources,
                COUNT(CASE WHEN lastCrawlAt IS NOT NULL THEN 1 END) as crawled_sources,
                AVG(crawlInterval) as avg_crawl_interval
            FROM {$this->table}
        ";
        
        return $this->db->fetchOne($sql) ?: [];
    }
}