<?php

namespace App\Controller\Api\V1;

use App\Controller\Api\BaseApiController;

/**
 * API v1 基礎控制器
 * 為 API v1 版本提供特定功能
 */
abstract class BaseV1Controller extends BaseApiController
{
    /**
     * API 版本
     */
    protected string $apiVersion = 'v1';

    /**
     * 版本特定的功能標誌
     */
    protected array $features = [
        'pagination' => true,
        'filtering' => true,
        'sorting' => true,
        'rate_limiting' => true,
        'caching' => true,
        'compression' => false,
        'webhooks' => false,
        'search_suggestions' => true,
        'advanced_search' => true,
        'batch_operations' => true,
        'real_time_stats' => true,
        'file_upload' => true,
        'content_analysis' => true,
        'code_generation' => true
    ];

    public function __construct()
    {
        parent::__construct();
        
        // 設定版本特定的標頭
        header('X-API-Version: ' . $this->apiVersion);
        header('X-API-Features: ' . implode(',', array_keys(array_filter($this->features))));
    }

    /**
     * 檢查功能是否啟用
     * 
     * @param string $feature
     * @return bool
     */
    protected function isFeatureEnabled(string $feature): bool
    {
        return $this->features[$feature] ?? false;
    }

    /**
     * 版本特定的錯誤響應
     * 
     * @param string $message
     * @param int $statusCode
     * @param array $errors
     * @param string|null $errorCode
     */
    protected function errorResponse(string $message, int $statusCode = 400, array $errors = [], string $errorCode = null): void
    {
        $response = [
            'success' => false,
            'message' => $message,
            'timestamp' => date('c'),
            'api_version' => $this->apiVersion
        ];

        if (!empty($errors)) {
            $response['errors'] = $errors;
        }

        if ($errorCode) {
            $response['error_code'] = $errorCode;
        }

        // v1 特定的錯誤格式
        $response['error_details'] = [
            'type' => $this->getErrorType($statusCode),
            'documentation_url' => $this->getBaseUrl() . '/api/docs',
            'support_url' => $this->getBaseUrl() . '/contact'
        ];

        $this->jsonResponse($response, $statusCode);
    }

    /**
     * 版本特定的成功響應
     * 
     * @param mixed $data
     * @param string $message
     * @param int $statusCode
     * @param array $meta
     */
    protected function successResponse($data = null, string $message = 'Success', int $statusCode = 200, array $meta = []): void
    {
        $response = [
            'success' => true,
            'message' => $message,
            'data' => $data,
            'timestamp' => date('c'),
            'api_version' => $this->apiVersion
        ];

        if (!empty($meta)) {
            $response['meta'] = $meta;
        }

        // v1 特定的響應格式
        $response['_links'] = [
            'self' => $_SERVER['REQUEST_URI'] ?? '',
            'documentation' => $this->getBaseUrl() . '/api/docs'
        ];

        $this->jsonResponse($response, $statusCode);
    }

    /**
     * 取得錯誤類型
     * 
     * @param int $statusCode
     * @return string
     */
    private function getErrorType(int $statusCode): string
    {
        $errorTypes = [
            400 => 'bad_request',
            401 => 'unauthorized',
            403 => 'forbidden',
            404 => 'not_found',
            405 => 'method_not_allowed',
            409 => 'conflict',
            422 => 'validation_error',
            429 => 'rate_limit_exceeded',
            500 => 'internal_server_error',
            502 => 'bad_gateway',
            503 => 'service_unavailable'
        ];

        return $errorTypes[$statusCode] ?? 'unknown_error';
    }

    /**
     * 取得版本資訊
     * 
     * @return array
     */
    public function getVersionInfo(): array
    {
        return [
            'version' => $this->apiVersion,
            'release_date' => '2024-01-15',
            'status' => 'stable',
            'features' => $this->features,
            'deprecation_date' => null,
            'end_of_life_date' => null,
            'documentation' => $this->getBaseUrl() . '/api/docs',
            'changelog' => $this->getBaseUrl() . '/api/v1/changelog',
            'migration_guide' => $this->getBaseUrl() . '/api/v1/migration'
        ];
    }

    /**
     * 生成請求 ID
     * 
     * @return string
     */
    protected function generateRequestId(): string
    {
        return 'req_v1_' . uniqid() . '_' . substr(md5(microtime()), 0, 8);
    }

    /**
     * 版本特定的分頁處理
     * 
     * @param array $data
     * @param int $total
     * @param int $page
     * @param int $perPage
     * @return array
     */
    protected function paginateResponse(array $data, int $total, int $page, int $perPage): array
    {
        $totalPages = ceil($total / $perPage);
        
        return [
            'data' => $data,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total_items' => $total,
                'total_pages' => $totalPages,
                'has_next_page' => $page < $totalPages,
                'has_prev_page' => $page > 1,
                'next_page_url' => $page < $totalPages ? $this->buildPageUrl($page + 1) : null,
                'prev_page_url' => $page > 1 ? $this->buildPageUrl($page - 1) : null,
                'first_page_url' => $this->buildPageUrl(1),
                'last_page_url' => $this->buildPageUrl($totalPages)
            ]
        ];
    }

    /**
     * 建立分頁 URL
     * 
     * @param int $page
     * @return string
     */
    private function buildPageUrl(int $page): string
    {
        $url = $_SERVER['REQUEST_URI'] ?? '';
        $parsedUrl = parse_url($url);
        
        parse_str($parsedUrl['query'] ?? '', $params);
        $params['page'] = $page;
        
        return ($parsedUrl['path'] ?? '') . '?' . http_build_query($params);
    }

    /**
     * 取得基礎 URL
     * 
     * @return string
     */
    private function getBaseUrl(): string
    {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        return "{$protocol}://{$host}";
    }
}