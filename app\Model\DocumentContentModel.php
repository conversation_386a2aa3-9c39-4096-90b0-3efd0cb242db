<?php

namespace App\Model;

/**
 * 文檔內容模型
 * 處理文檔內容相關的資料操作
 */
class DocumentContentModel extends BaseModel
{
    protected string $table = 'documentContent';
    
    protected array $fillable = [
        'documentId',
        'content',
        'contentHash',
        'wordCount'
    ];
    
    protected array $casts = [
        'id' => 'int',
        'documentId' => 'int',
        'wordCount' => 'int',
        'createdAt' => 'datetime'
    ];

    /**
     * 根據文檔ID查找內容
     * 
     * @param int $documentId
     * @return array|null
     */
    public function findByDocumentId(int $documentId): ?array
    {
        return $this->findFirst(['documentId' => $documentId]);
    }

    /**
     * 根據內容雜湊查找
     * 
     * @param string $hash
     * @return array|null
     */
    public function findByContentHash(string $hash): ?array
    {
        return $this->findFirst(['contentHash' => $hash]);
    }

    /**
     * 更新文檔內容
     * 
     * @param int $documentId
     * @param string $content
     * @return int
     */
    public function updateContent(int $documentId, string $content): int
    {
        $contentHash = hash('sha256', $content);
        $wordCount = $this->calculateWordCount($content);
        
        $existing = $this->findByDocumentId($documentId);
        
        $data = [
            'content' => $content,
            'contentHash' => $contentHash,
            'wordCount' => $wordCount
        ];
        
        if ($existing) {
            return $this->update($existing['id'], $data);
        } else {
            $data['documentId'] = $documentId;
            $this->create($data);
            return 1;
        }
    }

    /**
     * 全文搜尋文檔內容
     * 
     * @param string $keyword
     * @param int $limit
     * @return array
     */
    public function searchContent(string $keyword, int $limit = 50): array
    {
        $sql = "
            SELECT dc.*, d.title, d.fileName 
            FROM {$this->table} dc
            JOIN document d ON dc.documentId = d.id
            WHERE MATCH(dc.content) AGAINST(? IN NATURAL LANGUAGE MODE)
            ORDER BY MATCH(dc.content) AGAINST(? IN NATURAL LANGUAGE MODE) DESC
            LIMIT ?
        ";
        
        return $this->db->fetchAll($sql, [$keyword, $keyword, $limit]);
    }

    /**
     * 取得內容統計資訊
     * 
     * @return array
     */
    public function getContentStatistics(): array
    {
        $sql = "
            SELECT 
                COUNT(*) as total_contents,
                AVG(wordCount) as avg_word_count,
                MAX(wordCount) as max_word_count,
                MIN(wordCount) as min_word_count,
                SUM(wordCount) as total_words
            FROM {$this->table}
        ";
        
        return $this->db->fetchOne($sql) ?: [];
    }

    /**
     * 根據字數範圍查找內容
     * 
     * @param int $minWords
     * @param int $maxWords
     * @return array
     */
    public function findByWordCountRange(int $minWords, int $maxWords): array
    {
        $conditions = [
            'wordCount >=' => $minWords,
            'wordCount <=' => $maxWords
        ];
        
        return $this->findAll($conditions, 'wordCount DESC');
    }

    /**
     * 計算字數
     * 
     * @param string $content
     * @return int
     */
    private function calculateWordCount(string $content): int
    {
        // 移除HTML標籤
        $content = strip_tags($content);
        
        // 計算中文字符數
        $chineseCount = preg_match_all('/[\x{4e00}-\x{9fff}]/u', $content);
        
        // 計算英文單詞數
        $englishWords = preg_split('/\s+/', preg_replace('/[^\w\s]/u', ' ', $content));
        $englishCount = count(array_filter($englishWords, function($word) {
            return preg_match('/[a-zA-Z]/', $word) && strlen(trim($word)) > 0;
        }));
        
        return $chineseCount + $englishCount;
    }

    /**
     * 檢查內容是否重複
     * 
     * @param string $content
     * @param int|null $excludeDocumentId 排除的文檔ID
     * @return bool
     */
    public function isDuplicateContent(string $content, ?int $excludeDocumentId = null): bool
    {
        $contentHash = hash('sha256', $content);
        $conditions = ['contentHash' => $contentHash];
        
        if ($excludeDocumentId) {
            $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE contentHash = ? AND documentId != ?";
            $result = $this->db->fetchOne($sql, [$contentHash, $excludeDocumentId]);
        } else {
            $result = $this->db->fetchOne("SELECT COUNT(*) as count FROM {$this->table} WHERE contentHash = ?", [$contentHash]);
        }
        
        return ($result['count'] ?? 0) > 0;
    }
}