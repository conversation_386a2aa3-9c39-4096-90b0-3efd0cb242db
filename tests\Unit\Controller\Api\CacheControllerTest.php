<?php

namespace Tests\Unit\Controller\Api;

use Tests\TestCase;
use App\Controller\Api\CacheController;

/**
 * 快取控制器單元測試
 */
class CacheControllerTest extends TestCase
{
    private CacheController $controller;
    private string $testCacheDir;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->controller = new CacheController();
        $this->testCacheDir = __DIR__ . '/../../../../storage/cache/test';
        
        // 建立測試快取目錄
        if (!is_dir($this->testCacheDir)) {
            mkdir($this->testCacheDir, 0755, true);
        }
        
        // 設定測試快取目錄
        $reflection = new \ReflectionClass($this->controller);
        $property = $reflection->getProperty('cacheDir');
        $property->setAccessible(true);
        $property->setValue($this->controller, $this->testCacheDir);
    }

    /**
     * 測試取得快取狀態
     */
    public function testStatus(): void
    {
        ob_start();
        $this->controller->status();
        $output = ob_get_clean();

        $response = json_decode($output, true);

        // 驗證回應結構
        $this->assertIsArray($response);
        $this->assertTrue($response['success']);
        $this->assertArrayHasKey('data', $response);
        $this->assertArrayHasKey('cache_enabled', $response['data']);
        $this->assertArrayHasKey('statistics', $response['data']);
        $this->assertArrayHasKey('cache_policies', $response['data']);
    }

    /**
     * 測試清除所有快取
     */
    public function testClearAll(): void
    {
        // 建立一些測試快取檔案
        file_put_contents($this->testCacheDir . '/test1.cache', 'test data 1');
        file_put_contents($this->testCacheDir . '/test2.cache', 'test data 2');

        $_SERVER['REQUEST_METHOD'] = 'DELETE';

        ob_start();
        $this->controller->clear();
        $output = ob_get_clean();

        $response = json_decode($output, true);

        // 驗證清除成功
        $this->assertIsArray($response);
        $this->assertTrue($response['success']);
        $this->assertStringContainsString('清除成功', $response['message']);

        // 驗證檔案已被刪除
        $this->assertFileDoesNotExist($this->testCacheDir . '/test1.cache');
        $this->assertFileDoesNotExist($this->testCacheDir . '/test2.cache');

        // 清理
        $_SERVER['REQUEST_METHOD'] = 'GET';
    }

    /**
     * 測試清除特定快取
     */
    public function testClearSpecific(): void
    {
        // 建立測試快取檔案
        file_put_contents($this->testCacheDir . '/documents.cache', 'documents data');
        file_put_contents($this->testCacheDir . '/news.cache', 'news data');

        $_SERVER['REQUEST_METHOD'] = 'DELETE';
        $_GET['type'] = 'documents';

        ob_start();
        $this->controller->clear();
        $output = ob_get_clean();

        $response = json_decode($output, true);

        // 驗證特定快取清除成功
        $this->assertIsArray($response);
        $this->assertTrue($response['success']);

        // 驗證只有指定的快取被清除
        $this->assertFileDoesNotExist($this->testCacheDir . '/documents.cache');
        $this->assertFileExists($this->testCacheDir . '/news.cache');

        // 清理
        unlink($this->testCacheDir . '/news.cache');
        unset($_GET['type']);
        $_SERVER['REQUEST_METHOD'] = 'GET';
    }

    /**
     * 測試預熱快取
     */
    public function testWarmup(): void
    {
        $_SERVER['REQUEST_METHOD'] = 'POST';

        ob_start();
        $this->controller->warmup();
        $output = ob_get_clean();

        $response = json_decode($output, true);

        // 驗證預熱成功
        $this->assertIsArray($response);
        $this->assertTrue($response['success']);
        $this->assertArrayHasKey('warmed_caches', $response['data']);

        // 清理
        $_SERVER['REQUEST_METHOD'] = 'GET';
    }

    /**
     * 測試取得快取統計
     */
    public function testGetCacheStatistics(): void
    {
        // 建立一些測試快取檔案
        file_put_contents($this->testCacheDir . '/stat1.cache', 'data1');
        file_put_contents($this->testCacheDir . '/stat2.cache', 'data2');

        // 使用反射調用私有方法
        $reflection = new \ReflectionClass($this->controller);
        $method = $reflection->getMethod('getCacheStatistics');
        $method->setAccessible(true);

        $stats = $method->invoke($this->controller);

        // 驗證統計資料
        $this->assertIsArray($stats);
        $this->assertArrayHasKey('total_files', $stats);
        $this->assertArrayHasKey('total_size', $stats);
        $this->assertArrayHasKey('cache_hit_rate', $stats);
        $this->assertGreaterThanOrEqual(2, $stats['total_files']);

        // 清理
        unlink($this->testCacheDir . '/stat1.cache');
        unlink($this->testCacheDir . '/stat2.cache');
    }

    /**
     * 測試快取策略取得
     */
    public function testGetCachePolicies(): void
    {
        // 使用反射調用私有方法
        $reflection = new \ReflectionClass($this->controller);
        $method = $reflection->getMethod('getCachePolicies');
        $method->setAccessible(true);

        $policies = $method->invoke($this->controller);

        // 驗證策略資料
        $this->assertIsArray($policies);
        $this->assertArrayHasKey('documents', $policies);
        $this->assertArrayHasKey('news', $policies);
        $this->assertArrayHasKey('search', $policies);
    }

    /**
     * 測試快取檔案驗證
     */
    public function testValidateCacheFile(): void
    {
        $validFile = $this->testCacheDir . '/valid.cache';
        $invalidFile = $this->testCacheDir . '/invalid.txt';

        // 建立測試檔案
        file_put_contents($validFile, 'valid cache data');
        file_put_contents($invalidFile, 'invalid file');

        // 使用反射調用私有方法
        $reflection = new \ReflectionClass($this->controller);
        $method = $reflection->getMethod('validateCacheFile');
        $method->setAccessible(true);

        // 測試有效快取檔案
        $isValid = $method->invoke($this->controller, $validFile);
        $this->assertTrue($isValid);

        // 測試無效快取檔案
        $isInvalid = $method->invoke($this->controller, $invalidFile);
        $this->assertFalse($isInvalid);

        // 清理
        unlink($validFile);
        unlink($invalidFile);
    }

    /**
     * 測試快取大小計算
     */
    public function testCalculateCacheSize(): void
    {
        // 建立不同大小的測試檔案
        file_put_contents($this->testCacheDir . '/size1.cache', str_repeat('a', 1024)); // 1KB
        file_put_contents($this->testCacheDir . '/size2.cache', str_repeat('b', 2048)); // 2KB

        // 使用反射調用私有方法
        $reflection = new \ReflectionClass($this->controller);
        $method = $reflection->getMethod('calculateCacheSize');
        $method->setAccessible(true);

        $totalSize = $method->invoke($this->controller);

        // 驗證大小計算
        $this->assertGreaterThanOrEqual(3072, $totalSize); // 至少 3KB

        // 清理
        unlink($this->testCacheDir . '/size1.cache');
        unlink($this->testCacheDir . '/size2.cache');
    }

    /**
     * 測試快取過期檢查
     */
    public function testCheckCacheExpiry(): void
    {
        $expiredFile = $this->testCacheDir . '/expired.cache';
        $validFile = $this->testCacheDir . '/valid.cache';

        // 建立過期檔案（修改時間為1小時前）
        file_put_contents($expiredFile, 'expired data');
        touch($expiredFile, time() - 3600);

        // 建立有效檔案
        file_put_contents($validFile, 'valid data');

        // 使用反射調用私有方法
        $reflection = new \ReflectionClass($this->controller);
        $method = $reflection->getMethod('checkCacheExpiry');
        $method->setAccessible(true);

        // 測試過期檔案（假設快取有效期為30分鐘）
        $isExpired = $method->invoke($this->controller, $expiredFile, 1800);
        $this->assertTrue($isExpired);

        // 測試有效檔案
        $isValid = $method->invoke($this->controller, $validFile, 1800);
        $this->assertFalse($isValid);

        // 清理
        unlink($expiredFile);
        unlink($validFile);
    }

    /**
     * 測試快取命中率計算
     */
    public function testCalculateHitRate(): void
    {
        // 使用反射調用私有方法
        $reflection = new \ReflectionClass($this->controller);
        $method = $reflection->getMethod('calculateHitRate');
        $method->setAccessible(true);

        // 測試不同的命中率
        $hitRate1 = $method->invoke($this->controller, 80, 100);
        $this->assertEquals(80.0, $hitRate1);

        $hitRate2 = $method->invoke($this->controller, 0, 100);
        $this->assertEquals(0.0, $hitRate2);

        $hitRate3 = $method->invoke($this->controller, 50, 0);
        $this->assertEquals(0.0, $hitRate3);
    }

    /**
     * 測試錯誤處理
     */
    public function testErrorHandling(): void
    {
        // 設定無效的快取目錄
        $reflection = new \ReflectionClass($this->controller);
        $property = $reflection->getProperty('cacheDir');
        $property->setAccessible(true);
        $property->setValue($this->controller, '/invalid/path');

        ob_start();
        $this->controller->status();
        $output = ob_get_clean();

        $response = json_decode($output, true);

        // 驗證錯誤處理
        $this->assertIsArray($response);
        $this->assertFalse($response['success']);
        $this->assertArrayHasKey('error', $response);
    }

    /**
     * 測試 API 回應格式
     */
    public function testApiResponseFormat(): void
    {
        ob_start();
        $this->controller->status();
        $output = ob_get_clean();

        // 驗證 JSON 格式
        $this->assertJson($output);

        $response = json_decode($output, true);

        // 驗證標準 API 回應結構
        $this->assertArrayHasKey('success', $response);
        $this->assertArrayHasKey('message', $response);
        $this->assertArrayHasKey('data', $response);
        $this->assertArrayHasKey('timestamp', $response);
    }

    protected function tearDown(): void
    {
        // 清理測試快取目錄
        if (is_dir($this->testCacheDir)) {
            $files = array_diff(scandir($this->testCacheDir), ['.', '..']);
            foreach ($files as $file) {
                $filePath = $this->testCacheDir . '/' . $file;
                if (is_file($filePath)) {
                    unlink($filePath);
                }
            }
            rmdir($this->testCacheDir);
        }

        // 清理全域變數
        unset($_GET['type']);
        $_SERVER['REQUEST_METHOD'] = 'GET';

        parent::tearDown();
    }
}