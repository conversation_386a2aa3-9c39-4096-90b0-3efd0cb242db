<?php

namespace App\Service;

use App\Model\DocumentModel;
use App\Model\DocumentContentModel;
use App\Database\Database;
use Exception;

/**
 * 索引服務
 * 處理文檔索引建立和管理
 */
class IndexService extends BaseService
{
    private DocumentModel $documentModel;
    private DocumentContentModel $contentModel;
    
    public function __construct()
    {
        parent::__construct();
        $this->documentModel = new DocumentModel();
        $this->contentModel = new DocumentContentModel();
    }

    /**
     * 為文檔建立索引
     * 
     * @param int $documentId
     * @param string $content
     * @return array
     */
    public function indexDocument(int $documentId, string $content): array
    {
        try {
            // 分塊處理大文檔
            $chunks = $this->splitContent($content);
            
            // 刪除舊索引
            $this->deleteDocumentIndex($documentId);
            
            $indexedChunks = 0;
            
            foreach ($chunks as $index => $chunk) {
                if (empty(trim($chunk))) {
                    continue;
                }
                
                // 提取關鍵字
                $keywords = $this->extractKeywords($chunk);
                
                // 建立索引記錄
                $indexData = [
                    'documentId' => $documentId,
                    'chunkIndex' => $index,
                    'chunkContent' => $chunk,
                    'keywords' => implode(', ', $keywords)
                ];
                
                $this->createIndexRecord($indexData);
                $indexedChunks++;
            }

            $this->log('info', '文檔索引建立成功', [
                'document_id' => $documentId,
                'chunks' => $indexedChunks
            ]);

            return $this->formatResponse(true, [
                'document_id' => $documentId,
                'indexed_chunks' => $indexedChunks
            ], '索引建立成功');

        } catch (Exception $e) {
            return $this->handleException($e, '建立文檔索引');
        }
    }

    /**
     * 重新建立文檔索引
     * 
     * @param int $documentId
     * @param string $content
     * @return array
     */
    public function reindexDocument(int $documentId, string $content): array
    {
        try {
            $this->log('info', '開始重新建立索引', ['document_id' => $documentId]);
            
            return $this->indexDocument($documentId, $content);

        } catch (Exception $e) {
            return $this->handleException($e, '重新建立文檔索引');
        }
    }

    /**
     * 批量建立索引
     * 
     * @param array $documentIds 文檔ID陣列
     * @return array
     */
    public function batchIndex(array $documentIds = []): array
    {
        try {
            if (empty($documentIds)) {
                // 取得所有未建立索引的文檔
                $documents = $this->documentModel->findByStatus('processing', 100);
            } else {
                $documents = [];
                foreach ($documentIds as $id) {
                    $doc = $this->documentModel->find($id);
                    if ($doc) {
                        $documents[] = $doc;
                    }
                }
            }

            $results = [
                'total' => count($documents),
                'success' => 0,
                'failed' => 0,
                'errors' => []
            ];

            foreach ($documents as $document) {
                try {
                    $content = $this->contentModel->findByDocumentId($document['id']);
                    
                    if (!$content) {
                        $results['failed']++;
                        $results['errors'][] = "文檔 {$document['id']} 沒有內容";
                        continue;
                    }

                    $result = $this->indexDocument($document['id'], $content['content']);
                    
                    if ($result['success']) {
                        $this->documentModel->updateStatus($document['id'], 'indexed');
                        $results['success']++;
                    } else {
                        $this->documentModel->updateStatus($document['id'], 'failed');
                        $results['failed']++;
                        $results['errors'][] = "文檔 {$document['id']} 索引失敗";
                    }

                } catch (Exception $e) {
                    $results['failed']++;
                    $results['errors'][] = "文檔 {$document['id']} 處理異常: " . $e->getMessage();
                }
            }

            $this->log('info', '批量索引完成', $results);

            return $this->formatResponse(true, $results, '批量索引處理完成');

        } catch (Exception $e) {
            return $this->handleException($e, '批量建立索引');
        }
    }

    /**
     * 刪除文檔索引
     * 
     * @param int $documentId
     * @return array
     */
    public function deleteDocumentIndex(int $documentId): array
    {
        try {
            $sql = "DELETE FROM documentIndex WHERE documentId = ?";
            $affected = $this->db->delete($sql, [$documentId]);

            $this->log('info', '文檔索引刪除', [
                'document_id' => $documentId,
                'deleted_records' => $affected
            ]);

            return $this->formatResponse(true, [
                'document_id' => $documentId,
                'deleted_records' => $affected
            ], '索引刪除成功');

        } catch (Exception $e) {
            return $this->handleException($e, '刪除文檔索引');
        }
    }

    /**
     * 取得索引統計資訊
     * 
     * @return array
     */
    public function getIndexStatistics(): array
    {
        try {
            $sql = "
                SELECT 
                    COUNT(DISTINCT documentId) as indexed_documents,
                    COUNT(*) as total_chunks,
                    AVG(CHAR_LENGTH(chunkContent)) as avg_chunk_size,
                    MAX(CHAR_LENGTH(chunkContent)) as max_chunk_size,
                    MIN(CHAR_LENGTH(chunkContent)) as min_chunk_size
                FROM documentIndex
            ";
            
            $stats = $this->db->fetchOne($sql);

            // 取得文檔狀態統計
            $statusStats = $this->documentModel->getStatistics();

            $result = array_merge($stats ?: [], [
                'total_documents' => $statusStats['total'] ?? 0,
                'indexed_documents_count' => $statusStats['indexed'] ?? 0,
                'processing_documents' => $statusStats['processing'] ?? 0,
                'failed_documents' => $statusStats['failed'] ?? 0
            ]);

            return $this->formatResponse(true, $result);

        } catch (Exception $e) {
            return $this->handleException($e, '取得索引統計');
        }
    }

    /**
     * 優化索引
     * 
     * @return array
     */
    public function optimizeIndex(): array
    {
        try {
            // 重建全文索引
            $this->db->query("OPTIMIZE TABLE documentIndex");
            
            // 清理孤立的索引記錄
            $sql = "
                DELETE di FROM documentIndex di
                LEFT JOIN document d ON di.documentId = d.id
                WHERE d.id IS NULL
            ";
            $cleaned = $this->db->delete($sql);

            $this->log('info', '索引優化完成', ['cleaned_records' => $cleaned]);

            return $this->formatResponse(true, [
                'cleaned_records' => $cleaned
            ], '索引優化完成');

        } catch (Exception $e) {
            return $this->handleException($e, '優化索引');
        }
    }

    /**
     * 分割內容為塊
     * 
     * @param string $content
     * @param int $maxChunkSize 最大塊大小（字符數）
     * @return array
     */
    private function splitContent(string $content, int $maxChunkSize = 1000): array
    {
        // 按段落分割
        $paragraphs = preg_split('/\n\s*\n/', $content);
        $chunks = [];
        $currentChunk = '';

        foreach ($paragraphs as $paragraph) {
            $paragraph = trim($paragraph);
            
            if (empty($paragraph)) {
                continue;
            }

            // 如果當前塊加上新段落超過限制，則開始新塊
            if (mb_strlen($currentChunk . "\n\n" . $paragraph) > $maxChunkSize && !empty($currentChunk)) {
                $chunks[] = trim($currentChunk);
                $currentChunk = $paragraph;
            } else {
                if (!empty($currentChunk)) {
                    $currentChunk .= "\n\n" . $paragraph;
                } else {
                    $currentChunk = $paragraph;
                }
            }

            // 如果單個段落就超過限制，需要進一步分割
            if (mb_strlen($currentChunk) > $maxChunkSize) {
                $sentences = $this->splitIntoSentences($currentChunk);
                $tempChunk = '';
                
                foreach ($sentences as $sentence) {
                    if (mb_strlen($tempChunk . $sentence) > $maxChunkSize && !empty($tempChunk)) {
                        $chunks[] = trim($tempChunk);
                        $tempChunk = $sentence;
                    } else {
                        $tempChunk .= $sentence;
                    }
                }
                
                $currentChunk = $tempChunk;
            }
        }

        // 添加最後一塊
        if (!empty($currentChunk)) {
            $chunks[] = trim($currentChunk);
        }

        return array_filter($chunks, function($chunk) {
            return mb_strlen(trim($chunk)) > 0;
        });
    }

    /**
     * 分割為句子
     * 
     * @param string $text
     * @return array
     */
    private function splitIntoSentences(string $text): array
    {
        // 中文和英文句號、問號、驚嘆號
        $sentences = preg_split('/[。！？.!?]+/', $text, -1, PREG_SPLIT_NO_EMPTY);
        
        return array_map('trim', $sentences);
    }

    /**
     * 提取關鍵字
     * 
     * @param string $content
     * @return array
     */
    private function extractKeywords(string $content): array
    {
        // 移除標點符號和特殊字符
        $cleanContent = preg_replace('/[^\p{L}\p{N}\s]/u', ' ', $content);
        
        // 分詞（簡化版本）
        $words = [];
        
        // 提取中文詞彙（2-4個字符）
        preg_match_all('/[\p{Han}]{2,4}/u', $cleanContent, $chineseWords);
        $words = array_merge($words, $chineseWords[0]);
        
        // 提取英文單詞（3個字符以上）
        preg_match_all('/[a-zA-Z]{3,}/u', $cleanContent, $englishWords);
        $words = array_merge($words, $englishWords[0]);
        
        // 統計詞頻
        $wordCount = array_count_values($words);
        
        // 過濾停用詞
        $stopWords = $this->getStopWords();
        $filteredWords = array_diff_key($wordCount, array_flip($stopWords));
        
        // 按頻率排序，取前20個
        arsort($filteredWords);
        $keywords = array_slice(array_keys($filteredWords), 0, 20);
        
        return $keywords;
    }

    /**
     * 取得停用詞列表
     * 
     * @return array
     */
    private function getStopWords(): array
    {
        return [
            // 中文停用詞
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一個', '上', '也', '很', '到', '說', '要', '去', '你', '會', '著', '沒有', '看', '好', '自己', '這',
            // 英文停用詞
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those'
        ];
    }

    /**
     * 建立索引記錄
     * 
     * @param array $data
     * @return int
     */
    private function createIndexRecord(array $data): int
    {
        $sql = "
            INSERT INTO documentIndex (documentId, chunkIndex, chunkContent, keywords, createdAt)
            VALUES (?, ?, ?, ?, NOW())
        ";
        
        return $this->db->insert($sql, [
            $data['documentId'],
            $data['chunkIndex'],
            $data['chunkContent'],
            $data['keywords']
        ]);
    }
}