<?php
/**
 * 錯誤頁面通用佈局模板
 * 可被其他錯誤頁面繼承使用
 */

$pageTitle = $pageTitle ?? '錯誤';
$errorCode = $errorCode ?? '500';
$errorTitle = $errorTitle ?? '發生錯誤';
$errorMessage = $errorMessage ?? '抱歉，發生了一個錯誤。';
$showBackButton = $showBackButton ?? true;
$showHomeButton = $showHomeButton ?? true;
$showContactButton = $showContactButton ?? true;
$customButtons = $customButtons ?? [];
$additionalContent = $additionalContent ?? '';
?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($pageTitle) ?> - Tech.VG</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;700&display=swap');
        
        body {
            font-family: 'Noto Sans TC', sans-serif;
        }
        
        .error-animation {
            animation: float 3s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        .btn-hover {
            transition: all 0.3s ease;
        }
        
        .btn-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }
        
        .gradient-text {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
    <?php if (isset($customStyles)): ?>
        <style><?= $customStyles ?></style>
    <?php endif; ?>
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center">
    <div class="max-w-2xl mx-auto px-4 text-center">
        <!-- 錯誤圖示 -->
        <div class="error-animation mb-8">
            <?php if (isset($customIcon)): ?>
                <?= $customIcon ?>
            <?php else: ?>
                <div class="inline-flex items-center justify-center w-32 h-32 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full shadow-2xl">
                    <i class="fas fa-exclamation-triangle text-white text-5xl"></i>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- 錯誤代碼 -->
        <h1 class="text-8xl font-bold gradient-text mb-4"><?= htmlspecialchars($errorCode) ?></h1>
        
        <!-- 錯誤標題 -->
        <h2 class="text-3xl font-semibold text-gray-700 mb-6"><?= htmlspecialchars($errorTitle) ?></h2>
        
        <!-- 錯誤訊息 -->
        <p class="text-lg text-gray-600 mb-8 leading-relaxed">
            <?= htmlspecialchars($errorMessage) ?>
        </p>
        
        <!-- 額外內容 -->
        <?php if (!empty($additionalContent)): ?>
            <?= $additionalContent ?>
        <?php endif; ?>
        
        <!-- 操作按鈕 -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center mb-8">
            <?php if ($showHomeButton): ?>
                <a href="/" class="btn-hover inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-home mr-2"></i>
                    返回首頁
                </a>
            <?php endif; ?>
            
            <?php if ($showContactButton): ?>
                <a href="/contact" class="btn-hover inline-flex items-center px-6 py-3 bg-gray-600 text-white font-medium rounded-lg hover:bg-gray-700 transition-colors">
                    <i class="fas fa-envelope mr-2"></i>
                    聯繫我們
                </a>
            <?php endif; ?>
            
            <!-- 自定義按鈕 -->
            <?php foreach ($customButtons as $button): ?>
                <a href="<?= htmlspecialchars($button['url']) ?>" 
                   class="btn-hover inline-flex items-center px-6 py-3 <?= htmlspecialchars($button['class'] ?? 'bg-purple-600 hover:bg-purple-700') ?> text-white font-medium rounded-lg transition-colors">
                    <?php if (isset($button['icon'])): ?>
                        <i class="<?= htmlspecialchars($button['icon']) ?> mr-2"></i>
                    <?php endif; ?>
                    <?= htmlspecialchars($button['text']) ?>
                </a>
            <?php endforeach; ?>
        </div>
        
        <!-- 返回按鈕 -->
        <?php if ($showBackButton): ?>
            <div class="mt-8">
                <button onclick="history.back()" class="text-gray-500 hover:text-gray-700 transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>
                    返回上一頁
                </button>
            </div>
        <?php endif; ?>
        
        <!-- 錯誤詳情（僅在開發模式顯示） -->
        <?php if (isset($_ENV['APP_DEBUG']) && $_ENV['APP_DEBUG'] && isset($errorDetails)): ?>
            <div class="mt-8 p-4 bg-red-50 border border-red-200 rounded-lg text-left">
                <h3 class="text-lg font-semibold text-red-800 mb-2">開發模式 - 錯誤詳情</h3>
                <pre class="text-sm text-red-700 overflow-auto"><?= htmlspecialchars($errorDetails) ?></pre>
            </div>
        <?php endif; ?>
    </div>
    
    <!-- 背景裝飾 -->
    <div class="fixed inset-0 -z-10 overflow-hidden">
        <div class="absolute -top-40 -right-32 w-80 h-80 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse"></div>
        <div class="absolute -bottom-40 -left-32 w-80 h-80 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse"></div>
    </div>
    
    <?php if (isset($customScripts)): ?>
        <script><?= $customScripts ?></script>
    <?php endif; ?>
</body>
</html>