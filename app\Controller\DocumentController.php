<?php

namespace App\Controller;

use App\Service\DocumentService;
use App\Service\IndexService;

/**
 * 文檔管理控制器
 * 處理文檔上傳、管理和檢視功能
 */
class DocumentController extends BaseController
{
    private DocumentService $documentService;
    private IndexService $indexService;

    public function __construct()
    {
        $this->documentService = new DocumentService();
        $this->indexService = new IndexService();
    }

    /**
     * 文檔列表頁面
     * 
     * @return void
     */
    public function index(): void
    {
        try {
            $page = max(1, (int)$this->getGet('page', 1));
            $status = $this->getGet('status', '');
            $fileType = $this->getGet('file_type', '');
            
            // 建立過濾條件
            $filters = [];
            if (!empty($status)) {
                $filters['status'] = $status;
            }
            if (!empty($fileType)) {
                $filters['file_type'] = $fileType;
            }

            // 取得文檔列表
            $result = $this->documentService->getDocuments($filters, $page, 20);
            
            if ($result['success']) {
                $this->setViewData('documents', $result['data']);
                $this->setViewData('pagination', $result['meta']['pagination']);
            } else {
                $this->setViewData('documents', []);
                $this->setViewData('error', $result['message']);
            }

            $this->setViewData('currentPage', $page);
            $this->setViewData('status', $status);
            $this->setViewData('fileType', $fileType);
            $this->setViewData('pageTitle', '文檔管理 - Tech.VG');
            
            $this->render('document/index');

        } catch (\Exception $e) {
            $this->logError('文檔列表載入失敗', [
                'error' => $e->getMessage()
            ]);

            $this->setViewData('error', '無法載入文檔列表');
            $this->render('error/500');
        }
    }

    /**
     * 文檔上傳頁面
     * 
     * @return void
     */
    public function uploadForm(): void
    {
        $this->setViewData('pageTitle', '上傳文檔 - Tech.VG');
        $this->setViewData('csrfToken', $this->generateCsrfToken());
        $this->setViewData('maxFileSize', $this->formatBytes(10 * 1024 * 1024)); // 10MB
        $this->setViewData('allowedTypes', ['txt', 'md', 'markdown']);
        
        $this->render('document/upload');
    }

    /**
     * 處理文檔上傳
     * 
     * @return void
     */
    public function upload(): void
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/document/upload');
            return;
        }

        // 驗證CSRF令牌
        if (!$this->validateCsrfToken()) {
            $this->setViewData('error', '無效的請求');
            $this->upload();
            return;
        }

        // 檢查是否有檔案上傳
        if (!isset($_FILES['document']) || $_FILES['document']['error'] === UPLOAD_ERR_NO_FILE) {
            $this->setViewData('error', '請選擇要上傳的檔案');
            $this->upload();
            return;
        }

        $file = $_FILES['document'];
        $title = trim($this->getPost('title', ''));
        
        // 如果沒有提供標題，使用檔案名稱
        if (empty($title)) {
            $title = pathinfo($file['name'], PATHINFO_FILENAME);
        }

        // 驗證檔案
        $validation = $this->validateFileUpload($file, ['txt', 'md', 'markdown'], $this->getMaxFileSize());
        
        if (!$validation['valid']) {
            $this->setViewData('error', $validation['error']);
            $this->setViewData('formData', ['title' => $title]);
            $this->upload();
            return;
        }

        try {
            // 上傳文檔
            $result = $this->documentService->uploadDocument($file, ['title' => $title]);

            if ($result['success']) {
                $this->jsonResponse([
                    'success' => true,
                    'message' => '檔案上傳成功！',
                    'documentId' => $result['data']['document_id'],
                    'redirectUrl' => '/documents/' . $result['data']['document_id']
                ]);
            } else {
                $this->jsonResponse(['success' => false, 'error' => $result['message']], 400);
            }

        } catch (\Exception $e) {
            $this->logError('檔案上傳失敗', [
                'filename' => $file['name'],
                'title' => $title,
                'error' => $e->getMessage()
            ]);

            $this->jsonResponse(['success' => false, 'error' => '檔案上傳失敗：' . $e->getMessage()], 500);
        }
    }

    /**
     * 檢視文檔詳情
     * 
     * @param int $id 文檔ID
     * @return void
     */
    public function show(int $id): void
    {
        try {
            $result = $this->documentService->getDocument($id, true);
            
            if (!$result['success']) {
                $this->setViewData('error', $result['message']);
                $this->render('error/404');
                return;
            }

            $document = $result['data'];
            
            $this->setViewData('document', $document);
            $this->setViewData('pageTitle', $document['title'] . ' - Tech.VG');
            
            $this->render('document/show');

        } catch (\Exception $e) {
            $this->logError('文檔檢視失敗', [
                'documentId' => $id,
                'error' => $e->getMessage()
            ]);

            $this->setViewData('error', '無法載入文檔');
            $this->render('error/500');
        }
    }

    /**
     * 刪除文檔
     * 
     * @param int $id 文檔ID
     * @return void
     */
    public function delete(int $id): void
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'DELETE' && $_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['error' => '不支援的請求方法'], 405);
            return;
        }

        try {
            $result = $this->documentService->deleteDocument($id);
            
            if ($result['success']) {
                $this->jsonResponse(['success' => true, 'message' => $result['message']]);
            } else {
                $this->jsonResponse(['error' => $result['message']], 400);
            }

        } catch (\Exception $e) {
            $this->logError('文檔刪除失敗', [
                'documentId' => $id,
                'error' => $e->getMessage()
            ]);

            $this->jsonResponse(['error' => '刪除失敗'], 500);
        }
    }

    /**
     * 下載文檔
     * 
     * @param int $id 文檔ID
     * @return void
     */
    public function download(int $id): void
    {
        try {
            $result = $this->documentService->getDocument($id);
            
            if (!$result['success']) {
                $this->setViewData('error', $result['message']);
                $this->render('error/404');
                return;
            }

            $document = $result['data'];
            
            // 檢查文件是否存在
            if (!file_exists($document['filePath'])) {
                $this->setViewData('error', '文件不存在');
                $this->render('error/404');
                return;
            }

            // 設定下載標頭
            header('Content-Type: application/octet-stream');
            header('Content-Disposition: attachment; filename="' . $document['fileName'] . '"');
            header('Content-Length: ' . filesize($document['filePath']));
            
            // 輸出文件內容
            readfile($document['filePath']);
            exit;

        } catch (\Exception $e) {
            $this->logError('文檔下載失敗', [
                'documentId' => $id,
                'error' => $e->getMessage()
            ]);

            $this->setViewData('error', '下載失敗');
            $this->render('error/500');
        }
    }

    /**
     * 編輯文檔表單
     * 
     * @param int $id 文檔ID
     * @return void
     */
    public function editForm(int $id): void
    {
        try {
            $result = $this->documentService->getDocument($id);
            
            if (!$result['success']) {
                $this->setViewData('error', $result['message']);
                $this->render('error/404');
                return;
            }

            $document = $result['data'];
            
            $this->setViewData('document', $document);
            $this->setViewData('pageTitle', '編輯文檔 - ' . $document['title']);
            $this->setViewData('csrfToken', $this->generateCsrfToken());
            
            $this->render('document/edit');

        } catch (\Exception $e) {
            $this->logError('文檔編輯頁面載入失敗', [
                'documentId' => $id,
                'error' => $e->getMessage()
            ]);

            $this->setViewData('error', '無法載入編輯頁面');
            $this->render('error/500');
        }
    }

    /**
     * 更新文檔
     * 
     * @param int $id 文檔ID
     * @return void
     */
    public function update(int $id): void
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect("/documents/{$id}/edit");
            return;
        }

        // 驗證CSRF令牌
        if (!$this->validateCsrfToken()) {
            $this->setViewData('error', '無效的請求');
            $this->editForm($id);
            return;
        }

        $title = trim($this->getPost('title', ''));
        
        if (empty($title)) {
            $this->setViewData('error', '請輸入文檔標題');
            $this->editForm($id);
            return;
        }

        try {
            $result = $this->documentService->updateDocument($id, ['title' => $title]);
            
            if ($result['success']) {
                $this->redirect("/documents/{$id}");
            } else {
                $this->setViewData('error', $result['message']);
                $this->editForm($id);
            }

        } catch (\Exception $e) {
            $this->logError('文檔更新失敗', [
                'documentId' => $id,
                'title' => $title,
                'error' => $e->getMessage()
            ]);

            $this->setViewData('error', '更新失敗：' . $e->getMessage());
            $this->editForm($id);
        }
    }

    /**
     * 重新建立索引
     * 
     * @param int $id 文檔ID
     * @return void
     */
    public function reindex(int $id): void
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['error' => '不支援的請求方法'], 405);
            return;
        }

        try {
            $result = $this->documentService->reindexDocument($id);
            
            if ($result['success']) {
                $this->jsonResponse(['success' => true, 'message' => $result['message']]);
            } else {
                $this->jsonResponse(['error' => $result['message']], 400);
            }

        } catch (\Exception $e) {
            $this->logError('重新索引失敗', [
                'documentId' => $id,
                'error' => $e->getMessage()
            ]);

            $this->jsonResponse(['error' => '重新索引失敗'], 500);
        }
    }

    /**
     * 格式化位元組大小
     * 
     * @param int $bytes
     * @return string
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $factor = floor((strlen($bytes) - 1) / 3);
        
        return sprintf("%.2f", $bytes / pow(1024, $factor)) . ' ' . $units[$factor];
    }

    /**
     * 取得最大檔案大小
     * 
     * @return int
     */
    private function getMaxFileSize(): int
    {
        // 從系統配置取得，預設10MB
        return 10 * 1024 * 1024;
    }
}