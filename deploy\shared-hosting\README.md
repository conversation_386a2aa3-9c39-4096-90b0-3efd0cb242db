# 共享主機部署指南

## 概述

本指南說明如何在只有 `htdocs` 或 `public_html` 資料夾的共享主機上部署 Tech.VG 應用程式。

## 部署結構

```
htdocs/
├── index.php              # 主要入口點
├── api/                   # API 入口點
├── assets/                # 靜態資源
├── uploads/               # 上傳檔案
├── .htaccess              # Apache 重寫規則
└── app/                   # 應用程式檔案（受保護）
    ├── Controller/
    ├── Model/
    ├── Service/
    ├── View/
    └── ...
```

## 安全考量

1. **檔案保護**: 使用 `.htaccess` 保護敏感檔案
2. **配置分離**: 環境配置與程式碼分離
3. **錯誤處理**: 生產環境錯誤處理

## 部署步驟

1. 上傳所有檔案到 `htdocs` 資料夾
2. 設定檔案權限
3. 配置環境變數
4. 測試應用程式

## 檔案權限

- 資料夾: 755
- PHP 檔案: 644
- 上傳資料夾: 777 (僅限必要時)

## 環境配置

複製 `.env.example` 為 `.env` 並設定：

```
DB_HOST=localhost
DB_NAME=your_database
DB_USER=your_username
DB_PASS=your_password
APP_ENV=production
APP_DEBUG=false
```