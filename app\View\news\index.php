<?php
/**
 * 新聞列表頁面
 */

$news = $news ?? [];
$popularNews = $popularNews ?? [];
$currentPage = $currentPage ?? 1;
$category = $category ?? '';
$sourceId = $sourceId ?? '';
$dateFrom = $dateFrom ?? '';
$dateTo = $dateTo ?? '';
$error = $error ?? '';

include __DIR__ . '/../layout/header.php';
?>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- 頁面標題 -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">科技新聞</h1>
        <p class="text-gray-600">掌握最新科技動態，探索未來趨勢</p>
    </div>

    <!-- 篩選器 -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <form method="GET" action="/news" class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <!-- 分類篩選 -->
            <div>
                <label for="category" class="block text-sm font-medium text-gray-700 mb-2">分類</label>
                <select name="category" id="category" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">所有分類</option>
                    <option value="ai" <?= $category === 'ai' ? 'selected' : '' ?>>人工智慧</option>
                    <option value="blockchain" <?= $category === 'blockchain' ? 'selected' : '' ?>>區塊鏈</option>
                    <option value="mobile" <?= $category === 'mobile' ? 'selected' : '' ?>>行動科技</option>
                    <option value="web" <?= $category === 'web' ? 'selected' : '' ?>>網路技術</option>
                    <option value="security" <?= $category === 'security' ? 'selected' : '' ?>>資訊安全</option>
                </select>
            </div>

            <!-- 日期範圍 -->
            <div>
                <label for="date_from" class="block text-sm font-medium text-gray-700 mb-2">開始日期</label>
                <input type="date" name="date_from" id="date_from" value="<?= htmlspecialchars($dateFrom) ?>" 
                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>

            <div>
                <label for="date_to" class="block text-sm font-medium text-gray-700 mb-2">結束日期</label>
                <input type="date" name="date_to" id="date_to" value="<?= htmlspecialchars($dateTo) ?>" 
                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>

            <!-- 搜尋按鈕 -->
            <div class="flex items-end">
                <button type="submit" class="w-full btn-primary text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
                    <i class="fas fa-filter mr-2"></i>篩選
                </button>
            </div>
        </form>
    </div>

    <?php if ($error): ?>
        <div class="bg-red-50 border border-red-200 rounded-md p-4 mb-8">
            <div class="flex">
                <i class="fas fa-exclamation-circle text-red-400 mr-3 mt-1"></i>
                <div class="text-red-700"><?= htmlspecialchars($error) ?></div>
            </div>
        </div>
    <?php endif; ?>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- 主要新聞列表 -->
        <div class="lg:col-span-2">
            <?php if (empty($news)): ?>
                <div class="text-center py-12">
                    <i class="fas fa-newspaper text-gray-400 text-6xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">暫無新聞</h3>
                    <p class="text-gray-500">目前沒有符合條件的新聞，請稍後再試或調整篩選條件。</p>
                </div>
            <?php else: ?>
                <div class="space-y-6">
                    <?php foreach ($news as $article): ?>
                        <article class="bg-white rounded-lg shadow-md overflow-hidden card-hover">
                            <div class="md:flex">
                                <!-- 新聞圖片 -->
                                <div class="md:w-1/3">
                                    <?php if (!empty($article['image_url'])): ?>
                                        <img src="<?= htmlspecialchars($article['image_url']) ?>" 
                                             alt="<?= htmlspecialchars($article['title']) ?>"
                                             class="w-full h-48 md:h-full object-cover">
                                    <?php else: ?>
                                        <div class="w-full h-48 md:h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                                            <i class="fas fa-newspaper text-white text-3xl"></i>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <!-- 新聞內容 -->
                                <div class="md:w-2/3 p-6">
                                    <!-- 新聞元資訊 -->
                                    <div class="flex items-center text-sm text-gray-500 mb-3">
                                        <i class="fas fa-clock mr-1"></i>
                                        <?= date('Y-m-d H:i', strtotime($article['published_at'] ?? 'now')) ?>
                                        
                                        <?php if (!empty($article['source_name'])): ?>
                                            <span class="mx-2">•</span>
                                            <i class="fas fa-globe mr-1"></i>
                                            <?= htmlspecialchars($article['source_name']) ?>
                                        <?php endif; ?>

                                        <?php if (!empty($article['category'])): ?>
                                            <span class="mx-2">•</span>
                                            <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                                                <?= htmlspecialchars($article['category']) ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>

                                    <!-- 新聞標題 -->
                                    <h2 class="text-xl font-bold text-gray-900 mb-3">
                                        <a href="/news/<?= $article['id'] ?>" class="hover:text-blue-600 transition-colors">
                                            <?= htmlspecialchars($article['title']) ?>
                                        </a>
                                    </h2>

                                    <!-- 新聞摘要 -->
                                    <?php if (!empty($article['summary'])): ?>
                                        <p class="text-gray-600 mb-4 line-clamp-3">
                                            <?= htmlspecialchars($article['summary']) ?>
                                        </p>
                                    <?php endif; ?>

                                    <!-- 操作按鈕 -->
                                    <div class="flex justify-between items-center">
                                        <a href="/news/<?= $article['id'] ?>" 
                                           class="text-blue-600 hover:text-blue-800 font-medium">
                                            閱讀全文 <i class="fas fa-arrow-right ml-1"></i>
                                        </a>

                                        <div class="flex items-center text-sm text-gray-500">
                                            <i class="fas fa-eye mr-1"></i>
                                            <?= number_format($article['views'] ?? 0) ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </article>
                    <?php endforeach; ?>
                </div>

                <!-- 分頁 -->
                <div class="mt-8 flex justify-center">
                    <nav class="flex items-center space-x-2">
                        <?php if ($currentPage > 1): ?>
                            <a href="?page=<?= $currentPage - 1 ?>&category=<?= urlencode($category) ?>&date_from=<?= urlencode($dateFrom) ?>&date_to=<?= urlencode($dateTo) ?>" 
                               class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                                上一頁
                            </a>
                        <?php endif; ?>

                        <span class="px-3 py-2 text-sm font-medium text-white bg-blue-600 border border-blue-600 rounded-md">
                            第 <?= $currentPage ?> 頁
                        </span>

                        <a href="?page=<?= $currentPage + 1 ?>&category=<?= urlencode($category) ?>&date_from=<?= urlencode($dateFrom) ?>&date_to=<?= urlencode($dateTo) ?>" 
                           class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                            下一頁
                        </a>
                    </nav>
                </div>
            <?php endif; ?>
        </div>

        <!-- 側邊欄 -->
        <div class="lg:col-span-1">
            <!-- 熱門新聞 -->
            <?php if (!empty($popularNews)): ?>
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">
                        <i class="fas fa-fire text-orange-500 mr-2"></i>熱門新聞
                    </h3>
                    <div class="space-y-4">
                        <?php foreach (array_slice($popularNews, 0, 5) as $index => $popular): ?>
                            <div class="flex items-start">
                                <span class="flex-shrink-0 w-6 h-6 bg-gradient-to-br from-orange-500 to-red-600 text-white text-xs font-bold rounded-full flex items-center justify-center mr-3 mt-1">
                                    <?= $index + 1 ?>
                                </span>
                                <div class="flex-1">
                                    <h4 class="text-sm font-medium text-gray-900 line-clamp-2 mb-1">
                                        <a href="/news/<?= $popular['id'] ?>" class="hover:text-blue-600 transition-colors">
                                            <?= htmlspecialchars($popular['title']) ?>
                                        </a>
                                    </h4>
                                    <div class="text-xs text-gray-500">
                                        <i class="fas fa-eye mr-1"></i><?= number_format($popular['views'] ?? 0) ?>
                                        <span class="mx-1">•</span>
                                        <?= date('m-d', strtotime($popular['published_at'] ?? 'now')) ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- 新聞分類 -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h3 class="text-lg font-bold text-gray-900 mb-4">
                    <i class="fas fa-tags text-blue-500 mr-2"></i>新聞分類
                </h3>
                <div class="space-y-2">
                    <a href="/news" class="block px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md <?= empty($category) ? 'bg-blue-50 text-blue-700' : '' ?>">
                        所有新聞
                    </a>
                    <a href="/news?category=ai" class="block px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md <?= $category === 'ai' ? 'bg-blue-50 text-blue-700' : '' ?>">
                        人工智慧
                    </a>
                    <a href="/news?category=blockchain" class="block px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md <?= $category === 'blockchain' ? 'bg-blue-50 text-blue-700' : '' ?>">
                        區塊鏈
                    </a>
                    <a href="/news?category=mobile" class="block px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md <?= $category === 'mobile' ? 'bg-blue-50 text-blue-700' : '' ?>">
                        行動科技
                    </a>
                    <a href="/news?category=web" class="block px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md <?= $category === 'web' ? 'bg-blue-50 text-blue-700' : '' ?>">
                        網路技術
                    </a>
                    <a href="/news?category=security" class="block px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md <?= $category === 'security' ? 'bg-blue-50 text-blue-700' : '' ?>">
                        資訊安全
                    </a>
                </div>
            </div>

            <!-- RSS 訂閱 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-bold text-gray-900 mb-4">
                    <i class="fas fa-rss text-orange-500 mr-2"></i>RSS 訂閱
                </h3>
                <p class="text-gray-600 text-sm mb-4">
                    訂閱我們的 RSS 源，即時獲取最新科技新聞。
                </p>
                <a href="/rss" 
                   class="inline-flex items-center px-4 py-2 bg-orange-500 text-white text-sm font-medium rounded-md hover:bg-orange-600 transition-colors">
                    <i class="fas fa-rss mr-2"></i>訂閱 RSS
                </a>
            </div>
        </div>
    </div>
</div>

<?php include __DIR__ . '/../layout/footer.php'; ?>