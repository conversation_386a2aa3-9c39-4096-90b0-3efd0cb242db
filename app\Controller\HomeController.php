<?php

namespace App\Controller;

use App\Service\SearchService;
use App\Service\NewsService;
use App\Service\DocumentService;

/**
 * 首頁控制器
 * 處理網站首頁相關功能
 */
class HomeController extends BaseController
{
    private SearchService $searchService;
    private NewsService $newsService;
    private DocumentService $documentService;

    public function __construct()
    {
        $this->searchService = new SearchService();
        $this->newsService = new NewsService();
        $this->documentService = new DocumentService();
    }

    /**
     * 首頁顯示
     * 
     * @return void
     */
    public function index(): void
    {
        try {
            // 取得最新科技新聞
            $latestNewsResult = $this->newsService->getLatestNews(6);
            $latestNews = $latestNewsResult['success'] ? $latestNewsResult['data'] : [];
            
            // 取得熱門檢索關鍵字
            $popularSearchesResult = $this->searchService->getPopularSearches(7, 10);
            $popularSearches = $popularSearchesResult['success'] ? $popularSearchesResult['data'] : [];
            
            // 取得熱門新聞
            $popularNewsResult = $this->newsService->getPopularNews(5, 7);
            $popularNews = $popularNewsResult['success'] ? $popularNewsResult['data'] : [];
            
            // 取得系統統計資訊
            $statistics = $this->getSystemStatistics();
            
            $this->setViewData('latestNews', $latestNews);
            $this->setViewData('popularSearches', $popularSearches);
            $this->setViewData('popularNews', $popularNews);
            $this->setViewData('statistics', $statistics);
            $this->setViewData('pageTitle', 'Tech.VG - 科技新知檢索平台');
            $this->setViewData('csrfToken', $this->generateCsrfToken());
            
            $this->render('home/index');
            
        } catch (\Exception $e) {
            $this->logError('首頁載入失敗', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            $this->setViewData('error', '系統暫時無法使用，請稍後再試');
            $this->render('error/500');
        }
    }

    /**
     * 快速檢索
     * 處理首頁的快速檢索功能
     * 
     * @return void
     */
    public function quickSearch(): void
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['error' => '不支援的請求方法'], 405);
            return;
        }

        // 驗證CSRF令牌
        if (!$this->validateCsrfToken()) {
            $this->jsonResponse(['error' => '無效的請求'], 403);
            return;
        }

        $query = trim($this->getPost('query', ''));
        
        if (empty($query)) {
            $this->jsonResponse(['error' => '請輸入檢索關鍵字'], 400);
            return;
        }

        try {
            // 執行檢索
            $searchResults = $this->searchService->search($query, [
                'limit' => 5,
                'type' => 'quick'
            ]);

            $this->jsonResponse([
                'success' => true,
                'results' => $searchResults,
                'total' => count($searchResults),
                'query' => $query
            ]);

        } catch (\Exception $e) {
            $this->logError('快速檢索失敗', [
                'query' => $query,
                'error' => $e->getMessage()
            ]);

            $this->jsonResponse(['error' => '檢索失敗，請稍後再試'], 500);
        }
    }

    /**
     * 關於頁面
     * 
     * @return void
     */
    public function about(): void
    {
        $this->setViewData('pageTitle', '關於我們 - Tech.VG');
        $this->render('home/about');
    }

    /**
     * 聯絡我們頁面
     * 
     * @return void
     */
    public function contact(): void
    {
        $this->setViewData('pageTitle', '聯絡我們 - Tech.VG');
        $this->setViewData('csrfToken', $this->generateCsrfToken());
        $this->render('home/contact');
    }

    /**
     * 處理聯絡表單提交
     * 
     * @return void
     */
    public function submitContact(): void
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/contact');
            return;
        }

        // 驗證CSRF令牌
        if (!$this->validateCsrfToken()) {
            $this->setViewData('error', '無效的請求');
            $this->contact();
            return;
        }

        $name = trim($this->getPost('name', ''));
        $email = trim($this->getPost('email', ''));
        $subject = trim($this->getPost('subject', ''));
        $message = trim($this->getPost('message', ''));

        // 驗證表單資料
        $errors = [];
        
        if (empty($name)) {
            $errors[] = '請輸入姓名';
        }
        
        if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = '請輸入有效的電子郵件';
        }
        
        if (empty($subject)) {
            $errors[] = '請輸入主題';
        }
        
        if (empty($message)) {
            $errors[] = '請輸入訊息內容';
        }

        if (!empty($errors)) {
            $this->setViewData('errors', $errors);
            $this->setViewData('formData', [
                'name' => $name,
                'email' => $email,
                'subject' => $subject,
                'message' => $message
            ]);
            $this->contact();
            return;
        }

        try {
            // 這裡可以實作發送郵件功能
            // 暫時只記錄到日誌
            $this->logError('收到聯絡表單', [
                'name' => $name,
                'email' => $email,
                'subject' => $subject,
                'message' => $message
            ]);

            $this->setViewData('success', '感謝您的來信，我們會盡快回覆');
            $this->contact();

        } catch (\Exception $e) {
            $this->logError('聯絡表單處理失敗', [
                'error' => $e->getMessage()
            ]);

            $this->setViewData('error', '發送失敗，請稍後再試');
            $this->contact();
        }
    }

    /**
     * 取得系統統計資訊
     * 
     * @return array
     */
    private function getSystemStatistics(): array
    {
        try {
            // 取得文檔統計
            $docStatsResult = $this->documentService->getStatistics();
            $docStats = $docStatsResult['success'] ? $docStatsResult['data'] : [];
            
            // 取得新聞統計
            $newsStatsResult = $this->newsService->getNewsStatistics();
            $newsStats = $newsStatsResult['success'] ? $newsStatsResult['data'] : [];
            
            // 取得檢索統計
            $searchStatsResult = $this->searchService->getSearchStatistics(30);
            $searchStats = $searchStatsResult['success'] ? $searchStatsResult['data'] : [];

            return [
                'totalDocuments' => $docStats['total'] ?? 0,
                'indexedDocuments' => $docStats['indexed'] ?? 0,
                'totalNews' => $newsStats['total_articles'] ?? 0,
                'totalSearches' => $searchStats['total_searches'] ?? 0,
                'totalWords' => $docStats['total_words'] ?? 0,
                'lastUpdateTime' => date('Y-m-d H:i:s')
            ];
        } catch (\Exception $e) {
            $this->logError('取得統計資訊失敗', [
                'error' => $e->getMessage()
            ]);

            return [
                'totalDocuments' => 0,
                'indexedDocuments' => 0,
                'totalNews' => 0,
                'totalSearches' => 0,
                'totalWords' => 0,
                'lastUpdateTime' => null
            ];
        }
    }
}