<?php

/**
 * PHPUnit 測試引導檔案
 * 設定測試環境和自動載入
 */

// 設定應用程式根目錄
define('APP_ROOT', dirname(__DIR__));

// 設定測試環境
define('TESTING', true);

// 設定錯誤報告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 設定時區
date_default_timezone_set('Asia/Taipei');

// 載入 Composer 自動載入器
if (file_exists(APP_ROOT . '/htdocs/vendor/autoload.php')) {
    require_once APP_ROOT . '/htdocs/vendor/autoload.php';
} else {
    // 如果沒有 Composer，手動載入必要的類別
    spl_autoload_register(function ($class) {
        $prefix = 'App\\';
        $baseDir = APP_ROOT . '/app/';
        
        $len = strlen($prefix);
        if (strncmp($prefix, $class, $len) !== 0) {
            return;
        }
        
        $relativeClass = substr($class, $len);
        $file = $baseDir . str_replace('\\', '/', $relativeClass) . '.php';
        
        if (file_exists($file)) {
            require $file;
        }
    });
    
    // 載入測試類別
    spl_autoload_register(function ($class) {
        $prefix = 'Tests\\';
        $baseDir = __DIR__ . '/';
        
        $len = strlen($prefix);
        if (strncmp($prefix, $class, $len) !== 0) {
            return;
        }
        
        $relativeClass = substr($class, $len);
        $file = $baseDir . str_replace('\\', '/', $relativeClass) . '.php';
        
        if (file_exists($file)) {
            require $file;
        }
    });
}

// 載入測試環境配置
if (file_exists(__DIR__ . '/.env.testing')) {
    $lines = file(__DIR__ . '/.env.testing', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            [$key, $value] = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

// 建立測試目錄
$testDirs = [
    __DIR__ . '/data',
    __DIR__ . '/uploads',
    __DIR__ . '/cache',
    __DIR__ . '/coverage',
    __DIR__ . '/results'
];

foreach ($testDirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// 設定測試資料庫
function setupTestDatabase() {
    try {
        $host = $_ENV['DB_HOST'] ?? 'localhost';
        $dbname = $_ENV['DB_DATABASE'] ?? 'techvg_test';
        $username = $_ENV['DB_USERNAME'] ?? 'test_user';
        $password = $_ENV['DB_PASSWORD'] ?? 'test_password';
        
        $dsn = "mysql:host={$host};charset=utf8mb4";
        $pdo = new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
        ]);
        
        // 建立測試資料庫（如果不存在）
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$dbname}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        
        echo "Test database setup completed.\n";
        
    } catch (PDOException $e) {
        echo "Warning: Could not setup test database: " . $e->getMessage() . "\n";
    }
}

// 清理函數
function cleanupTestEnvironment() {
    $cleanupDirs = [
        __DIR__ . '/uploads',
        __DIR__ . '/cache'
    ];
    
    foreach ($cleanupDirs as $dir) {
        if (is_dir($dir)) {
            $files = array_diff(scandir($dir), ['.', '..', '.gitkeep']);
            foreach ($files as $file) {
                $path = $dir . '/' . $file;
                if (is_file($path)) {
                    unlink($path);
                } elseif (is_dir($path)) {
                    rmdir($path);
                }
            }
        }
    }
}

// 註冊關閉時的清理函數
register_shutdown_function('cleanupTestEnvironment');

// 如果從命令行執行，設定測試資料庫
if (php_sapi_name() === 'cli') {
    setupTestDatabase();
}

// 模擬 HTTP 環境（如果需要）
if (!isset($_SERVER['HTTP_HOST'])) {
    $_SERVER['HTTP_HOST'] = 'localhost:8000';
    $_SERVER['REQUEST_URI'] = '/';
    $_SERVER['REQUEST_METHOD'] = 'GET';
    $_SERVER['SCRIPT_NAME'] = '/index.php';
}

echo "Test environment initialized.\n";