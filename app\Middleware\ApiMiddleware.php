<?php

namespace App\Middleware;

/**
 * API 中間件
 * 處理 API 請求的通用邏輯
 */
class ApiMiddleware implements MiddlewareInterface
{
    /**
     * 處理請求
     * 
     * @return void
     * @throws \Exception
     */
    public function handle(): void
    {
        // 設定 API 響應標頭
        $this->setApiHeaders();
        
        // 處理 CORS 預檢請求
        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            http_response_code(200);
            exit;
        }
        
        // 驗證 API 請求格式
        $this->validateApiRequest();
        
        // 檢查 API 金鑰（如果需要）
        $this->validateApiKey();
        
        // 檢查速率限制
        $this->checkRateLimit();
    }
    
    /**
     * 設定 API 響應標頭
     */
    private function setApiHeaders(): void
    {
        header('Content-Type: application/json; charset=utf-8');
        header('X-API-Version: v1');
        header('X-Powered-By: Tech.VG API');
        
        // CORS 設定
        if (isset($_SERVER['HTTP_ORIGIN'])) {
            $allowedOrigins = [
                'http://localhost:3000',
                'http://localhost:8000',
                'https://techvg.com',
                'https://www.techvg.com'
            ];
            
            if (in_array($_SERVER['HTTP_ORIGIN'], $allowedOrigins)) {
                header('Access-Control-Allow-Origin: ' . $_SERVER['HTTP_ORIGIN']);
            }
        }
        
        header('Access-Control-Allow-Credentials: true');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, X-API-Key');
        header('Access-Control-Max-Age: 86400'); // 24 小時
    }
    
    /**
     * 驗證 API 請求格式
     */
    private function validateApiRequest(): void
    {
        // 檢查請求方法
        $allowedMethods = ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'];
        if (!in_array($_SERVER['REQUEST_METHOD'], $allowedMethods)) {
            $this->apiError('不支援的請求方法', 405);
        }
        
        // 檢查 Content-Type（對於 POST/PUT 請求）
        if (in_array($_SERVER['REQUEST_METHOD'], ['POST', 'PUT'])) {
            $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
            
            // 允許的 Content-Type
            $allowedTypes = [
                'application/json',
                'application/x-www-form-urlencoded',
                'multipart/form-data'
            ];
            
            $isValidContentType = false;
            foreach ($allowedTypes as $type) {
                if (strpos($contentType, $type) !== false) {
                    $isValidContentType = true;
                    break;
                }
            }
            
            if (!$isValidContentType && !empty($contentType)) {
                $this->apiError('不支援的 Content-Type', 415);
            }
        }
        
        // 檢查 Accept 標頭
        $accept = $_SERVER['HTTP_ACCEPT'] ?? '';
        if (!empty($accept) && strpos($accept, 'application/json') === false && strpos($accept, '*/*') === false) {
            $this->apiError('API 只支援 JSON 格式響應', 406);
        }
    }
    
    /**
     * 驗證 API 金鑰
     */
    private function validateApiKey(): void
    {
        // 檢查是否需要 API 金鑰
        $requireApiKey = $this->isApiKeyRequired();
        
        if (!$requireApiKey) {
            return;
        }
        
        $apiKey = $_SERVER['HTTP_X_API_KEY'] ?? $_GET['api_key'] ?? null;
        
        if (empty($apiKey)) {
            $this->apiError('缺少 API 金鑰', 401, 'MISSING_API_KEY');
        }
        
        if (!$this->isValidApiKey($apiKey)) {
            $this->apiError('無效的 API 金鑰', 401, 'INVALID_API_KEY');
        }
    }
    
    /**
     * 檢查是否需要 API 金鑰
     * 
     * @return bool
     */
    private function isApiKeyRequired(): bool
    {
        $requestUri = $_SERVER['REQUEST_URI'] ?? '';
        
        // 不需要 API 金鑰的端點
        $publicEndpoints = [
            '/api/',
            '/api/health',
            '/api/search',
            '/api/news',
            '/api/stats/overview'
        ];
        
        foreach ($publicEndpoints as $endpoint) {
            if (strpos($requestUri, $endpoint) === 0) {
                return false;
            }
        }
        
        // 寫入操作需要 API 金鑰
        $writeMethods = ['POST', 'PUT', 'DELETE'];
        if (in_array($_SERVER['REQUEST_METHOD'], $writeMethods)) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 驗證 API 金鑰是否有效
     * 
     * @param string $apiKey
     * @return bool
     */
    private function isValidApiKey(string $apiKey): bool
    {
        // 這裡應該從資料庫或配置檔案中驗證 API 金鑰
        // 暫時使用簡單的驗證
        $validApiKeys = [
            'demo_key_123456',
            'test_key_789012',
            'admin_key_999999'
        ];
        
        return in_array($apiKey, $validApiKeys);
    }
    
    /**
     * 檢查速率限制
     */
    private function checkRateLimit(): void
    {
        $identifier = $this->getRateLimitIdentifier();
        $limits = $this->getRateLimits();
        
        foreach ($limits as $limit) {
            if (!$this->checkSpecificRateLimit($identifier, $limit['requests'], $limit['window'])) {
                $this->apiError(
                    "速率限制：每 {$limit['window']} 秒最多 {$limit['requests']} 次請求",
                    429,
                    'RATE_LIMIT_EXCEEDED'
                );
            }
        }
    }
    
    /**
     * 取得速率限制識別符
     * 
     * @return string
     */
    private function getRateLimitIdentifier(): string
    {
        // 優先使用 API 金鑰，否則使用 IP 地址
        $apiKey = $_SERVER['HTTP_X_API_KEY'] ?? $_GET['api_key'] ?? null;
        
        if ($apiKey) {
            return "api_key:{$apiKey}";
        }
        
        return "ip:" . $this->getClientIp();
    }
    
    /**
     * 取得速率限制配置
     * 
     * @return array
     */
    private function getRateLimits(): array
    {
        $method = $_SERVER['REQUEST_METHOD'];
        $requestUri = $_SERVER['REQUEST_URI'] ?? '';
        
        // 根據請求類型設定不同的限制
        if (in_array($method, ['POST', 'PUT', 'DELETE'])) {
            // 寫入操作更嚴格的限制
            return [
                ['requests' => 50, 'window' => 3600],  // 每小時 50 次
                ['requests' => 10, 'window' => 600]    // 每 10 分鐘 10 次
            ];
        } elseif (strpos($requestUri, '/api/search') !== false) {
            // 搜尋操作的限制
            return [
                ['requests' => 200, 'window' => 3600], // 每小時 200 次
                ['requests' => 50, 'window' => 600]    // 每 10 分鐘 50 次
            ];
        } else {
            // 一般讀取操作的限制
            return [
                ['requests' => 300, 'window' => 3600], // 每小時 300 次
                ['requests' => 100, 'window' => 600]   // 每 10 分鐘 100 次
            ];
        }
    }
    
    /**
     * 檢查特定速率限制
     * 
     * @param string $identifier
     * @param int $maxRequests
     * @param int $timeWindow
     * @return bool
     */
    private function checkSpecificRateLimit(string $identifier, int $maxRequests, int $timeWindow): bool
    {
        $cacheKey = "rate_limit:{$identifier}:{$timeWindow}";
        $cacheFile = __DIR__ . "/../../storage/cache/{$cacheKey}";
        
        // 確保快取目錄存在
        $cacheDir = dirname($cacheFile);
        if (!is_dir($cacheDir)) {
            mkdir($cacheDir, 0755, true);
        }
        
        $now = time();
        $requests = [];
        
        // 讀取現有請求記錄
        if (file_exists($cacheFile)) {
            $data = json_decode(file_get_contents($cacheFile), true);
            if ($data) {
                $requests = $data['requests'] ?? [];
            }
        }
        
        // 清理過期的請求記錄
        $requests = array_filter($requests, function($timestamp) use ($now, $timeWindow) {
            return ($now - $timestamp) < $timeWindow;
        });
        
        // 檢查是否超過限制
        if (count($requests) >= $maxRequests) {
            return false;
        }
        
        // 記錄當前請求
        $requests[] = $now;
        
        // 儲存更新後的記錄
        file_put_contents($cacheFile, json_encode([
            'requests' => $requests,
            'updated_at' => $now
        ]));
        
        return true;
    }
    
    /**
     * 取得客戶端 IP
     * 
     * @return string
     */
    private function getClientIp(): string
    {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * API 錯誤響應
     * 
     * @param string $message
     * @param int $statusCode
     * @param string|null $errorCode
     */
    private function apiError(string $message, int $statusCode = 400, ?string $errorCode = null): void
    {
        http_response_code($statusCode);
        
        $response = [
            'success' => false,
            'message' => $message,
            'timestamp' => date('c'),
            'api_version' => 'v1'
        ];
        
        if ($errorCode) {
            $response['error_code'] = $errorCode;
        }
        
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }
}