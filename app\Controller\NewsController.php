<?php

namespace App\Controller;

use App\Service\NewsService;

/**
 * 新聞控制器
 * 處理新聞瀏覽和展示功能
 */
class NewsController extends BaseController
{
    private NewsService $newsService;

    public function __construct()
    {
        $this->newsService = new NewsService();
    }

    /**
     * 新聞列表頁面
     * 
     * @return void
     */
    public function index(): void
    {
        try {
            $page = max(1, (int)$this->getGet('page', 1));
            $category = $this->getGet('category', '');
            $sourceId = $this->getGet('source', '');
            $dateFrom = $this->getGet('date_from', '');
            $dateTo = $this->getGet('date_to', '');

            // 建立過濾條件
            $filters = [];
            if (!empty($sourceId)) {
                $filters['source_id'] = $sourceId;
            }
            if (!empty($dateFrom)) {
                $filters['date_from'] = $dateFrom;
            }
            if (!empty($dateTo)) {
                $filters['date_to'] = $dateTo;
            }

            // 取得新聞列表
            $result = $this->newsService->getLatestNews(20, $filters);
            
            if ($result['success']) {
                $this->setViewData('news', $result['data']);
            } else {
                $this->setViewData('news', []);
                $this->setViewData('error', $result['message']);
            }

            // 取得熱門新聞
            $popularResult = $this->newsService->getPopularNews(5, 7);
            $this->setViewData('popularNews', $popularResult['success'] ? $popularResult['data'] : []);

            $this->setViewData('currentPage', $page);
            $this->setViewData('category', $category);
            $this->setViewData('sourceId', $sourceId);
            $this->setViewData('dateFrom', $dateFrom);
            $this->setViewData('dateTo', $dateTo);
            $this->setViewData('pageTitle', '科技新聞 - Tech.VG');

            $this->render('news/index');

        } catch (\Exception $e) {
            $this->logError('新聞列表載入失敗', [
                'error' => $e->getMessage()
            ]);

            $this->setViewData('error', '無法載入新聞列表');
            $this->render('error/500');
        }
    }

    /**
     * 新聞詳情頁面
     * 
     * @param int $id 新聞ID
     * @return void
     */
    public function show(int $id): void
    {
        try {
            $result = $this->newsService->getNewsDetail($id, true);
            
            if (!$result['success']) {
                $this->setViewData('error', $result['message']);
                $this->render('error/404');
                return;
            }

            $news = $result['data'];
            
            $this->setViewData('news', $news);
            $this->setViewData('pageTitle', $news['title'] . ' - Tech.VG');
            
            $this->render('news/show');

        } catch (\Exception $e) {
            $this->logError('新聞詳情載入失敗', [
                'newsId' => $id,
                'error' => $e->getMessage()
            ]);

            $this->setViewData('error', '無法載入新聞詳情');
            $this->render('error/500');
        }
    }

    /**
     * 分類新聞頁面
     * 
     * @param string $category 分類名稱
     * @return void
     */
    public function category(string $category): void
    {
        try {
            $page = max(1, (int)$this->getGet('page', 1));
            
            // 取得分類新聞
            $filters = ['category' => $category];
            $result = $this->newsService->getLatestNews(20, $filters);
            
            if ($result['success']) {
                $this->setViewData('news', $result['data']);
            } else {
                $this->setViewData('news', []);
                $this->setViewData('error', $result['message']);
            }

            $this->setViewData('currentPage', $page);
            $this->setViewData('category', $category);
            $this->setViewData('pageTitle', "分類: {$category} - Tech.VG");

            $this->render('news/category');

        } catch (\Exception $e) {
            $this->logError('分類新聞載入失敗', [
                'category' => $category,
                'error' => $e->getMessage()
            ]);

            $this->setViewData('error', '無法載入分類新聞');
            $this->render('error/500');
        }
    }

    /**
     * 新聞源頁面
     * 
     * @param int $sourceId 新聞源ID
     * @return void
     */
    public function source(int $sourceId): void
    {
        try {
            $page = max(1, (int)$this->getGet('page', 1));
            
            // 取得新聞源的新聞
            $filters = ['source_id' => $sourceId];
            $result = $this->newsService->getLatestNews(20, $filters);
            
            if ($result['success']) {
                $this->setViewData('news', $result['data']);
                
                // 取得新聞源資訊
                if (!empty($result['data'])) {
                    $sourceName = $result['data'][0]['source_name'] ?? '未知來源';
                    $this->setViewData('sourceName', $sourceName);
                    $this->setViewData('pageTitle', "來源: {$sourceName} - Tech.VG");
                }
            } else {
                $this->setViewData('news', []);
                $this->setViewData('error', $result['message']);
            }

            $this->setViewData('currentPage', $page);
            $this->setViewData('sourceId', $sourceId);

            $this->render('news/source');

        } catch (\Exception $e) {
            $this->logError('新聞源頁面載入失敗', [
                'sourceId' => $sourceId,
                'error' => $e->getMessage()
            ]);

            $this->setViewData('error', '無法載入新聞源頁面');
            $this->render('error/500');
        }
    }

    /**
     * 新聞RSS訂閱
     * 
     * @return void
     */
    public function rss(): void
    {
        try {
            $result = $this->newsService->getLatestNews(50);
            
            if (!$result['success']) {
                http_response_code(500);
                echo 'RSS生成失敗';
                return;
            }

            $news = $result['data'];
            
            header('Content-Type: application/rss+xml; charset=utf-8');
            
            echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
            echo '<rss version="2.0">' . "\n";
            echo '<channel>' . "\n";
            echo '<title>Tech.VG 科技新聞</title>' . "\n";
            echo '<link>' . url('/news') . '</link>' . "\n";
            echo '<description>最新科技新聞和資訊</description>' . "\n";
            echo '<language>zh-TW</language>' . "\n";
            echo '<lastBuildDate>' . date('r') . '</lastBuildDate>' . "\n";
            
            foreach ($news as $item) {
                echo '<item>' . "\n";
                echo '<title><![CDATA[' . $item['title'] . ']]></title>' . "\n";
                echo '<link>' . url("/news/{$item['id']}") . '</link>' . "\n";
                echo '<description><![CDATA[' . ($item['summary'] ?? '') . ']]></description>' . "\n";
                echo '<pubDate>' . date('r', strtotime($item['publishedAt'])) . '</pubDate>' . "\n";
                echo '<guid>' . url("/news/{$item['id']}") . '</guid>' . "\n";
                echo '</item>' . "\n";
            }
            
            echo '</channel>' . "\n";
            echo '</rss>' . "\n";

        } catch (\Exception $e) {
            $this->logError('RSS生成失敗', [
                'error' => $e->getMessage()
            ]);

            http_response_code(500);
            echo 'RSS生成失敗';
        }
    }

    /**
     * AJAX載入更多新聞
     * 
     * @return void
     */
    public function loadMore(): void
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['error' => '不支援的請求方法'], 405);
            return;
        }

        $page = max(1, (int)$this->getPost('page', 1));
        $category = $this->getPost('category', '');
        $sourceId = $this->getPost('source_id', '');

        try {
            $filters = [];
            if (!empty($category)) {
                $filters['category'] = $category;
            }
            if (!empty($sourceId)) {
                $filters['source_id'] = $sourceId;
            }

            $result = $this->newsService->getLatestNews(10, $filters);
            
            if ($result['success']) {
                $this->jsonResponse([
                    'success' => true,
                    'news' => $result['data'],
                    'hasMore' => count($result['data']) === 10
                ]);
            } else {
                $this->jsonResponse(['error' => $result['message']], 400);
            }

        } catch (\Exception $e) {
            $this->logError('載入更多新聞失敗', [
                'page' => $page,
                'category' => $category,
                'sourceId' => $sourceId,
                'error' => $e->getMessage()
            ]);

            $this->jsonResponse(['error' => '載入失敗'], 500);
        }
    }
}