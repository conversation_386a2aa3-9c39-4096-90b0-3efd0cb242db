<?php

namespace App\Controller\Api;

use App\Service\ApiCodeGenerator;

/**
 * API 文檔控制器
 * 生成和顯示 API 文檔
 */
class DocsController extends BaseApiController
{
    /**
     * API 文檔首頁
     * 
     * GET /api/docs
     */
    public function index(): void
    {
        // 檢查是否請求 HTML 格式
        $acceptHeader = $_SERVER['HTTP_ACCEPT'] ?? '';
        $format = $_GET['format'] ?? '';
        
        if ($format === 'html' || strpos($acceptHeader, 'text/html') !== false) {
            $this->showHtmlDocs();
            return;
        }
        
        $docs = $this->generateApiDocs();
        
        $this->successResponse([
            'title' => 'Tech.VG API Documentation',
            'version' => 'v1',
            'base_url' => $this->getBaseUrl(),
            'endpoints' => $docs
        ], 'API 文檔取得成功');
    }

    /**
     * 顯示 HTML 格式的 API 文檔
     */
    public function showHtmlDocs(): void
    {
        $docs = $this->generateApiDocs();
        
        $data = [
            'title' => 'Tech.VG API Documentation',
            'version' => 'v1',
            'base_url' => $this->getBaseUrl(),
            'endpoints' => $docs
        ];
        
        // 載入 HTML 模板
        include __DIR__ . '/../../View/api/docs.php';
        exit;
    }

    /**
     * OpenAPI 規格
     * 
     * GET /api/docs/openapi
     */
    public function openapi(): void
    {
        $openapi = $this->generateOpenApiSpec();
        
        header('Content-Type: application/json');
        echo json_encode($openapi, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        exit;
    }

    /**
     * 生成代碼範例
     * 
     * GET /api/docs/code
     */
    public function generateCode(): void
    {
        $language = $_GET['language'] ?? 'curl';
        $method = $_GET['method'] ?? 'GET';
        $endpoint = $_GET['endpoint'] ?? '/search';
        $params = $_GET['params'] ?? [];
        $headers = $_GET['headers'] ?? [];
        $body = $_GET['body'] ?? '';
        
        try {
            $generator = new ApiCodeGenerator();
            $url = $this->getBaseUrl() . '/api' . $endpoint;
            
            // 解析參數
            if (is_string($params)) {
                parse_str($params, $params);
            }
            
            // 解析標頭
            if (is_string($headers)) {
                $headerLines = explode("\n", $headers);
                $headers = [];
                foreach ($headerLines as $line) {
                    if (strpos($line, ':') !== false) {
                        list($name, $value) = explode(':', $line, 2);
                        $headers[trim($name)] = trim($value);
                    }
                }
            }
            
            $code = $generator->generateCode($language, $method, $url, $params, $headers, $body);
            
            $this->successResponse([
                'language' => $language,
                'code' => $code,
                'supported_languages' => $generator->getSupportedLanguages()
            ], '代碼範例生成成功');
            
        } catch (\Exception $e) {
            $this->errorResponse('代碼生成失敗: ' . $e->getMessage(), 400);
        }
    }

    /**
     * 生成 API 文檔
     * 
     * @return array
     */
    private function generateApiDocs(): array
    {
        return [
            'search' => [
                'title' => '搜尋 API',
                'description' => '提供文檔和新聞的搜尋功能',
                'endpoints' => [
                    [
                        'method' => 'GET/POST',
                        'path' => '/api/search',
                        'description' => '執行搜尋',
                        'parameters' => [
                            'q' => ['type' => 'string', 'required' => true, 'description' => '搜尋關鍵字'],
                            'type' => ['type' => 'string', 'required' => false, 'description' => '搜尋類型 (all, document, news)'],
                            'page' => ['type' => 'integer', 'required' => false, 'description' => '頁碼'],
                            'per_page' => ['type' => 'integer', 'required' => false, 'description' => '每頁項目數'],
                            'sort_by' => ['type' => 'string', 'required' => false, 'description' => '排序欄位'],
                            'sort_direction' => ['type' => 'string', 'required' => false, 'description' => '排序方向']
                        ]
                    ],
                    [
                        'method' => 'GET',
                        'path' => '/api/search/suggestions',
                        'description' => '取得搜尋建議',
                        'parameters' => [
                            'q' => ['type' => 'string', 'required' => true, 'description' => '搜尋關鍵字'],
                            'limit' => ['type' => 'integer', 'required' => false, 'description' => '建議數量限制']
                        ]
                    ],
                    [
                        'method' => 'GET',
                        'path' => '/api/search/popular',
                        'description' => '取得熱門搜尋',
                        'parameters' => [
                            'days' => ['type' => 'integer', 'required' => false, 'description' => '統計天數'],
                            'limit' => ['type' => 'integer', 'required' => false, 'description' => '結果數量限制']
                        ]
                    ]
                ]
            ],
            'documents' => [
                'title' => '文檔 API',
                'description' => '文檔管理和檢索功能',
                'endpoints' => [
                    [
                        'method' => 'GET',
                        'path' => '/api/documents',
                        'description' => '取得文檔列表',
                        'parameters' => [
                            'page' => ['type' => 'integer', 'required' => false, 'description' => '頁碼'],
                            'per_page' => ['type' => 'integer', 'required' => false, 'description' => '每頁項目數'],
                            'status' => ['type' => 'string', 'required' => false, 'description' => '狀態篩選'],
                            'file_type' => ['type' => 'string', 'required' => false, 'description' => '檔案類型篩選']
                        ]
                    ],
                    [
                        'method' => 'POST',
                        'path' => '/api/documents',
                        'description' => '上傳新文檔',
                        'parameters' => [
                            'document' => ['type' => 'file', 'required' => true, 'description' => '文檔檔案'],
                            'title' => ['type' => 'string', 'required' => false, 'description' => '文檔標題'],
                            'description' => ['type' => 'string', 'required' => false, 'description' => '文檔描述']
                        ]
                    ],
                    [
                        'method' => 'GET',
                        'path' => '/api/documents/{id}',
                        'description' => '取得文檔詳情',
                        'parameters' => [
                            'include_content' => ['type' => 'boolean', 'required' => false, 'description' => '是否包含文檔內容']
                        ]
                    ],
                    [
                        'method' => 'PUT',
                        'path' => '/api/documents/{id}',
                        'description' => '更新文檔',
                        'parameters' => [
                            'title' => ['type' => 'string', 'required' => false, 'description' => '文檔標題'],
                            'description' => ['type' => 'string', 'required' => false, 'description' => '文檔描述'],
                            'status' => ['type' => 'string', 'required' => false, 'description' => '文檔狀態']
                        ]
                    ],
                    [
                        'method' => 'DELETE',
                        'path' => '/api/documents/{id}',
                        'description' => '刪除文檔'
                    ]
                ]
            ],
            'news' => [
                'title' => '新聞 API',
                'description' => '新聞瀏覽和檢索功能',
                'endpoints' => [
                    [
                        'method' => 'GET',
                        'path' => '/api/news',
                        'description' => '取得新聞列表',
                        'parameters' => [
                            'page' => ['type' => 'integer', 'required' => false, 'description' => '頁碼'],
                            'per_page' => ['type' => 'integer', 'required' => false, 'description' => '每頁項目數'],
                            'category' => ['type' => 'string', 'required' => false, 'description' => '分類篩選'],
                            'source_id' => ['type' => 'integer', 'required' => false, 'description' => '新聞源篩選']
                        ]
                    ],
                    [
                        'method' => 'GET',
                        'path' => '/api/news/{id}',
                        'description' => '取得新聞詳情',
                        'parameters' => [
                            'include_content' => ['type' => 'boolean', 'required' => false, 'description' => '是否包含完整內容'],
                            'increment_views' => ['type' => 'boolean', 'required' => false, 'description' => '是否增加瀏覽次數']
                        ]
                    ],
                    [
                        'method' => 'GET',
                        'path' => '/api/news/latest',
                        'description' => '取得最新新聞',
                        'parameters' => [
                            'limit' => ['type' => 'integer', 'required' => false, 'description' => '數量限制'],
                            'category' => ['type' => 'string', 'required' => false, 'description' => '分類篩選']
                        ]
                    ],
                    [
                        'method' => 'GET',
                        'path' => '/api/news/popular',
                        'description' => '取得熱門新聞',
                        'parameters' => [
                            'limit' => ['type' => 'integer', 'required' => false, 'description' => '數量限制'],
                            'days' => ['type' => 'integer', 'required' => false, 'description' => '統計天數']
                        ]
                    ]
                ]
            ],
            'stats' => [
                'title' => '統計 API',
                'description' => '系統統計和分析功能',
                'endpoints' => [
                    [
                        'method' => 'GET',
                        'path' => '/api/stats/overview',
                        'description' => '取得系統總覽統計',
                        'parameters' => [
                            'days' => ['type' => 'integer', 'required' => false, 'description' => '統計天數']
                        ]
                    ],
                    [
                        'method' => 'GET',
                        'path' => '/api/stats/documents',
                        'description' => '取得文檔統計',
                        'parameters' => [
                            'days' => ['type' => 'integer', 'required' => false, 'description' => '統計天數'],
                            'group_by' => ['type' => 'string', 'required' => false, 'description' => '分組方式']
                        ]
                    ],
                    [
                        'method' => 'GET',
                        'path' => '/api/stats/news',
                        'description' => '取得新聞統計',
                        'parameters' => [
                            'days' => ['type' => 'integer', 'required' => false, 'description' => '統計天數'],
                            'group_by' => ['type' => 'string', 'required' => false, 'description' => '分組方式']
                        ]
                    ],
                    [
                        'method' => 'GET',
                        'path' => '/api/stats/search',
                        'description' => '取得搜尋統計',
                        'parameters' => [
                            'days' => ['type' => 'integer', 'required' => false, 'description' => '統計天數'],
                            'group_by' => ['type' => 'string', 'required' => false, 'description' => '分組方式']
                        ]
                    ]
                ]
            ]
        ];
    }

    /**
     * 生成 OpenAPI 規格
     * 
     * @return array
     */
    private function generateOpenApiSpec(): array
    {
        return [
            'openapi' => '3.0.0',
            'info' => [
                'title' => 'Tech.VG API',
                'description' => '科技新知檢索平台 API',
                'version' => 'v1',
                'contact' => [
                    'name' => 'Tech.VG Support',
                    'email' => '<EMAIL>'
                ]
            ],
            'servers' => [
                [
                    'url' => $this->getBaseUrl() . '/api',
                    'description' => 'Production server'
                ]
            ],
            'paths' => $this->generateOpenApiPaths(),
            'components' => [
                'schemas' => $this->generateOpenApiSchemas(),
                'securitySchemes' => [
                    'ApiKeyAuth' => [
                        'type' => 'apiKey',
                        'in' => 'header',
                        'name' => 'X-API-Key'
                    ]
                ]
            ]
        ];
    }

    /**
     * 生成 OpenAPI 路徑
     * 
     * @return array
     */
    private function generateOpenApiPaths(): array
    {
        return [
            '/search' => [
                'get' => [
                    'summary' => '搜尋內容',
                    'parameters' => [
                        [
                            'name' => 'q',
                            'in' => 'query',
                            'required' => true,
                            'schema' => ['type' => 'string'],
                            'description' => '搜尋關鍵字'
                        ],
                        [
                            'name' => 'type',
                            'in' => 'query',
                            'schema' => ['type' => 'string', 'enum' => ['all', 'document', 'news']],
                            'description' => '搜尋類型'
                        ]
                    ],
                    'responses' => [
                        '200' => [
                            'description' => '搜尋成功',
                            'content' => [
                                'application/json' => [
                                    'schema' => ['$ref' => '#/components/schemas/SearchResponse']
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            '/documents' => [
                'get' => [
                    'summary' => '取得文檔列表',
                    'parameters' => [
                        [
                            'name' => 'page',
                            'in' => 'query',
                            'schema' => ['type' => 'integer', 'minimum' => 1],
                            'description' => '頁碼'
                        ]
                    ],
                    'responses' => [
                        '200' => [
                            'description' => '文檔列表',
                            'content' => [
                                'application/json' => [
                                    'schema' => ['$ref' => '#/components/schemas/DocumentListResponse']
                                ]
                            ]
                        ]
                    ]
                ],
                'post' => [
                    'summary' => '上傳文檔',
                    'requestBody' => [
                        'content' => [
                            'multipart/form-data' => [
                                'schema' => [
                                    'type' => 'object',
                                    'properties' => [
                                        'document' => ['type' => 'string', 'format' => 'binary'],
                                        'title' => ['type' => 'string']
                                    ],
                                    'required' => ['document']
                                ]
                            ]
                        ]
                    ],
                    'responses' => [
                        '201' => [
                            'description' => '文檔上傳成功',
                            'content' => [
                                'application/json' => [
                                    'schema' => ['$ref' => '#/components/schemas/DocumentResponse']
                                ]
                            ]
                        ]
                    ],
                    'security' => [['ApiKeyAuth' => []]]
                ]
            ]
        ];
    }

    /**
     * 生成 OpenAPI 模式
     * 
     * @return array
     */
    private function generateOpenApiSchemas(): array
    {
        return [
            'ApiResponse' => [
                'type' => 'object',
                'properties' => [
                    'success' => ['type' => 'boolean'],
                    'message' => ['type' => 'string'],
                    'data' => ['type' => 'object'],
                    'timestamp' => ['type' => 'string', 'format' => 'date-time'],
                    'api_version' => ['type' => 'string']
                ]
            ],
            'SearchResponse' => [
                'allOf' => [
                    ['$ref' => '#/components/schemas/ApiResponse'],
                    [
                        'type' => 'object',
                        'properties' => [
                            'data' => [
                                'type' => 'object',
                                'properties' => [
                                    'query' => ['type' => 'string'],
                                    'results' => [
                                        'type' => 'array',
                                        'items' => ['$ref' => '#/components/schemas/SearchResult']
                                    ],
                                    'search_time' => ['type' => 'number']
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            'SearchResult' => [
                'type' => 'object',
                'properties' => [
                    'id' => ['type' => 'integer'],
                    'title' => ['type' => 'string'],
                    'type' => ['type' => 'string', 'enum' => ['document', 'news']],
                    'summary' => ['type' => 'string'],
                    'relevance' => ['type' => 'number']
                ]
            ],
            'DocumentListResponse' => [
                'allOf' => [
                    ['$ref' => '#/components/schemas/ApiResponse'],
                    [
                        'type' => 'object',
                        'properties' => [
                            'data' => [
                                'type' => 'array',
                                'items' => ['$ref' => '#/components/schemas/Document']
                            ],
                            'meta' => ['$ref' => '#/components/schemas/PaginationMeta']
                        ]
                    ]
                ]
            ],
            'DocumentResponse' => [
                'allOf' => [
                    ['$ref' => '#/components/schemas/ApiResponse'],
                    [
                        'type' => 'object',
                        'properties' => [
                            'data' => ['$ref' => '#/components/schemas/Document']
                        ]
                    ]
                ]
            ],
            'Document' => [
                'type' => 'object',
                'properties' => [
                    'id' => ['type' => 'integer'],
                    'title' => ['type' => 'string'],
                    'filename' => ['type' => 'string'],
                    'file_type' => ['type' => 'string'],
                    'file_size' => ['type' => 'integer'],
                    'status' => ['type' => 'string'],
                    'created_at' => ['type' => 'string', 'format' => 'date-time'],
                    'updated_at' => ['type' => 'string', 'format' => 'date-time']
                ]
            ],
            'PaginationMeta' => [
                'type' => 'object',
                'properties' => [
                    'pagination' => [
                        'type' => 'object',
                        'properties' => [
                            'current_page' => ['type' => 'integer'],
                            'per_page' => ['type' => 'integer'],
                            'total_items' => ['type' => 'integer'],
                            'total_pages' => ['type' => 'integer'],
                            'has_next_page' => ['type' => 'boolean'],
                            'has_prev_page' => ['type' => 'boolean']
                        ]
                    ]
                ]
            ]
        ];
    }

    /**
     * 取得基礎 URL
     * 
     * @return string
     */
    private function getBaseUrl(): string
    {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        return "{$protocol}://{$host}";
    }
}