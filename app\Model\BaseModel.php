<?php

namespace App\Model;

use App\Database\Database;
use Exception;

/**
 * 基礎模型類
 * 提供所有模型的共用功能和CRUD操作
 */
abstract class BaseModel
{
    protected Database $db;
    protected string $table;
    protected string $primaryKey = 'id';
    protected array $fillable = [];
    protected array $hidden = [];
    protected array $casts = [];
    
    public function __construct()
    {
        $this->db = Database::getInstance();
        
        if (empty($this->table)) {
            // 自動推斷表名 (類名轉為小寫)
            $className = (new \ReflectionClass($this))->getShortName();
            $this->table = strtolower(preg_replace('/(?<!^)[A-Z]/', '_$0', str_replace('Model', '', $className)));
        }
    }

    /**
     * 查找單一記錄
     * 
     * @param mixed $id 主鍵值
     * @return array|null
     */
    public function find($id): ?array
    {
        $sql = "SELECT * FROM {$this->table} WHERE {$this->primaryKey} = ? LIMIT 1";
        $result = $this->db->fetchOne($sql, [$id]);
        
        return $result ? $this->castAttributes($result) : null;
    }

    /**
     * 查找所有記錄
     * 
     * @param array $conditions 查詢條件
     * @param string $orderBy 排序
     * @param int|null $limit 限制數量
     * @param int $offset 偏移量
     * @return array
     */
    public function findAll(array $conditions = [], string $orderBy = '', ?int $limit = null, int $offset = 0): array
    {
        $sql = "SELECT * FROM {$this->table}";
        $params = [];
        
        if (!empty($conditions)) {
            $whereClause = $this->buildWhereClause($conditions, $params);
            $sql .= " WHERE {$whereClause}";
        }
        
        if (!empty($orderBy)) {
            $sql .= " ORDER BY {$orderBy}";
        }
        
        if ($limit !== null) {
            $sql .= " LIMIT {$limit}";
            if ($offset > 0) {
                $sql .= " OFFSET {$offset}";
            }
        }
        
        $results = $this->db->fetchAll($sql, $params);
        
        return array_map([$this, 'castAttributes'], $results);
    }

    /**
     * 查找第一筆記錄
     * 
     * @param array $conditions
     * @param string $orderBy
     * @return array|null
     */
    public function findFirst(array $conditions = [], string $orderBy = ''): ?array
    {
        $results = $this->findAll($conditions, $orderBy, 1);
        return !empty($results) ? $results[0] : null;
    }

    /**
     * 計算記錄數量
     * 
     * @param array $conditions
     * @return int
     */
    public function count(array $conditions = []): int
    {
        $sql = "SELECT COUNT(*) as count FROM {$this->table}";
        $params = [];
        
        if (!empty($conditions)) {
            $whereClause = $this->buildWhereClause($conditions, $params);
            $sql .= " WHERE {$whereClause}";
        }
        
        $result = $this->db->fetchOne($sql, $params);
        return (int) ($result['count'] ?? 0);
    }

    /**
     * 插入新記錄
     * 
     * @param array $data
     * @return int 新插入記錄的ID
     * @throws Exception
     */
    public function create(array $data): int
    {
        $data = $this->filterFillable($data);
        $data = $this->addTimestamps($data, true);
        
        if (empty($data)) {
            throw new Exception('沒有可插入的資料');
        }
        
        $columns = array_keys($data);
        $placeholders = array_fill(0, count($columns), '?');
        
        $sql = "INSERT INTO {$this->table} (" . implode(', ', $columns) . ") VALUES (" . implode(', ', $placeholders) . ")";
        
        return $this->db->insert($sql, array_values($data));
    }

    /**
     * 更新記錄
     * 
     * @param mixed $id 主鍵值
     * @param array $data 更新資料
     * @return int 影響的行數
     * @throws Exception
     */
    public function update($id, array $data): int
    {
        $data = $this->filterFillable($data);
        $data = $this->addTimestamps($data, false);
        
        if (empty($data)) {
            throw new Exception('沒有可更新的資料');
        }
        
        $setClause = implode(', ', array_map(fn($col) => "{$col} = ?", array_keys($data)));
        $sql = "UPDATE {$this->table} SET {$setClause} WHERE {$this->primaryKey} = ?";
        
        $params = array_values($data);
        $params[] = $id;
        
        return $this->db->update($sql, $params);
    }

    /**
     * 刪除記錄
     * 
     * @param mixed $id 主鍵值
     * @return int 影響的行數
     */
    public function delete($id): int
    {
        $sql = "DELETE FROM {$this->table} WHERE {$this->primaryKey} = ?";
        return $this->db->delete($sql, [$id]);
    }

    /**
     * 批量刪除
     * 
     * @param array $conditions
     * @return int 影響的行數
     */
    public function deleteWhere(array $conditions): int
    {
        if (empty($conditions)) {
            throw new Exception('批量刪除必須提供條件');
        }
        
        $params = [];
        $whereClause = $this->buildWhereClause($conditions, $params);
        $sql = "DELETE FROM {$this->table} WHERE {$whereClause}";
        
        return $this->db->delete($sql, $params);
    }

    /**
     * 執行原始SQL查詢
     * 
     * @param string $sql
     * @param array $params
     * @return array
     */
    public function raw(string $sql, array $params = []): array
    {
        return $this->db->fetchAll($sql, $params);
    }

    /**
     * 分頁查詢
     * 
     * @param int $page 頁碼
     * @param int $perPage 每頁數量
     * @param array $conditions 查詢條件
     * @param string $orderBy 排序
     * @return array
     */
    public function paginate(int $page = 1, int $perPage = 20, array $conditions = [], string $orderBy = ''): array
    {
        $page = max(1, $page);
        $offset = ($page - 1) * $perPage;
        
        $total = $this->count($conditions);
        $data = $this->findAll($conditions, $orderBy, $perPage, $offset);
        
        return [
            'data' => $data,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $total,
                'total_pages' => ceil($total / $perPage),
                'has_next' => $page < ceil($total / $perPage),
                'has_prev' => $page > 1
            ]
        ];
    }

    /**
     * 建立WHERE子句
     * 
     * @param array $conditions
     * @param array &$params
     * @return string
     */
    protected function buildWhereClause(array $conditions, array &$params): string
    {
        $clauses = [];
        
        foreach ($conditions as $key => $value) {
            if (is_array($value)) {
                // IN 查詢
                $placeholders = array_fill(0, count($value), '?');
                $clauses[] = "{$key} IN (" . implode(', ', $placeholders) . ")";
                $params = array_merge($params, $value);
            } elseif (strpos($key, ' ') !== false) {
                // 包含操作符的條件 (例如: 'age >' => 18)
                $clauses[] = $key . ' ?';
                $params[] = $value;
            } else {
                // 等於查詢
                $clauses[] = "{$key} = ?";
                $params[] = $value;
            }
        }
        
        return implode(' AND ', $clauses);
    }

    /**
     * 過濾可填充欄位
     * 
     * @param array $data
     * @return array
     */
    protected function filterFillable(array $data): array
    {
        if (empty($this->fillable)) {
            return $data;
        }
        
        return array_intersect_key($data, array_flip($this->fillable));
    }

    /**
     * 添加時間戳
     * 
     * @param array $data
     * @param bool $isCreate
     * @return array
     */
    protected function addTimestamps(array $data, bool $isCreate = false): array
    {
        $now = date('Y-m-d H:i:s');
        
        if ($isCreate && !isset($data['createdAt'])) {
            $data['createdAt'] = $now;
        }
        
        if (!isset($data['updatedAt'])) {
            $data['updatedAt'] = $now;
        }
        
        return $data;
    }

    /**
     * 類型轉換
     * 
     * @param array $attributes
     * @return array
     */
    protected function castAttributes(array $attributes): array
    {
        foreach ($this->casts as $key => $type) {
            if (!isset($attributes[$key])) {
                continue;
            }
            
            switch ($type) {
                case 'int':
                case 'integer':
                    $attributes[$key] = (int) $attributes[$key];
                    break;
                case 'float':
                case 'double':
                    $attributes[$key] = (float) $attributes[$key];
                    break;
                case 'bool':
                case 'boolean':
                    $attributes[$key] = (bool) $attributes[$key];
                    break;
                case 'array':
                case 'json':
                    $attributes[$key] = json_decode($attributes[$key], true);
                    break;
                case 'datetime':
                    $attributes[$key] = new \DateTime($attributes[$key]);
                    break;
            }
        }
        
        // 移除隱藏欄位
        foreach ($this->hidden as $field) {
            unset($attributes[$field]);
        }
        
        return $attributes;
    }

    /**
     * 開始資料庫交易
     */
    public function beginTransaction(): void
    {
        $this->db->beginTransaction();
    }

    /**
     * 提交交易
     */
    public function commit(): void
    {
        $this->db->commit();
    }

    /**
     * 回滾交易
     */
    public function rollback(): void
    {
        $this->db->rollback();
    }
}