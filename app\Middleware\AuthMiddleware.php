<?php

namespace App\Middleware;

/**
 * 認證中間件
 * 檢查用戶是否已登入
 */
class AuthMiddleware implements MiddlewareInterface
{
    /**
     * 處理請求
     * 
     * @param callable $next
     * @return mixed
     */
    public function handle(callable $next)
    {
        // 檢查會話是否已啟動
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // 檢查用戶是否已登入
        if (!isset($_SESSION['user_id']) || empty($_SESSION['user_id'])) {
            // 保存原本要訪問的 URL
            $_SESSION['intended_url'] = $_SERVER['REQUEST_URI'] ?? '/';
            
            // 設定錯誤訊息
            $_SESSION['flash']['error'] = '請先登入才能訪問此頁面';
            
            // 重定向到登入頁面
            header('Location: /auth/login');
            exit;
        }

        // 檢查會話是否過期（24小時）
        $sessionTimeout = 24 * 60 * 60; // 24小時
        if (isset($_SESSION['login_time']) && (time() - $_SESSION['login_time']) > $sessionTimeout) {
            // 清除會話
            session_destroy();
            
            // 設定錯誤訊息
            session_start();
            $_SESSION['flash']['error'] = '會話已過期，請重新登入';
            
            // 重定向到登入頁面
            header('Location: /auth/login');
            exit;
        }

        // 更新最後活動時間
        $_SESSION['last_activity'] = time();

        // 繼續處理請求
        return $next();
    }
}