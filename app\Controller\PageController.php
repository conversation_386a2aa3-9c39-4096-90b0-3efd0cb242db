<?php

namespace App\Controller;

/**
 * 靜態頁面控制器
 * 處理關於我們、聯絡我們等靜態頁面
 */
class PageController extends BaseController
{
    /**
     * 關於我們頁面
     */
    public function about(): void
    {
        $this->setViewData('pageTitle', '關於我們 - Tech.VG 科技新知檢索');
        $this->setViewData('currentPage', 'about');
        
        $this->render('home/about');
    }

    /**
     * 聯絡我們頁面
     */
    public function contact(): void
    {
        $this->setViewData('pageTitle', '聯絡我們 - Tech.VG 科技新知檢索');
        $this->setViewData('currentPage', 'contact');
        
        $this->render('home/contact');
    }

    /**
     * 處理聯絡表單提交
     */
    public function contactSubmit(): void
    {
        try {
            // 驗證 CSRF 令牌
            if (!$this->validateCsrfToken()) {
                throw new \Exception('無效的請求');
            }

            // 取得表單資料
            $name = $this->getPost('name');
            $email = $this->getPost('email');
            $subject = $this->getPost('subject');
            $message = $this->getPost('message');

            // 驗證必要欄位
            if (empty($name) || empty($email) || empty($subject) || empty($message)) {
                throw new \Exception('請填寫所有必要欄位');
            }

            // 驗證電子郵件格式
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                throw new \Exception('請輸入有效的電子郵件地址');
            }

            // 記錄聯絡訊息到日誌
            $contactData = [
                'timestamp' => date('Y-m-d H:i:s'),
                'name' => sanitize($name),
                'email' => sanitize($email),
                'subject' => sanitize($subject),
                'message' => sanitize($message),
                'ip' => $_SERVER['REMOTE_ADDR'] ?? '',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
            ];

            $logFile = APP_ROOT . '/storage/logs/contact.log';
            $logDir = dirname($logFile);
            
            if (!is_dir($logDir)) {
                mkdir($logDir, 0755, true);
            }

            file_put_contents($logFile, json_encode($contactData, JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND | LOCK_EX);

            // 設定成功訊息
            flash('success', '感謝您的聯絡！我們會盡快回覆您。');
            
            // 重定向回聯絡頁面
            $this->redirect('/contact');

        } catch (\Exception $e) {
            // 設定錯誤訊息
            flash('error', $e->getMessage());
            
            // 保存舊的輸入值
            $_SESSION['old_input'] = [
                'name' => $this->getPost('name'),
                'email' => $this->getPost('email'),
                'subject' => $this->getPost('subject'),
                'message' => $this->getPost('message')
            ];
            
            // 重定向回聯絡頁面
            $this->redirect('/contact');
        }
    }

    /**
     * 隱私政策頁面
     */
    public function privacy(): void
    {
        $this->setViewData('pageTitle', '隱私政策 - Tech.VG 科技新知檢索');
        $this->setViewData('currentPage', 'privacy');
        
        $this->render('pages/privacy');
    }

    /**
     * 服務條款頁面
     */
    public function terms(): void
    {
        $this->setViewData('pageTitle', '服務條款 - Tech.VG 科技新知檢索');
        $this->setViewData('currentPage', 'terms');
        
        $this->render('pages/terms');
    }
}