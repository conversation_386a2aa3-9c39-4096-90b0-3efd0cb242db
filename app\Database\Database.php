<?php

namespace App\Database;

use PDO;
use PDOException;
use Exception;

/**
 * 資料庫連接管理類
 * 提供PDO連接的封裝和管理
 */
class Database
{
    private static ?Database $instance = null;
    private ?PDO $connection = null;
    private array $config;
    private array $queryLog = [];

    private function __construct()
    {
        $this->config = require __DIR__ . '/../../config/database.php';
    }

    /**
     * 取得資料庫實例 (單例模式)
     * 
     * @return Database
     */
    public static function getInstance(): Database
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        
        return self::$instance;
    }

    /**
     * 取得PDO連接
     * 
     * @param string|null $connection 連接名稱
     * @return PDO
     * @throws Exception
     */
    public function getConnection(?string $connection = null): PDO
    {
        if ($this->connection === null) {
            $this->connect($connection);
        }
        
        return $this->connection;
    }

    /**
     * 建立資料庫連接
     * 
     * @param string|null $connectionName
     * @throws Exception
     */
    private function connect(?string $connectionName = null): void
    {
        $connectionName = $connectionName ?? $this->config['default'];
        $config = $this->config['connections'][$connectionName] ?? null;
        
        if (!$config) {
            throw new Exception("資料庫連接配置不存在: {$connectionName}");
        }

        try {
            $dsn = $this->buildDsn($config);
            $this->connection = new PDO(
                $dsn,
                $config['username'],
                $config['password'],
                $config['options'] ?? []
            );
            
            // 設定時區
            $this->connection->exec("SET time_zone = '+08:00'");
            
        } catch (PDOException $e) {
            throw new Exception("資料庫連接失敗: " . $e->getMessage());
        }
    }

    /**
     * 建立DSN字串
     * 
     * @param array $config
     * @return string
     */
    private function buildDsn(array $config): string
    {
        $dsn = "{$config['driver']}:host={$config['host']};port={$config['port']};dbname={$config['database']}";
        
        if (isset($config['charset'])) {
            $dsn .= ";charset={$config['charset']}";
        }
        
        return $dsn;
    }

    /**
     * 執行查詢
     * 
     * @param string $sql
     * @param array $params
     * @return \PDOStatement
     * @throws Exception
     */
    public function query(string $sql, array $params = []): \PDOStatement
    {
        try {
            $startTime = microtime(true);
            
            $stmt = $this->getConnection()->prepare($sql);
            $stmt->execute($params);
            
            $executionTime = microtime(true) - $startTime;
            
            // 記錄查詢日誌
            if ($this->config['log_queries']) {
                $this->logQuery($sql, $params, $executionTime);
            }
            
            return $stmt;
            
        } catch (PDOException $e) {
            throw new Exception("查詢執行失敗: " . $e->getMessage() . " SQL: {$sql}");
        }
    }

    /**
     * 執行插入操作
     * 
     * @param string $sql
     * @param array $params
     * @return int 最後插入的ID
     */
    public function insert(string $sql, array $params = []): int
    {
        $this->query($sql, $params);
        return (int) $this->getConnection()->lastInsertId();
    }

    /**
     * 執行更新操作
     * 
     * @param string $sql
     * @param array $params
     * @return int 影響的行數
     */
    public function update(string $sql, array $params = []): int
    {
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }

    /**
     * 執行刪除操作
     * 
     * @param string $sql
     * @param array $params
     * @return int 影響的行數
     */
    public function delete(string $sql, array $params = []): int
    {
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }

    /**
     * 取得單一記錄
     * 
     * @param string $sql
     * @param array $params
     * @return array|null
     */
    public function fetchOne(string $sql, array $params = []): ?array
    {
        $stmt = $this->query($sql, $params);
        $result = $stmt->fetch();
        
        return $result ?: null;
    }

    /**
     * 取得多筆記錄
     * 
     * @param string $sql
     * @param array $params
     * @return array
     */
    public function fetchAll(string $sql, array $params = []): array
    {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }

    /**
     * 開始交易
     * 
     * @return bool
     */
    public function beginTransaction(): bool
    {
        return $this->getConnection()->beginTransaction();
    }

    /**
     * 提交交易
     * 
     * @return bool
     */
    public function commit(): bool
    {
        return $this->getConnection()->commit();
    }

    /**
     * 回滾交易
     * 
     * @return bool
     */
    public function rollback(): bool
    {
        return $this->getConnection()->rollback();
    }

    /**
     * 檢查是否在交易中
     * 
     * @return bool
     */
    public function inTransaction(): bool
    {
        return $this->getConnection()->inTransaction();
    }

    /**
     * 記錄查詢日誌
     * 
     * @param string $sql
     * @param array $params
     * @param float $executionTime
     */
    private function logQuery(string $sql, array $params, float $executionTime): void
    {
        $this->queryLog[] = [
            'sql' => $sql,
            'params' => $params,
            'execution_time' => $executionTime,
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }

    /**
     * 取得查詢日誌
     * 
     * @return array
     */
    public function getQueryLog(): array
    {
        return $this->queryLog;
    }

    /**
     * 清除查詢日誌
     */
    public function clearQueryLog(): void
    {
        $this->queryLog = [];
    }

    /**
     * 關閉連接
     */
    public function disconnect(): void
    {
        $this->connection = null;
    }

    /**
     * 防止複製實例
     */
    private function __clone() {}

    /**
     * 防止反序列化
     */
    public function __wakeup()
    {
        throw new Exception("Cannot unserialize singleton");
    }
}