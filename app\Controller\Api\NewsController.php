<?php

namespace App\Controller\Api;

use App\Service\NewsService;

/**
 * 新聞 API 控制器
 * 處理新聞相關的 API 請求
 */
class NewsController extends BaseApiController
{
    private NewsService $newsService;

    public function __construct()
    {
        parent::__construct();
        $this->newsService = new NewsService();
    }

    /**
     * 取得新聞列表
     * 
     * GET /api/news
     * 
     * 參數：
     * - page: 頁碼
     * - per_page: 每頁項目數
     * - sort_by: 排序欄位 (id, title, published_at, views)
     * - sort_direction: 排序方向 (asc, desc)
     * - category: 分類篩選
     * - source_id: 新聞源篩選
     * - date_from: 開始日期
     * - date_to: 結束日期
     * - search: 搜尋關鍵字
     */
    public function index(): void
    {
        try {
            // 檢查速率限制
            $clientIp = $this->getClientIp();
            if (!$this->checkRateLimit($clientIp, 300, 3600)) {
                $this->errorResponse('請求過於頻繁，請稍後再試', 429, [], 'RATE_LIMIT_EXCEEDED');
            }

            // 取得分頁參數
            $pagination = $this->getPaginationParams();
            
            // 取得排序參數
            $sort = $this->getSortParams(['id', 'title', 'published_at', 'views'], 'published_at', 'desc');
            
            // 取得篩選參數
            $filters = $this->getFilterParams(['category', 'source_id', 'date_from', 'date_to', 'search']);

            // 取得新聞列表
            $result = $this->newsService->getLatestNews($pagination['per_page'], $filters, $pagination['page'], $sort);

            if (!$result['success']) {
                $this->errorResponse($result['message'] ?? '取得新聞列表失敗', 500, [], 'NEWS_LIST_ERROR');
            }

            // 處理響應資料
            $news = array_map(function($article) {
                return $this->formatNewsResponse($article, false);
            }, $result['data'] ?? []);

            $this->paginatedResponse(
                $news,
                $result['meta']['total'] ?? 0,
                $pagination['page'],
                $pagination['per_page'],
                '新聞列表取得成功'
            );

        } catch (\Exception $e) {
            $this->errorResponse('新聞服務暫時不可用', 500, [], 'NEWS_SERVICE_ERROR');
        }
    }

    /**
     * 取得單一新聞詳情
     * 
     * GET /api/news/{id}
     * 
     * 參數：
     * - include_content: 是否包含完整內容 (true/false)
     * - increment_views: 是否增加瀏覽次數 (true/false)
     */
    public function show(int $id): void
    {
        try {
            // 檢查速率限制
            $clientIp = $this->getClientIp();
            if (!$this->checkRateLimit($clientIp, 500, 3600)) {
                $this->errorResponse('請求過於頻繁，請稍後再試', 429, [], 'RATE_LIMIT_EXCEEDED');
            }

            $includeContent = $this->getGet('include_content', 'true') === 'true';
            $incrementViews = $this->getGet('increment_views', 'true') === 'true';

            $result = $this->newsService->getNewsDetail($id, $incrementViews);

            if (!$result['success']) {
                if (strpos($result['message'], '不存在') !== false) {
                    $this->errorResponse('新聞不存在', 404, [], 'NEWS_NOT_FOUND');
                }
                $this->errorResponse($result['message'] ?? '取得新聞失敗', 500, [], 'NEWS_GET_ERROR');
            }

            $news = $this->formatNewsResponse($result['data'], $includeContent);

            $this->successResponse($news, '新聞詳情取得成功');

        } catch (\Exception $e) {
            $this->errorResponse('新聞服務暫時不可用', 500, [], 'NEWS_SERVICE_ERROR');
        }
    }

    /**
     * 取得最新新聞
     * 
     * GET /api/news/latest
     * 
     * 參數：
     * - limit: 數量限制 (預設: 10, 最大: 50)
     * - category: 分類篩選
     * - hours: 時間範圍（小時）
     */
    public function latest(): void
    {
        try {
            // 檢查速率限制
            $clientIp = $this->getClientIp();
            if (!$this->checkRateLimit($clientIp, 200, 3600)) {
                $this->errorResponse('請求過於頻繁，請稍後再試', 429, [], 'RATE_LIMIT_EXCEEDED');
            }

            $limit = min(50, max(1, (int)$this->getGet('limit', 10)));
            $category = $this->getGet('category', '');
            $hours = max(1, min(168, (int)$this->getGet('hours', 24))); // 最多7天

            $filters = [];
            if (!empty($category)) {
                $filters['category'] = $category;
            }

            // 設定時間範圍
            $filters['date_from'] = date('Y-m-d H:i:s', strtotime("-{$hours} hours"));

            $result = $this->newsService->getLatestNews($limit, $filters);

            if (!$result['success']) {
                $this->errorResponse($result['message'] ?? '取得最新新聞失敗', 500, [], 'LATEST_NEWS_ERROR');
            }

            // 處理響應資料
            $news = array_map(function($article) {
                return $this->formatNewsResponse($article, false);
            }, $result['data'] ?? []);

            $this->successResponse([
                'news' => $news,
                'filters' => [
                    'category' => $category,
                    'hours' => $hours,
                    'limit' => $limit
                ]
            ], '最新新聞取得成功');

        } catch (\Exception $e) {
            $this->errorResponse('最新新聞服務暫時不可用', 500, [], 'LATEST_NEWS_SERVICE_ERROR');
        }
    }

    /**
     * 取得熱門新聞
     * 
     * GET /api/news/popular
     * 
     * 參數：
     * - limit: 數量限制 (預設: 10, 最大: 50)
     * - days: 統計天數 (預設: 7, 最大: 30)
     * - category: 分類篩選
     */
    public function popular(): void
    {
        try {
            // 檢查速率限制
            $clientIp = $this->getClientIp();
            if (!$this->checkRateLimit($clientIp, 200, 3600)) {
                $this->errorResponse('請求過於頻繁，請稍後再試', 429, [], 'RATE_LIMIT_EXCEEDED');
            }

            $limit = min(50, max(1, (int)$this->getGet('limit', 10)));
            $days = max(1, min(30, (int)$this->getGet('days', 7)));
            $category = $this->getGet('category', '');

            $result = $this->newsService->getPopularNews($limit, $days, $category);

            if (!$result['success']) {
                $this->errorResponse($result['message'] ?? '取得熱門新聞失敗', 500, [], 'POPULAR_NEWS_ERROR');
            }

            // 處理響應資料
            $news = array_map(function($article) {
                return $this->formatNewsResponse($article, false);
            }, $result['data'] ?? []);

            $this->successResponse([
                'news' => $news,
                'filters' => [
                    'category' => $category,
                    'days' => $days,
                    'limit' => $limit
                ]
            ], '熱門新聞取得成功');

        } catch (\Exception $e) {
            $this->errorResponse('熱門新聞服務暫時不可用', 500, [], 'POPULAR_NEWS_SERVICE_ERROR');
        }
    }

    /**
     * 取得新聞分類
     * 
     * GET /api/news/categories
     */
    public function categories(): void
    {
        try {
            $result = $this->newsService->getCategories();

            if (!$result['success']) {
                $this->errorResponse($result['message'] ?? '取得新聞分類失敗', 500, [], 'NEWS_CATEGORIES_ERROR');
            }

            $this->successResponse($result['data'], '新聞分類取得成功');

        } catch (\Exception $e) {
            $this->errorResponse('新聞分類服務暫時不可用', 500, [], 'NEWS_CATEGORIES_SERVICE_ERROR');
        }
    }

    /**
     * 取得新聞源列表
     * 
     * GET /api/news/sources
     * 
     * 參數：
     * - active_only: 只取得啟用的新聞源 (true/false)
     */
    public function sources(): void
    {
        try {
            $activeOnly = $this->getGet('active_only', 'true') === 'true';

            $result = $this->newsService->getNewsSources($activeOnly);

            if (!$result['success']) {
                $this->errorResponse($result['message'] ?? '取得新聞源失敗', 500, [], 'NEWS_SOURCES_ERROR');
            }

            $this->successResponse($result['data'], '新聞源列表取得成功');

        } catch (\Exception $e) {
            $this->errorResponse('新聞源服務暫時不可用', 500, [], 'NEWS_SOURCES_SERVICE_ERROR');
        }
    }

    /**
     * 取得新聞統計
     * 
     * GET /api/news/stats
     * 
     * 參數：
     * - days: 統計天數 (預設: 30)
     */
    public function stats(): void
    {
        try {
            $days = max(1, min(365, (int)$this->getGet('days', 30)));

            $result = $this->newsService->getNewsStatistics($days);

            if (!$result['success']) {
                $this->errorResponse($result['message'] ?? '取得新聞統計失敗', 500, [], 'NEWS_STATS_ERROR');
            }

            $this->successResponse([
                'statistics' => $result['data'],
                'period_days' => $days
            ], '新聞統計取得成功');

        } catch (\Exception $e) {
            $this->errorResponse('新聞統計服務暫時不可用', 500, [], 'NEWS_STATS_SERVICE_ERROR');
        }
    }

    /**
     * 取得分類新聞
     * 
     * GET /api/news/category/{category}
     * 
     * 參數：
     * - page: 頁碼
     * - per_page: 每頁項目數
     * - sort_by: 排序欄位
     * - sort_direction: 排序方向
     */
    public function category(string $category): void
    {
        try {
            // 檢查速率限制
            $clientIp = $this->getClientIp();
            if (!$this->checkRateLimit($clientIp, 300, 3600)) {
                $this->errorResponse('請求過於頻繁，請稍後再試', 429, [], 'RATE_LIMIT_EXCEEDED');
            }

            // 取得分頁參數
            $pagination = $this->getPaginationParams();
            
            // 取得排序參數
            $sort = $this->getSortParams(['id', 'title', 'published_at', 'views'], 'published_at', 'desc');

            $filters = ['category' => $category];

            $result = $this->newsService->getLatestNews($pagination['per_page'], $filters, $pagination['page'], $sort);

            if (!$result['success']) {
                $this->errorResponse($result['message'] ?? '取得分類新聞失敗', 500, [], 'CATEGORY_NEWS_ERROR');
            }

            // 處理響應資料
            $news = array_map(function($article) {
                return $this->formatNewsResponse($article, false);
            }, $result['data'] ?? []);

            $this->paginatedResponse(
                $news,
                $result['meta']['total'] ?? 0,
                $pagination['page'],
                $pagination['per_page'],
                "分類「{$category}」新聞取得成功"
            );

        } catch (\Exception $e) {
            $this->errorResponse('分類新聞服務暫時不可用', 500, [], 'CATEGORY_NEWS_SERVICE_ERROR');
        }
    }

    /**
     * 取得新聞源的新聞
     * 
     * GET /api/news/source/{sourceId}
     * 
     * 參數：
     * - page: 頁碼
     * - per_page: 每頁項目數
     * - sort_by: 排序欄位
     * - sort_direction: 排序方向
     */
    public function source(int $sourceId): void
    {
        try {
            // 檢查速率限制
            $clientIp = $this->getClientIp();
            if (!$this->checkRateLimit($clientIp, 300, 3600)) {
                $this->errorResponse('請求過於頻繁，請稍後再試', 429, [], 'RATE_LIMIT_EXCEEDED');
            }

            // 取得分頁參數
            $pagination = $this->getPaginationParams();
            
            // 取得排序參數
            $sort = $this->getSortParams(['id', 'title', 'published_at', 'views'], 'published_at', 'desc');

            $filters = ['source_id' => $sourceId];

            $result = $this->newsService->getLatestNews($pagination['per_page'], $filters, $pagination['page'], $sort);

            if (!$result['success']) {
                $this->errorResponse($result['message'] ?? '取得新聞源新聞失敗', 500, [], 'SOURCE_NEWS_ERROR');
            }

            // 處理響應資料
            $news = array_map(function($article) {
                return $this->formatNewsResponse($article, false);
            }, $result['data'] ?? []);

            // 取得新聞源資訊
            $sourceName = !empty($news) ? $news[0]['source_name'] : null;

            $this->paginatedResponse(
                $news,
                $result['meta']['total'] ?? 0,
                $pagination['page'],
                $pagination['per_page'],
                $sourceName ? "新聞源「{$sourceName}」新聞取得成功" : '新聞源新聞取得成功'
            );

        } catch (\Exception $e) {
            $this->errorResponse('新聞源新聞服務暫時不可用', 500, [], 'SOURCE_NEWS_SERVICE_ERROR');
        }
    }

    /**
     * 搜尋新聞
     * 
     * GET /api/news/search
     * 
     * 參數：
     * - q: 搜尋關鍵字 (必填)
     * - page: 頁碼
     * - per_page: 每頁項目數
     * - category: 分類篩選
     * - source_id: 新聞源篩選
     * - date_from: 開始日期
     * - date_to: 結束日期
     */
    public function search(): void
    {
        try {
            // 檢查速率限制
            $clientIp = $this->getClientIp();
            if (!$this->checkRateLimit($clientIp, 200, 3600)) {
                $this->errorResponse('請求過於頻繁，請稍後再試', 429, [], 'RATE_LIMIT_EXCEEDED');
            }

            $query = trim($this->getGet('q', ''));
            if (empty($query)) {
                $this->errorResponse('搜尋關鍵字不能為空', 400, [], 'MISSING_QUERY');
            }

            // 取得分頁參數
            $pagination = $this->getPaginationParams();
            
            // 取得篩選參數
            $filters = $this->getFilterParams(['category', 'source_id', 'date_from', 'date_to']);
            $filters['search'] = $query;

            $result = $this->newsService->searchNews($query, $filters, $pagination['page'], $pagination['per_page']);

            if (!$result['success']) {
                $this->errorResponse($result['message'] ?? '新聞搜尋失敗', 500, [], 'NEWS_SEARCH_ERROR');
            }

            // 處理響應資料
            $news = array_map(function($article) {
                return $this->formatNewsResponse($article, false);
            }, $result['data'] ?? []);

            $this->paginatedResponse(
                $news,
                $result['meta']['total'] ?? 0,
                $pagination['page'],
                $pagination['per_page'],
                "搜尋「{$query}」完成"
            );

        } catch (\Exception $e) {
            $this->errorResponse('新聞搜尋服務暫時不可用', 500, [], 'NEWS_SEARCH_SERVICE_ERROR');
        }
    }

    /**
     * 格式化新聞響應資料
     * 
     * @param array $news 新聞資料
     * @param bool $includeContent 是否包含完整內容
     * @return array
     */
    private function formatNewsResponse(array $news, bool $includeContent = false): array
    {
        $response = [
            'id' => $news['id'] ?? null,
            'title' => $news['title'] ?? null,
            'summary' => $news['summary'] ?? null,
            'url' => $news['url'] ?? null,
            'image_url' => $news['image_url'] ?? null,
            'category' => $news['category'] ?? null,
            'author' => $news['author'] ?? null,
            'source_id' => $news['source_id'] ?? null,
            'source_name' => $news['source_name'] ?? null,
            'source_url' => $news['source_url'] ?? null,
            'views' => $news['views'] ?? 0,
            'tags' => $news['tags'] ?? null,
            'published_at' => $news['published_at'] ?? null,
            'created_at' => $news['created_at'] ?? null,
            'updated_at' => $news['updated_at'] ?? null
        ];

        if ($includeContent) {
            $response['content'] = $news['content'] ?? null;
        }

        // 添加 API 連結
        $response['links'] = [
            'self' => "/api/news/{$news['id']}",
            'web' => "/news/{$news['id']}",
            'source' => $news['url'] ?? null
        ];

        return $response;
    }
}