<?php
/**
 * API 測試腳本 - 繞過路由系統直接測試
 */

// 設定錯誤報告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 設定內容類型
header('Content-Type: application/json; charset=utf-8');

try {
    // 簡單的API響應
    $response = [
        'success' => true,
        'message' => 'API 測試成功',
        'timestamp' => date('c'),
        'server_info' => [
            'php_version' => PHP_VERSION,
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'unknown',
            'request_method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown'
        ],
        'database_test' => 'pending'
    ];
    
    // 測試資料庫連接（不拋出異常）
    try {
        // 載入環境變數
        $envFile = dirname(__DIR__) . '/.env';
        if (file_exists($envFile)) {
            $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            foreach ($lines as $line) {
                $line = trim($line);
                if (!empty($line) && strpos($line, '=') !== false && strpos($line, '#') !== 0) {
                    [$key, $value] = explode('=', $line, 2);
                    $_ENV[trim($key)] = trim($value);
                }
            }
        }
        
        $host = $_ENV['DB_HOST'] ?? 'localhost';
        $port = $_ENV['DB_PORT'] ?? '3306';
        $database = $_ENV['DB_DATABASE'] ?? 'techvg';
        $username = $_ENV['DB_USERNAME'] ?? 'root';
        $password = $_ENV['DB_PASSWORD'] ?? '';
        
        $dsn = "mysql:host={$host};port={$port};dbname={$database};charset=utf8mb4";
        $pdo = new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_TIMEOUT => 5
        ]);
        
        $response['database_test'] = 'success';
        $response['database_info'] = [
            'host' => $host,
            'database' => $database,
            'connection_time' => 'under 5 seconds'
        ];
        
    } catch (Exception $e) {
        $response['database_test'] = 'failed';
        $response['database_error'] = $e->getMessage();
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'timestamp' => date('c')
    ], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
}
?>
