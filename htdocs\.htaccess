# Tech.VG 共享主機 .htaccess 配置

# 啟用重寫引擎
RewriteEngine On

# 安全性設定
# 禁止存取敏感檔案
<FilesMatch "\.(env|log|sql|md|txt)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# 禁止存取配置目錄
<IfModule mod_rewrite.c>
    RewriteRule ^config/ - [F,L]
    RewriteRule ^storage/ - [F,L]
    RewriteRule ^app/ - [F,L]
</IfModule>

# 禁止存取 .git 目錄
<IfModule mod_rewrite.c>
    RewriteRule ^\.git - [F,L]
</IfModule>

# 禁止存取 composer 檔案
<FilesMatch "^(composer\.(json|lock)|package\.json)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# 設定預設檔案
DirectoryIndex index.php index.html

# API 路由重寫
<IfModule mod_rewrite.c>
    # API 請求重定向到 api.php
    RewriteRule ^api/?$ api.php [L]
    RewriteRule ^api/(.*)$ api.php [L]
    
    # 搜索頁面特殊處理
    RewriteRule ^search/?$ search.php [L]
</IfModule>

# 主應用程式路由重寫
<IfModule mod_rewrite.c>
    # 如果請求的檔案或目錄不存在，重定向到 index.php
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} !^/api/
    RewriteRule ^(.*)$ index.php [L]
</IfModule>

# 快取設定
<IfModule mod_expires.c>
    ExpiresActive On
    
    # 圖片快取 1 個月
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    
    # CSS 和 JS 快取 1 週
    ExpiresByType text/css "access plus 1 week"
    ExpiresByType application/javascript "access plus 1 week"
    ExpiresByType text/javascript "access plus 1 week"
    
    # 字型快取 1 個月
    ExpiresByType font/woff "access plus 1 month"
    ExpiresByType font/woff2 "access plus 1 month"
    ExpiresByType application/font-woff "access plus 1 month"
    ExpiresByType application/font-woff2 "access plus 1 month"
</IfModule>

# 壓縮設定
<IfModule mod_deflate.c>
    # 壓縮文字檔案
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# 安全標頭
<IfModule mod_headers.c>
    # 防止 XSS 攻擊
    Header always set X-XSS-Protection "1; mode=block"
    
    # 防止內容類型嗅探
    Header always set X-Content-Type-Options "nosniff"
    
    # 防止點擊劫持
    Header always set X-Frame-Options "SAMEORIGIN"
    
    # 強制 HTTPS（如果支援）
    # Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
    
    # 內容安全政策
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.tailwindcss.com https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://cdnjs.cloudflare.com https://fonts.googleapis.com; img-src 'self' data: https:; font-src 'self' https://cdnjs.cloudflare.com https://fonts.gstatic.com;"
</IfModule>

# 錯誤頁面
ErrorDocument 404 /error.php?code=404
ErrorDocument 403 /error.php?code=403
ErrorDocument 500 /error.php?code=500

# 禁用伺服器簽名
ServerTokens Prod

# 限制檔案上傳大小（如果支援）
<IfModule mod_php.c>
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    php_value max_execution_time 300
    php_value max_input_time 300
    php_value memory_limit 256M
</IfModule>

# 禁止存取備份檔案
<FilesMatch "\.(bak|backup|old|tmp|temp)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# 禁止存取版本控制檔案
<FilesMatch "^(\.git|\.svn|\.hg)">
    Order allow,deny
    Deny from all
</FilesMatch>