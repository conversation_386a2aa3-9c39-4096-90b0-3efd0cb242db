<?php

namespace App\Controller\Api;

use App\Database\Database;

/**
 * API 狀態監控控制器
 * 提供系統狀態和健康檢查
 */
class StatusController extends BaseApiController
{
    /**
     * 系統狀態總覽
     * 
     * GET /api/status
     */
    public function index(): void
    {
        $format = $_GET['format'] ?? 'json';
        
        $status = $this->getSystemStatus();
        
        if ($format === 'html') {
            $this->showHtmlStatus($status);
            return;
        }
        
        $this->successResponse($status, '系統狀態取得成功');
    }

    /**
     * 健康檢查
     * 
     * GET /api/health
     */
    public function health(): void
    {
        $checks = $this->performHealthChecks();
        $overall = $this->calculateOverallHealth($checks);
        
        $response = [
            'status' => $overall['status'],
            'timestamp' => date('c'),
            'checks' => $checks,
            'uptime' => $this->getUptime(),
            'version' => 'v1'
        ];
        
        $httpCode = $overall['status'] === 'healthy' ? 200 : 503;
        
        http_response_code($httpCode);
        echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        exit;
    }

    /**
     * 詳細系統資訊
     * 
     * GET /api/status/detailed
     */
    public function detailed(): void
    {
        $status = [
            'system' => $this->getSystemInfo(),
            'database' => $this->getDatabaseStatus(),
            'api' => $this->getApiStatus(),
            'performance' => $this->getPerformanceMetrics(),
            'security' => $this->getSecurityStatus(),
            'dependencies' => $this->getDependencyStatus()
        ];
        
        $this->successResponse($status, '詳細系統狀態取得成功');
    }

    /**
     * 顯示 HTML 格式的狀態頁面
     */
    private function showHtmlStatus(array $status): void
    {
        $data = [
            'status' => $status,
            'base_url' => $this->getBaseUrl()
        ];
        
        include __DIR__ . '/../../View/api/status.php';
        exit;
    }

    /**
     * 取得系統狀態
     * 
     * @return array
     */
    private function getSystemStatus(): array
    {
        $checks = $this->performHealthChecks();
        $overall = $this->calculateOverallHealth($checks);
        
        return [
            'overall_status' => $overall['status'],
            'timestamp' => date('c'),
            'uptime' => $this->getUptime(),
            'version' => 'v1',
            'environment' => $this->getEnvironment(),
            'services' => $checks,
            'metrics' => $this->getBasicMetrics()
        ];
    }

    /**
     * 執行健康檢查
     * 
     * @return array
     */
    private function performHealthChecks(): array
    {
        $checks = [];
        
        // 資料庫檢查
        $checks['database'] = $this->checkDatabase();
        
        // 檔案系統檢查
        $checks['filesystem'] = $this->checkFilesystem();
        
        // 記憶體檢查
        $checks['memory'] = $this->checkMemory();
        
        // 磁碟空間檢查
        $checks['disk'] = $this->checkDiskSpace();
        
        // API 端點檢查
        $checks['api_endpoints'] = $this->checkApiEndpoints();
        
        return $checks;
    }

    /**
     * 檢查資料庫連線
     * 
     * @return array
     */
    private function checkDatabase(): array
    {
        try {
            $db = Database::getInstance();
            $pdo = $db->getConnection();
            
            // 執行簡單查詢測試連線
            $stmt = $pdo->query('SELECT 1');
            $result = $stmt->fetch();
            
            if ($result) {
                return [
                    'status' => 'healthy',
                    'message' => '資料庫連線正常',
                    'response_time' => $this->measureResponseTime(function() use ($pdo) {
                        $pdo->query('SELECT 1');
                    })
                ];
            } else {
                return [
                    'status' => 'unhealthy',
                    'message' => '資料庫查詢失敗',
                    'response_time' => null
                ];
            }
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'message' => '資料庫連線失敗: ' . $e->getMessage(),
                'response_time' => null
            ];
        }
    }

    /**
     * 檢查檔案系統
     * 
     * @return array
     */
    private function checkFilesystem(): array
    {
        $storageDir = __DIR__ . '/../../../storage';
        
        if (!is_dir($storageDir)) {
            return [
                'status' => 'unhealthy',
                'message' => '儲存目錄不存在'
            ];
        }
        
        if (!is_writable($storageDir)) {
            return [
                'status' => 'unhealthy',
                'message' => '儲存目錄無法寫入'
            ];
        }
        
        return [
            'status' => 'healthy',
            'message' => '檔案系統正常',
            'storage_path' => $storageDir,
            'writable' => true
        ];
    }

    /**
     * 檢查記憶體使用量
     * 
     * @return array
     */
    private function checkMemory(): array
    {
        $memoryUsage = memory_get_usage(true);
        $memoryLimit = $this->parseMemoryLimit(ini_get('memory_limit'));
        $memoryPercent = ($memoryUsage / $memoryLimit) * 100;
        
        $status = 'healthy';
        if ($memoryPercent > 90) {
            $status = 'critical';
        } elseif ($memoryPercent > 75) {
            $status = 'warning';
        }
        
        return [
            'status' => $status,
            'message' => sprintf('記憶體使用率: %.1f%%', $memoryPercent),
            'usage_bytes' => $memoryUsage,
            'usage_mb' => round($memoryUsage / 1024 / 1024, 2),
            'limit_bytes' => $memoryLimit,
            'limit_mb' => round($memoryLimit / 1024 / 1024, 2),
            'percentage' => round($memoryPercent, 1)
        ];
    }

    /**
     * 檢查磁碟空間
     * 
     * @return array
     */
    private function checkDiskSpace(): array
    {
        $rootPath = __DIR__ . '/../../../';
        $freeBytes = disk_free_space($rootPath);
        $totalBytes = disk_total_space($rootPath);
        $usedBytes = $totalBytes - $freeBytes;
        $usedPercent = ($usedBytes / $totalBytes) * 100;
        
        $status = 'healthy';
        if ($usedPercent > 95) {
            $status = 'critical';
        } elseif ($usedPercent > 85) {
            $status = 'warning';
        }
        
        return [
            'status' => $status,
            'message' => sprintf('磁碟使用率: %.1f%%', $usedPercent),
            'free_bytes' => $freeBytes,
            'free_gb' => round($freeBytes / 1024 / 1024 / 1024, 2),
            'total_bytes' => $totalBytes,
            'total_gb' => round($totalBytes / 1024 / 1024 / 1024, 2),
            'used_percentage' => round($usedPercent, 1)
        ];
    }

    /**
     * 檢查 API 端點
     * 
     * @return array
     */
    private function checkApiEndpoints(): array
    {
        $endpoints = [
            '/api/search' => 'GET',
            '/api/documents' => 'GET',
            '/api/news' => 'GET',
            '/api/stats/overview' => 'GET'
        ];
        
        $results = [];
        foreach ($endpoints as $endpoint => $method) {
            $results[$endpoint] = $this->checkEndpoint($endpoint, $method);
        }
        
        $healthyCount = count(array_filter($results, function($result) {
            return $result['status'] === 'healthy';
        }));
        
        $overallStatus = $healthyCount === count($results) ? 'healthy' : 
                        ($healthyCount > 0 ? 'degraded' : 'unhealthy');
        
        return [
            'status' => $overallStatus,
            'message' => sprintf('%d/%d 端點正常', $healthyCount, count($results)),
            'endpoints' => $results
        ];
    }

    /**
     * 檢查單一端點
     * 
     * @param string $endpoint
     * @param string $method
     * @return array
     */
    private function checkEndpoint(string $endpoint, string $method): array
    {
        try {
            $startTime = microtime(true);
            
            // 這裡應該實際呼叫端點，但為了簡化，我們假設都是正常的
            // 在實際環境中，您可能需要使用 cURL 或其他方式來測試端點
            
            $responseTime = (microtime(true) - $startTime) * 1000;
            
            return [
                'status' => 'healthy',
                'response_time_ms' => round($responseTime, 2),
                'method' => $method
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
                'method' => $method
            ];
        }
    }

    /**
     * 計算整體健康狀態
     * 
     * @param array $checks
     * @return array
     */
    private function calculateOverallHealth(array $checks): array
    {
        $statuses = array_column($checks, 'status');
        
        if (in_array('unhealthy', $statuses) || in_array('critical', $statuses)) {
            return ['status' => 'unhealthy', 'message' => '系統存在嚴重問題'];
        }
        
        if (in_array('warning', $statuses) || in_array('degraded', $statuses)) {
            return ['status' => 'degraded', 'message' => '系統部分功能異常'];
        }
        
        return ['status' => 'healthy', 'message' => '系統運行正常'];
    }

    /**
     * 取得系統運行時間
     * 
     * @return array
     */
    private function getUptime(): array
    {
        // 簡化的運行時間計算
        $startTime = filemtime(__DIR__ . '/../../../public/index.php');
        $uptime = time() - $startTime;
        
        return [
            'seconds' => $uptime,
            'human_readable' => $this->formatUptime($uptime)
        ];
    }

    /**
     * 取得環境資訊
     * 
     * @return array
     */
    private function getEnvironment(): array
    {
        return [
            'php_version' => PHP_VERSION,
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'operating_system' => PHP_OS,
            'timezone' => date_default_timezone_get()
        ];
    }

    /**
     * 取得基本指標
     * 
     * @return array
     */
    private function getBasicMetrics(): array
    {
        return [
            'requests_per_minute' => $this->getRequestsPerMinute(),
            'average_response_time' => $this->getAverageResponseTime(),
            'error_rate' => $this->getErrorRate()
        ];
    }

    /**
     * 測量響應時間
     * 
     * @param callable $callback
     * @return float
     */
    private function measureResponseTime(callable $callback): float
    {
        $startTime = microtime(true);
        $callback();
        return round((microtime(true) - $startTime) * 1000, 2);
    }

    /**
     * 解析記憶體限制
     * 
     * @param string $memoryLimit
     * @return int
     */
    private function parseMemoryLimit(string $memoryLimit): int
    {
        $memoryLimit = trim($memoryLimit);
        $last = strtolower($memoryLimit[strlen($memoryLimit) - 1]);
        $value = (int) $memoryLimit;
        
        switch ($last) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }
        
        return $value;
    }

    /**
     * 格式化運行時間
     * 
     * @param int $seconds
     * @return string
     */
    private function formatUptime(int $seconds): string
    {
        $days = floor($seconds / 86400);
        $hours = floor(($seconds % 86400) / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $seconds = $seconds % 60;
        
        $parts = [];
        if ($days > 0) $parts[] = "{$days} 天";
        if ($hours > 0) $parts[] = "{$hours} 小時";
        if ($minutes > 0) $parts[] = "{$minutes} 分鐘";
        if ($seconds > 0 || empty($parts)) $parts[] = "{$seconds} 秒";
        
        return implode(' ', $parts);
    }

    /**
     * 取得每分鐘請求數（模擬）
     * 
     * @return int
     */
    private function getRequestsPerMinute(): int
    {
        // 這裡應該從實際的日誌或統計系統取得資料
        return rand(50, 200);
    }

    /**
     * 取得平均響應時間（模擬）
     * 
     * @return float
     */
    private function getAverageResponseTime(): float
    {
        // 這裡應該從實際的監控系統取得資料
        return round(rand(50, 300) / 10, 1);
    }

    /**
     * 取得錯誤率（模擬）
     * 
     * @return float
     */
    private function getErrorRate(): float
    {
        // 這裡應該從實際的日誌系統取得資料
        return round(rand(0, 50) / 100, 2);
    }

    /**
     * 取得系統資訊
     * 
     * @return array
     */
    private function getSystemInfo(): array
    {
        return [
            'php_version' => PHP_VERSION,
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'operating_system' => PHP_OS,
            'timezone' => date_default_timezone_get(),
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'upload_max_filesize' => ini_get('upload_max_filesize'),
            'post_max_size' => ini_get('post_max_size')
        ];
    }

    /**
     * 取得資料庫狀態
     * 
     * @return array
     */
    private function getDatabaseStatus(): array
    {
        try {
            $db = Database::getInstance();
            $pdo = $db->getConnection();
            
            return [
                'status' => 'connected',
                'driver' => $pdo->getAttribute(\PDO::ATTR_DRIVER_NAME),
                'version' => $pdo->getAttribute(\PDO::ATTR_SERVER_VERSION),
                'connection_status' => $pdo->getAttribute(\PDO::ATTR_CONNECTION_STATUS)
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'disconnected',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 取得 API 狀態
     * 
     * @return array
     */
    private function getApiStatus(): array
    {
        return [
            'version' => 'v1',
            'endpoints_count' => 25,
            'rate_limiting' => 'enabled',
            'caching' => 'enabled',
            'documentation' => 'available'
        ];
    }

    /**
     * 取得效能指標
     * 
     * @return array
     */
    private function getPerformanceMetrics(): array
    {
        return [
            'memory_usage' => $this->checkMemory(),
            'disk_usage' => $this->checkDiskSpace(),
            'cpu_usage' => $this->getCpuUsage(),
            'load_average' => $this->getLoadAverage()
        ];
    }

    /**
     * 取得安全狀態
     * 
     * @return array
     */
    private function getSecurityStatus(): array
    {
        return [
            'https_enabled' => isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on',
            'api_key_required' => true,
            'rate_limiting' => true,
            'cors_enabled' => true,
            'security_headers' => true
        ];
    }

    /**
     * 取得依賴狀態
     * 
     * @return array
     */
    private function getDependencyStatus(): array
    {
        return [
            'php_extensions' => $this->checkPhpExtensions(),
            'composer_packages' => $this->checkComposerPackages()
        ];
    }

    /**
     * 取得 CPU 使用率（模擬）
     * 
     * @return array
     */
    private function getCpuUsage(): array
    {
        $usage = rand(10, 80);
        $status = $usage > 90 ? 'critical' : ($usage > 75 ? 'warning' : 'healthy');
        
        return [
            'status' => $status,
            'percentage' => $usage,
            'message' => "CPU 使用率: {$usage}%"
        ];
    }

    /**
     * 取得負載平均（模擬）
     * 
     * @return array
     */
    private function getLoadAverage(): array
    {
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg();
            return [
                '1_minute' => $load[0],
                '5_minutes' => $load[1],
                '15_minutes' => $load[2]
            ];
        }
        
        return [
            '1_minute' => round(rand(50, 200) / 100, 2),
            '5_minutes' => round(rand(50, 200) / 100, 2),
            '15_minutes' => round(rand(50, 200) / 100, 2)
        ];
    }

    /**
     * 檢查 PHP 擴展
     * 
     * @return array
     */
    private function checkPhpExtensions(): array
    {
        $required = ['pdo', 'json', 'mbstring', 'curl'];
        $extensions = [];
        
        foreach ($required as $ext) {
            $extensions[$ext] = extension_loaded($ext);
        }
        
        return $extensions;
    }

    /**
     * 檢查 Composer 套件（模擬）
     * 
     * @return array
     */
    private function checkComposerPackages(): array
    {
        return [
            'installed' => true,
            'up_to_date' => true,
            'security_vulnerabilities' => 0
        ];
    }

    /**
     * 取得基礎 URL
     * 
     * @return string
     */
    private function getBaseUrl(): string
    {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        return "{$protocol}://{$host}";
    }
}