<?php

/**
 * 資料庫配置文件
 * 包含資料庫連接參數和相關設定
 */

return [
    // 預設資料庫連接
    'default' => 'mysql',
    
    // 資料庫連接配置
    'connections' => [
        'mysql' => [
            'driver' => 'mysql',
            'host' => $_ENV['DB_HOST'] ?? 'localhost',
            'port' => $_ENV['DB_PORT'] ?? '3306',
            'database' => $_ENV['DB_DATABASE'] ?? 'techvg',
            'username' => $_ENV['DB_USERNAME'] ?? 'root',
            'password' => $_ENV['DB_PASSWORD'] ?? '',
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'options' => [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
            ]
        ]
    ],
    
    // 查詢日誌設定
    'log_queries' => $_ENV['DB_LOG_QUERIES'] ?? false,
    
    // 連接池設定
    'pool' => [
        'max_connections' => 10,
        'timeout' => 30
    ]
];