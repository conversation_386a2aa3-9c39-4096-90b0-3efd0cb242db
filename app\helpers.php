<?php

/**
 * 全域輔助函數
 */

if (!function_exists('config')) {
    /**
     * 取得配置值
     * 
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    function config(string $key, $default = null)
    {
        static $config = null;
        
        if ($config === null) {
            $config = require APP_ROOT . '/config/app.php';
        }
        
        $keys = explode('.', $key);
        $value = $config;
        
        foreach ($keys as $k) {
            if (!isset($value[$k])) {
                return $default;
            }
            $value = $value[$k];
        }
        
        return $value;
    }
}

if (!function_exists('env')) {
    /**
     * 取得環境變數
     * 
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    function env(string $key, $default = null)
    {
        return $_ENV[$key] ?? $default;
    }
}

if (!function_exists('url')) {
    /**
     * 生成URL
     * 
     * @param string $path
     * @return string
     */
    function url(string $path = ''): string
    {
        $baseUrl = config('app.url', 'http://localhost:8000');
        return rtrim($baseUrl, '/') . '/' . ltrim($path, '/');
    }
}

if (!function_exists('asset')) {
    /**
     * 生成靜態資源URL
     * 
     * @param string $path
     * @return string
     */
    function asset(string $path): string
    {
        return url('assets/' . ltrim($path, '/'));
    }
}

if (!function_exists('view')) {
    /**
     * 渲染視圖
     * 
     * @param string $viewName
     * @param array $data
     * @return string
     */
    function view(string $viewName, array $data = []): string
    {
        extract($data);
        
        ob_start();
        $viewPath = APP_ROOT . '/app/View/' . $viewName . '.php';
        
        if (file_exists($viewPath)) {
            include $viewPath;
        } else {
            throw new Exception("視圖文件不存在: {$viewName}");
        }
        
        return ob_get_clean();
    }
}

if (!function_exists('redirect')) {
    /**
     * 重定向
     * 
     * @param string $url
     * @param int $statusCode
     */
    function redirect(string $url, int $statusCode = 302): void
    {
        http_response_code($statusCode);
        header("Location: {$url}");
        exit;
    }
}

if (!function_exists('json_response')) {
    /**
     * JSON響應
     * 
     * @param array $data
     * @param int $statusCode
     */
    function json_response(array $data, int $statusCode = 200): void
    {
        http_response_code($statusCode);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }
}

if (!function_exists('csrf_token')) {
    /**
     * 生成CSRF令牌
     * 
     * @return string
     */
    function csrf_token(): string
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        
        return $_SESSION['csrf_token'];
    }
}

if (!function_exists('csrf_field')) {
    /**
     * 生成CSRF隱藏欄位
     * 
     * @return string
     */
    function csrf_field(): string
    {
        return '<input type="hidden" name="csrf_token" value="' . csrf_token() . '">';
    }
}

if (!function_exists('old')) {
    /**
     * 取得舊的表單輸入值
     * 
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    function old(string $key, $default = '')
    {
        return $_SESSION['old_input'][$key] ?? $default;
    }
}

if (!function_exists('flash')) {
    /**
     * 設定或取得快閃訊息
     * 
     * @param string|null $key
     * @param mixed $value
     * @return mixed
     */
    function flash(?string $key = null, $value = null)
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        if ($key === null) {
            return $_SESSION['flash'] ?? [];
        }
        
        if ($value !== null) {
            $_SESSION['flash'][$key] = $value;
            return;
        }
        
        $message = $_SESSION['flash'][$key] ?? null;
        unset($_SESSION['flash'][$key]);
        
        return $message;
    }
}

if (!function_exists('sanitize')) {
    /**
     * 清理用戶輸入
     * 
     * @param string $input
     * @return string
     */
    function sanitize(string $input): string
    {
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
}

if (!function_exists('format_bytes')) {
    /**
     * 格式化位元組大小
     * 
     * @param int $bytes
     * @param int $precision
     * @return string
     */
    function format_bytes(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}

if (!function_exists('time_ago')) {
    /**
     * 時間差顯示
     * 
     * @param string $datetime
     * @return string
     */
    function time_ago(string $datetime): string
    {
        $time = time() - strtotime($datetime);
        
        if ($time < 60) {
            return '剛剛';
        } elseif ($time < 3600) {
            return floor($time / 60) . ' 分鐘前';
        } elseif ($time < 86400) {
            return floor($time / 3600) . ' 小時前';
        } elseif ($time < 2592000) {
            return floor($time / 86400) . ' 天前';
        } elseif ($time < 31536000) {
            return floor($time / 2592000) . ' 個月前';
        } else {
            return floor($time / 31536000) . ' 年前';
        }
    }
}

if (!function_exists('truncate')) {
    /**
     * 截斷文字
     * 
     * @param string $text
     * @param int $length
     * @param string $suffix
     * @return string
     */
    function truncate(string $text, int $length = 100, string $suffix = '...'): string
    {
        if (mb_strlen($text) <= $length) {
            return $text;
        }
        
        return mb_substr($text, 0, $length) . $suffix;
    }
}

if (!function_exists('is_active_route')) {
    /**
     * 檢查是否為當前路由
     * 
     * @param string $route
     * @return bool
     */
    function is_active_route(string $route): bool
    {
        $currentPath = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        return $currentPath === $route || strpos($currentPath, $route) === 0;
    }
}