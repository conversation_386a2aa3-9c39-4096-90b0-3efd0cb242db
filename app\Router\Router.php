<?php

namespace App\Router;

use Exception;

/**
 * 路由系統
 * 處理URL路由和請求分發
 */
class Router
{
    private array $routes = [];
    private array $middleware = [];
    private string $basePath = '';
    
    /**
     * 設定基礎路徑
     * 
     * @param string $basePath
     */
    public function setBasePath(string $basePath): void
    {
        $this->basePath = rtrim($basePath, '/');
    }

    /**
     * 註冊GET路由
     * 
     * @param string $pattern
     * @param callable|string $handler
     * @param array $middleware
     */
    public function get(string $pattern, $handler, array $middleware = []): void
    {
        $this->addRoute('GET', $pattern, $handler, $middleware);
    }

    /**
     * 註冊POST路由
     * 
     * @param string $pattern
     * @param callable|string $handler
     * @param array $middleware
     */
    public function post(string $pattern, $handler, array $middleware = []): void
    {
        $this->addRoute('POST', $pattern, $handler, $middleware);
    }

    /**
     * 註冊PUT路由
     * 
     * @param string $pattern
     * @param callable|string $handler
     * @param array $middleware
     */
    public function put(string $pattern, $handler, array $middleware = []): void
    {
        $this->addRoute('PUT', $pattern, $handler, $middleware);
    }

    /**
     * 註冊DELETE路由
     * 
     * @param string $pattern
     * @param callable|string $handler
     * @param array $middleware
     */
    public function delete(string $pattern, $handler, array $middleware = []): void
    {
        $this->addRoute('DELETE', $pattern, $handler, $middleware);
    }

    /**
     * 註冊任意方法路由
     * 
     * @param array $methods
     * @param string $pattern
     * @param callable|string $handler
     * @param array $middleware
     */
    public function match(array $methods, string $pattern, $handler, array $middleware = []): void
    {
        foreach ($methods as $method) {
            $this->addRoute(strtoupper($method), $pattern, $handler, $middleware);
        }
    }

    /**
     * 添加路由
     * 
     * @param string $method
     * @param string $pattern
     * @param callable|string $handler
     * @param array $middleware
     */
    private function addRoute(string $method, string $pattern, $handler, array $middleware = []): void
    {
        $pattern = $this->basePath . $pattern;
        
        $this->routes[] = [
            'method' => $method,
            'pattern' => $pattern,
            'handler' => $handler,
            'middleware' => $middleware,
            'regex' => $this->convertPatternToRegex($pattern),
            'params' => $this->extractParameterNames($pattern)
        ];
    }

    /**
     * 註冊全域中間件
     * 
     * @param callable|string $middleware
     */
    public function addMiddleware($middleware): void
    {
        $this->middleware[] = $middleware;
    }

    /**
     * 路由群組
     * 
     * @param string $prefix
     * @param callable $callback
     * @param array $middleware
     */
    public function group(string $prefix, callable $callback, array $middleware = []): void
    {
        $originalBasePath = $this->basePath;
        $originalMiddleware = $this->middleware;
        
        $this->basePath .= $prefix;
        $this->middleware = array_merge($this->middleware, $middleware);
        
        $callback($this);
        
        $this->basePath = $originalBasePath;
        $this->middleware = $originalMiddleware;
    }

    /**
     * 分發請求
     * 
     * @param string|null $requestUri
     * @param string|null $requestMethod
     * @return mixed
     * @throws Exception
     */
    public function dispatch(?string $requestUri = null, ?string $requestMethod = null)
    {
        $requestUri = $requestUri ?? $_SERVER['REQUEST_URI'];
        $requestMethod = $requestMethod ?? $_SERVER['REQUEST_METHOD'];
        
        // 移除查詢字串
        $requestUri = parse_url($requestUri, PHP_URL_PATH);
        
        // 尋找匹配的路由
        $matchedRoute = $this->findMatchingRoute($requestMethod, $requestUri);
        
        if (!$matchedRoute) {
            throw new Exception('Route not found', 404);
        }
        
        // 執行中間件
        $this->executeMiddleware(array_merge($this->middleware, $matchedRoute['middleware']));
        
        // 執行處理器
        return $this->executeHandler($matchedRoute['handler'], $matchedRoute['params']);
    }

    /**
     * 尋找匹配的路由
     * 
     * @param string $method
     * @param string $uri
     * @return array|null
     */
    private function findMatchingRoute(string $method, string $uri): ?array
    {
        foreach ($this->routes as $route) {
            if ($route['method'] !== $method) {
                continue;
            }
            
            if (preg_match($route['regex'], $uri, $matches)) {
                // 提取參數值
                $params = [];
                foreach ($route['params'] as $index => $paramName) {
                    $params[$paramName] = $matches[$index + 1] ?? null;
                }
                
                return [
                    'handler' => $route['handler'],
                    'middleware' => $route['middleware'],
                    'params' => $params
                ];
            }
        }
        
        return null;
    }

    /**
     * 將路由模式轉換為正則表達式
     * 
     * @param string $pattern
     * @return string
     */
    private function convertPatternToRegex(string $pattern): string
    {
        // 轉義特殊字符
        $pattern = preg_quote($pattern, '/');

        // 替換參數佔位符 - 修正字符類的轉義問題
        $pattern = preg_replace('/\\\\{([^}]+)\\\\}/', '([^\/]+)', $pattern);

        return '/^' . $pattern . '$/';
    }

    /**
     * 提取參數名稱
     * 
     * @param string $pattern
     * @return array
     */
    private function extractParameterNames(string $pattern): array
    {
        preg_match_all('/{([^}]+)}/', $pattern, $matches);
        return $matches[1] ?? [];
    }

    /**
     * 執行中間件
     * 
     * @param array $middleware
     * @throws Exception
     */
    private function executeMiddleware(array $middleware): void
    {
        foreach ($middleware as $middlewareItem) {
            if (is_string($middlewareItem)) {
                if (!class_exists($middlewareItem)) {
                    throw new Exception("Middleware class not found: {$middlewareItem}");
                }
                
                $instance = new $middlewareItem();
                if (!method_exists($instance, 'handle')) {
                    throw new Exception("Middleware must have handle method: {$middlewareItem}");
                }
                
                $instance->handle();
            } elseif (is_callable($middlewareItem)) {
                $middlewareItem();
            }
        }
    }

    /**
     * 執行處理器
     * 
     * @param callable|string $handler
     * @param array $params
     * @return mixed
     * @throws Exception
     */
    private function executeHandler($handler, array $params = [])
    {
        if (is_string($handler)) {
            // 解析控制器@方法格式
            if (strpos($handler, '@') !== false) {
                [$controllerClass, $method] = explode('@', $handler);
                
                if (!class_exists($controllerClass)) {
                    throw new Exception("Controller class not found: {$controllerClass}");
                }
                
                $controller = new $controllerClass();
                
                if (!method_exists($controller, $method)) {
                    throw new Exception("Method not found: {$controllerClass}@{$method}");
                }
                
                return $controller->$method(...array_values($params));
            }
        }
        
        if (is_callable($handler)) {
            return $handler(...array_values($params));
        }
        
        throw new Exception('Invalid handler');
    }

    /**
     * 生成URL
     * 
     * @param string $name
     * @param array $params
     * @return string
     */
    public function url(string $name, array $params = []): string
    {
        // 這裡可以實現命名路由的URL生成
        // 目前簡化實現
        return $this->basePath . $name;
    }

    /**
     * 取得所有路由
     * 
     * @return array
     */
    public function getRoutes(): array
    {
        return $this->routes;
    }
}