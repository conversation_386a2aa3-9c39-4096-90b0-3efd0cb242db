<?php

namespace App\Controller\Api;

/**
 * API 測試控制器
 * 提供 API 測試和驗證功能
 */
class TestController extends BaseApiController
{
    /**
     * API 測試首頁
     * 
     * GET /api/test
     */
    public function index(): void
    {
        // 檢查是否請求 HTML 格式
        $acceptHeader = $_SERVER['HTTP_ACCEPT'] ?? '';
        $format = $_GET['format'] ?? '';
        
        if ($format === 'html' || strpos($acceptHeader, 'text/html') !== false) {
            $this->showHtmlTestTool();
            return;
        }
        
        $this->successResponse([
            'message' => 'API 測試工具',
            'available_tests' => [
                'ping' => '/api/test/ping',
                'echo' => '/api/test/echo',
                'validate' => '/api/test/validate',
                'performance' => '/api/test/performance',
                'endpoints' => '/api/test/endpoints'
            ],
            'documentation' => '/api/docs'
        ], 'API 測試工具可用');
    }

    /**
     * 顯示 HTML 格式的測試工具
     */
    public function showHtmlTestTool(): void
    {
        $data = [
            'base_url' => $this->getBaseUrl()
        ];
        
        // 載入 HTML 模板
        include __DIR__ . '/../../View/api/test.php';
        exit;
    }

    /**
     * Ping 測試
     * 
     * GET /api/test/ping
     */
    public function ping(): void
    {
        $startTime = microtime(true);
        
        $this->successResponse([
            'pong' => true,
            'timestamp' => date('c'),
            'response_time_ms' => round((microtime(true) - $startTime) * 1000, 2),
            'server_info' => [
                'php_version' => PHP_VERSION,
                'memory_usage' => $this->formatBytes(memory_get_usage(true)),
                'peak_memory' => $this->formatBytes(memory_get_peak_usage(true))
            ]
        ], 'Pong!');
    }

    /**
     * Echo 測試
     * 
     * POST /api/test/echo
     */
    public function echo(): void
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->errorResponse('只支援 POST 請求', 405);
        }

        $data = $this->getRequestData();
        
        $this->successResponse([
            'echo' => $data,
            'request_info' => [
                'method' => $_SERVER['REQUEST_METHOD'],
                'content_type' => $_SERVER['CONTENT_TYPE'] ?? null,
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
                'ip' => $this->getClientIp(),
                'timestamp' => date('c')
            ]
        ], 'Echo 測試完成');
    }

    /**
     * 驗證測試
     * 
     * POST /api/test/validate
     */
    public function validate(): void
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->errorResponse('只支援 POST 請求', 405);
        }

        $data = $this->getRequestData();
        
        // 測試驗證規則
        $rules = [
            'name' => 'required|min:2|max:50',
            'email' => 'required|email',
            'age' => 'integer|min:1|max:150',
            'website' => '',
            'tags' => ''
        ];

        $errors = $this->validateRequest($rules, $data);
        
        if (!empty($errors)) {
            $this->errorResponse('驗證失敗', 400, $errors, 'VALIDATION_ERROR');
        }

        $this->successResponse([
            'validated_data' => $data,
            'validation_rules' => $rules,
            'status' => 'passed'
        ], '驗證測試通過');
    }

    /**
     * 效能測試
     * 
     * GET /api/test/performance
     */
    public function performance(): void
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage(true);
        
        // 執行一些運算來測試效能
        $iterations = (int)$this->getGet('iterations', 1000);
        $data = [];
        
        for ($i = 0; $i < $iterations; $i++) {
            $data[] = [
                'id' => $i,
                'value' => md5($i),
                'timestamp' => time()
            ];
        }
        
        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);
        
        $this->successResponse([
            'performance_metrics' => [
                'iterations' => $iterations,
                'execution_time_ms' => round(($endTime - $startTime) * 1000, 2),
                'memory_used' => $this->formatBytes($endMemory - $startMemory),
                'peak_memory' => $this->formatBytes(memory_get_peak_usage(true)),
                'items_per_second' => round($iterations / ($endTime - $startTime), 2)
            ],
            'data_sample' => array_slice($data, 0, 5)
        ], '效能測試完成');
    }

    /**
     * 端點測試
     * 
     * GET /api/test/endpoints
     */
    public function endpoints(): void
    {
        $baseUrl = $this->getBaseUrl();
        $endpoints = [
            // 搜尋端點
            [
                'name' => 'Search API',
                'method' => 'GET',
                'url' => $baseUrl . '/api/search?q=test',
                'expected_status' => 200
            ],
            [
                'name' => 'Search Suggestions',
                'method' => 'GET',
                'url' => $baseUrl . '/api/search/suggestions?q=tech',
                'expected_status' => 200
            ],
            
            // 文檔端點
            [
                'name' => 'Documents List',
                'method' => 'GET',
                'url' => $baseUrl . '/api/documents',
                'expected_status' => 200
            ],
            [
                'name' => 'Document Stats',
                'method' => 'GET',
                'url' => $baseUrl . '/api/documents/stats',
                'expected_status' => 200
            ],
            
            // 新聞端點
            [
                'name' => 'News List',
                'method' => 'GET',
                'url' => $baseUrl . '/api/news',
                'expected_status' => 200
            ],
            [
                'name' => 'Latest News',
                'method' => 'GET',
                'url' => $baseUrl . '/api/news/latest',
                'expected_status' => 200
            ],
            
            // 統計端點
            [
                'name' => 'Stats Overview',
                'method' => 'GET',
                'url' => $baseUrl . '/api/stats/overview',
                'expected_status' => 200
            ]
        ];

        $testResults = [];
        $successCount = 0;
        $totalCount = count($endpoints);

        foreach ($endpoints as $endpoint) {
            $result = $this->testEndpoint($endpoint);
            $testResults[] = $result;
            
            if ($result['success']) {
                $successCount++;
            }
        }

        $this->successResponse([
            'test_summary' => [
                'total_endpoints' => $totalCount,
                'successful' => $successCount,
                'failed' => $totalCount - $successCount,
                'success_rate' => round(($successCount / $totalCount) * 100, 2) . '%'
            ],
            'test_results' => $testResults,
            'timestamp' => date('c')
        ], '端點測試完成');
    }

    /**
     * 負載測試
     * 
     * POST /api/test/load
     */
    public function load(): void
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->errorResponse('只支援 POST 請求', 405);
        }

        $data = $this->getRequestData();
        $endpoint = $data['endpoint'] ?? '/api/search?q=test';
        $requests = min(100, max(1, (int)($data['requests'] ?? 10)));
        $concurrent = min(10, max(1, (int)($data['concurrent'] ?? 1)));

        $results = [];
        $startTime = microtime(true);

        // 模擬負載測試
        for ($i = 0; $i < $requests; $i++) {
            $requestStart = microtime(true);
            
            // 這裡應該實際發送 HTTP 請求，暫時模擬
            usleep(rand(10000, 100000)); // 模擬 10-100ms 的響應時間
            
            $requestEnd = microtime(true);
            $responseTime = ($requestEnd - $requestStart) * 1000;
            
            $results[] = [
                'request_id' => $i + 1,
                'response_time_ms' => round($responseTime, 2),
                'status' => 200,
                'success' => true
            ];
        }

        $endTime = microtime(true);
        $totalTime = $endTime - $startTime;
        
        $responseTimes = array_column($results, 'response_time_ms');
        $successfulRequests = count(array_filter($results, fn($r) => $r['success']));

        $this->successResponse([
            'load_test_results' => [
                'endpoint' => $endpoint,
                'total_requests' => $requests,
                'successful_requests' => $successfulRequests,
                'failed_requests' => $requests - $successfulRequests,
                'success_rate' => round(($successfulRequests / $requests) * 100, 2) . '%',
                'total_time_seconds' => round($totalTime, 2),
                'requests_per_second' => round($requests / $totalTime, 2),
                'response_times' => [
                    'min_ms' => min($responseTimes),
                    'max_ms' => max($responseTimes),
                    'avg_ms' => round(array_sum($responseTimes) / count($responseTimes), 2),
                    'median_ms' => $this->calculateMedian($responseTimes)
                ]
            ],
            'sample_results' => array_slice($results, 0, 5)
        ], '負載測試完成');
    }

    /**
     * 測試單一端點
     * 
     * @param array $endpoint
     * @return array
     */
    private function testEndpoint(array $endpoint): array
    {
        $startTime = microtime(true);
        
        try {
            // 這裡應該實際發送 HTTP 請求，暫時模擬
            $responseTime = (microtime(true) - $startTime) * 1000;
            
            // 模擬不同的響應狀態
            $simulatedStatus = rand(1, 100) <= 95 ? 200 : 500; // 95% 成功率
            
            return [
                'name' => $endpoint['name'],
                'method' => $endpoint['method'],
                'url' => $endpoint['url'],
                'expected_status' => $endpoint['expected_status'],
                'actual_status' => $simulatedStatus,
                'response_time_ms' => round($responseTime, 2),
                'success' => $simulatedStatus === $endpoint['expected_status'],
                'error' => $simulatedStatus !== $endpoint['expected_status'] ? 'Status code mismatch' : null
            ];
            
        } catch (\Exception $e) {
            return [
                'name' => $endpoint['name'],
                'method' => $endpoint['method'],
                'url' => $endpoint['url'],
                'expected_status' => $endpoint['expected_status'],
                'actual_status' => null,
                'response_time_ms' => round((microtime(true) - $startTime) * 1000, 2),
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 計算中位數
     * 
     * @param array $values
     * @return float
     */
    private function calculateMedian(array $values): float
    {
        sort($values);
        $count = count($values);
        
        if ($count % 2 === 0) {
            return ($values[$count / 2 - 1] + $values[$count / 2]) / 2;
        } else {
            return $values[floor($count / 2)];
        }
    }

    /**
     * 取得基礎 URL
     * 
     * @return string
     */
    private function getBaseUrl(): string
    {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        return "{$protocol}://{$host}";
    }
}