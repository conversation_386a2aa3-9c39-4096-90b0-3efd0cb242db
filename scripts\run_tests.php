<?php

/**
 * 測試執行腳本
 * 提供便捷的測試執行和報告功能
 */

// 確保只能從命令行執行
if (php_sapi_name() !== 'cli') {
    die('此腳本只能從命令行執行');
}

class TestRunner
{
    private string $projectRoot;
    private array $config;
    private array $testResults = [];

    public function __construct()
    {
        $this->projectRoot = dirname(__DIR__);
        $this->loadConfig();
    }

    /**
     * 載入配置
     */
    private function loadConfig(): void
    {
        $this->config = [
            'phpunit_path' => $this->findPhpUnit(),
            'test_dir' => $this->projectRoot . '/tests',
            'coverage_dir' => $this->projectRoot . '/tests/coverage',
            'results_dir' => $this->projectRoot . '/tests/results',
            'timeout' => 300 // 5 分鐘
        ];
    }

    /**
     * 尋找 PHPUnit 執行檔
     */
    private function findPhpUnit(): string
    {
        $paths = [
            $this->projectRoot . '/htdocs/vendor/bin/phpunit',
            $this->projectRoot . '/htdocs/vendor/bin/phpunit.phar',
            'phpunit'
        ];

        foreach ($paths as $path) {
            if (file_exists($path) || $this->commandExists($path)) {
                return $path;
            }
        }

        throw new Exception('PHPUnit not found. Please install PHPUnit via Composer.');
    }

    /**
     * 檢查命令是否存在
     */
    private function commandExists(string $command): bool
    {
        $return = shell_exec(sprintf("which %s", escapeshellarg($command)));
        return !empty($return);
    }

    /**
     * 執行所有測試
     */
    public function runAllTests(): void
    {
        echo "Tech.VG 測試執行器\n";
        echo "==================\n\n";

        $this->prepareTestEnvironment();

        $testSuites = [
            'unit' => '單元測試',
            'integration' => '整合測試',
            'performance' => '效能測試'
        ];

        foreach ($testSuites as $suite => $description) {
            echo "執行 {$description}...\n";
            $result = $this->runTestSuite($suite);
            $this->testResults[$suite] = $result;
            
            if ($result['success']) {
                echo "✓ {$description} 完成\n";
            } else {
                echo "✗ {$description} 失敗\n";
            }
            echo "\n";
        }

        $this->generateReport();
    }

    /**
     * 執行特定測試套件
     */
    public function runTestSuite(string $suite): array
    {
        $command = $this->buildPhpUnitCommand($suite);
        
        $startTime = microtime(true);
        $output = [];
        $returnCode = 0;
        
        exec($command . ' 2>&1', $output, $returnCode);
        
        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        return [
            'suite' => $suite,
            'success' => $returnCode === 0,
            'execution_time' => $executionTime,
            'output' => implode("\n", $output),
            'return_code' => $returnCode
        ];
    }

    /**
     * 建立 PHPUnit 命令
     */
    private function buildPhpUnitCommand(string $suite): string
    {
        $phpunit = $this->config['phpunit_path'];
        $configFile = $this->projectRoot . '/phpunit.xml';
        
        $command = "cd {$this->projectRoot} && {$phpunit}";
        
        if (file_exists($configFile)) {
            $command .= " --configuration {$configFile}";
        }
        
        $command .= " --testsuite " . ucfirst($suite);
        
        // 添加覆蓋率報告（僅對單元測試）
        if ($suite === 'unit') {
            $command .= " --coverage-html {$this->config['coverage_dir']}/html";
            $command .= " --coverage-text";
        }
        
        return $command;
    }

    /**
     * 執行特定測試類別
     */
    public function runTestClass(string $className): array
    {
        $command = $this->buildPhpUnitCommand('unit') . " --filter {$className}";
        
        $output = [];
        $returnCode = 0;
        exec($command . ' 2>&1', $output, $returnCode);

        return [
            'class' => $className,
            'success' => $returnCode === 0,
            'output' => implode("\n", $output)
        ];
    }

    /**
     * 準備測試環境
     */
    private function prepareTestEnvironment(): void
    {
        echo "準備測試環境...\n";

        // 建立必要的目錄
        $dirs = [
            $this->config['coverage_dir'],
            $this->config['results_dir'],
            $this->config['test_dir'] . '/data',
            $this->config['test_dir'] . '/uploads',
            $this->config['test_dir'] . '/cache'
        ];

        foreach ($dirs as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
        }

        // 檢查測試資料庫連線
        $this->checkTestDatabase();

        echo "測試環境準備完成\n\n";
    }

    /**
     * 檢查測試資料庫
     */
    private function checkTestDatabase(): void
    {
        try {
            $envFile = $this->config['test_dir'] . '/.env.testing';
            if (file_exists($envFile)) {
                $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
                $dbConfig = [];
                
                foreach ($lines as $line) {
                    if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
                        [$key, $value] = explode('=', $line, 2);
                        $dbConfig[trim($key)] = trim($value);
                    }
                }

                $dsn = sprintf(
                    'mysql:host=%s;dbname=%s;charset=utf8mb4',
                    $dbConfig['DB_HOST'] ?? 'localhost',
                    $dbConfig['DB_DATABASE'] ?? 'techvg_test'
                );

                $pdo = new PDO(
                    $dsn,
                    $dbConfig['DB_USERNAME'] ?? 'test_user',
                    $dbConfig['DB_PASSWORD'] ?? 'test_password'
                );

                echo "✓ 測試資料庫連線正常\n";
            }
        } catch (Exception $e) {
            echo "⚠ 測試資料庫連線失敗: " . $e->getMessage() . "\n";
            echo "  某些測試可能會被跳過\n";
        }
    }

    /**
     * 生成測試報告
     */
    private function generateReport(): void
    {
        echo "\n測試執行報告\n";
        echo "=============\n\n";

        $totalTests = 0;
        $passedTests = 0;
        $totalTime = 0;

        foreach ($this->testResults as $suite => $result) {
            $status = $result['success'] ? '✓ 通過' : '✗ 失敗';
            $time = number_format($result['execution_time'], 2);
            
            echo "{$suite}: {$status} ({$time}s)\n";
            
            if ($result['success']) {
                $passedTests++;
            }
            $totalTests++;
            $totalTime += $result['execution_time'];
        }

        echo "\n總結:\n";
        echo "- 測試套件: {$totalTests}\n";
        echo "- 通過: {$passedTests}\n";
        echo "- 失敗: " . ($totalTests - $passedTests) . "\n";
        echo "- 總執行時間: " . number_format($totalTime, 2) . "s\n";

        // 生成 HTML 報告
        $this->generateHtmlReport();

        // 檢查覆蓋率報告
        $this->checkCoverageReport();
    }

    /**
     * 生成 HTML 報告
     */
    private function generateHtmlReport(): void
    {
        $htmlReport = $this->config['results_dir'] . '/test_report.html';
        
        $html = $this->buildHtmlReport();
        file_put_contents($htmlReport, $html);
        
        echo "\nHTML 報告已生成: {$htmlReport}\n";
    }

    /**
     * 建立 HTML 報告內容
     */
    private function buildHtmlReport(): string
    {
        $timestamp = date('Y-m-d H:i:s');
        
        $html = "<!DOCTYPE html>
<html lang='zh-TW'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Tech.VG 測試報告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .suite { margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .failure { background-color: #f8d7da; border-color: #f5c6cb; }
        .output { background: #f8f9fa; padding: 10px; border-radius: 3px; font-family: monospace; white-space: pre-wrap; }
        .summary { background: #e9ecef; padding: 15px; border-radius: 5px; margin-top: 20px; }
    </style>
</head>
<body>
    <div class='header'>
        <h1>Tech.VG 測試報告</h1>
        <p>生成時間: {$timestamp}</p>
    </div>";

        foreach ($this->testResults as $suite => $result) {
            $statusClass = $result['success'] ? 'success' : 'failure';
            $status = $result['success'] ? '✓ 通過' : '✗ 失敗';
            $time = number_format($result['execution_time'], 2);
            
            $html .= "
    <div class='suite {$statusClass}'>
        <h2>{$suite} - {$status}</h2>
        <p>執行時間: {$time} 秒</p>
        <details>
            <summary>詳細輸出</summary>
            <div class='output'>" . htmlspecialchars($result['output']) . "</div>
        </details>
    </div>";
        }

        $html .= "
    <div class='summary'>
        <h2>執行摘要</h2>
        <ul>
            <li>總測試套件: " . count($this->testResults) . "</li>
            <li>通過: " . count(array_filter($this->testResults, fn($r) => $r['success'])) . "</li>
            <li>失敗: " . count(array_filter($this->testResults, fn($r) => !$r['success'])) . "</li>
            <li>總執行時間: " . number_format(array_sum(array_column($this->testResults, 'execution_time')), 2) . " 秒</li>
        </ul>
    </div>
</body>
</html>";

        return $html;
    }

    /**
     * 檢查覆蓋率報告
     */
    private function checkCoverageReport(): void
    {
        $coverageHtml = $this->config['coverage_dir'] . '/html/index.html';
        
        if (file_exists($coverageHtml)) {
            echo "程式碼覆蓋率報告: {$coverageHtml}\n";
        }
    }

    /**
     * 執行快速測試
     */
    public function runQuickTests(): void
    {
        echo "執行快速測試...\n";
        
        $quickTests = [
            'Tests\\Unit\\Controller\\Api\\SearchControllerTest',
            'Tests\\Unit\\Controller\\Api\\DocumentControllerTest'
        ];

        foreach ($quickTests as $testClass) {
            echo "執行 {$testClass}...\n";
            $result = $this->runTestClass($testClass);
            
            if ($result['success']) {
                echo "✓ 通過\n";
            } else {
                echo "✗ 失敗\n";
                echo $result['output'] . "\n";
            }
        }
    }

    /**
     * 清理測試環境
     */
    public function cleanup(): void
    {
        echo "清理測試環境...\n";
        
        $cleanupDirs = [
            $this->config['test_dir'] . '/uploads',
            $this->config['test_dir'] . '/cache'
        ];

        foreach ($cleanupDirs as $dir) {
            if (is_dir($dir)) {
                $this->removeDirectory($dir);
                mkdir($dir, 0755, true);
            }
        }

        echo "清理完成\n";
    }

    /**
     * 移除目錄
     */
    private function removeDirectory(string $dir): void
    {
        if (!is_dir($dir)) {
            return;
        }

        $files = array_diff(scandir($dir), ['.', '..', '.gitkeep']);
        
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            if (is_dir($path)) {
                $this->removeDirectory($path);
            } else {
                unlink($path);
            }
        }
    }
}

// 主程式
try {
    $runner = new TestRunner();
    
    if ($argc > 1) {
        $command = $argv[1];
        
        switch ($command) {
            case 'all':
                $runner->runAllTests();
                break;
                
            case 'unit':
                $result = $runner->runTestSuite('unit');
                echo $result['success'] ? "單元測試通過\n" : "單元測試失敗\n";
                break;
                
            case 'integration':
                $result = $runner->runTestSuite('integration');
                echo $result['success'] ? "整合測試通過\n" : "整合測試失敗\n";
                break;
                
            case 'performance':
                $result = $runner->runTestSuite('performance');
                echo $result['success'] ? "效能測試通過\n" : "效能測試失敗\n";
                break;
                
            case 'quick':
                $runner->runQuickTests();
                break;
                
            case 'cleanup':
                $runner->cleanup();
                break;
                
            default:
                echo "用法: php run_tests.php [command]\n\n";
                echo "可用命令:\n";
                echo "  all         - 執行所有測試\n";
                echo "  unit        - 執行單元測試\n";
                echo "  integration - 執行整合測試\n";
                echo "  performance - 執行效能測試\n";
                echo "  quick       - 執行快速測試\n";
                echo "  cleanup     - 清理測試環境\n";
                break;
        }
    } else {
        $runner->runAllTests();
    }
    
} catch (Exception $e) {
    echo "錯誤: " . $e->getMessage() . "\n";
    exit(1);
}