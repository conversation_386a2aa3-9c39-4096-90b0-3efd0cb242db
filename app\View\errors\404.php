<?php
/**
 * 404 錯誤頁面
 * 當請求的頁面不存在時顯示
 */

$pageTitle = '404 - 頁面不存在';
$errorCode = '404';
$errorTitle = '頁面不存在';
$errorMessage = '抱歉，您要尋找的頁面不存在或已被移除。';
?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($pageTitle) ?> - Tech.VG</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;700&display=swap');
        
        body {
            font-family: 'Noto Sans TC', sans-serif;
        }
        
        .error-animation {
            animation: float 3s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .btn-hover {
            transition: all 0.3s ease;
        }
        
        .btn-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center">
    <div class="max-w-2xl mx-auto px-4 text-center">
        <!-- 錯誤圖示 -->
        <div class="error-animation mb-8">
            <div class="inline-flex items-center justify-center w-32 h-32 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full shadow-2xl">
                <i class="fas fa-search text-white text-5xl"></i>
            </div>
        </div>
        
        <!-- 錯誤代碼 -->
        <h1 class="text-8xl font-bold text-gray-800 mb-4"><?= htmlspecialchars($errorCode) ?></h1>
        
        <!-- 錯誤標題 -->
        <h2 class="text-3xl font-semibold text-gray-700 mb-6"><?= htmlspecialchars($errorTitle) ?></h2>
        
        <!-- 錯誤訊息 -->
        <p class="text-lg text-gray-600 mb-8 leading-relaxed">
            <?= htmlspecialchars($errorMessage) ?>
        </p>
        
        <!-- 建議操作 -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h3 class="text-xl font-semibold text-gray-800 mb-4">您可以嘗試：</h3>
            <ul class="text-left text-gray-600 space-y-2">
                <li class="flex items-center">
                    <i class="fas fa-check-circle text-green-500 mr-3"></i>
                    檢查網址是否正確
                </li>
                <li class="flex items-center">
                    <i class="fas fa-check-circle text-green-500 mr-3"></i>
                    返回首頁重新開始
                </li>
                <li class="flex items-center">
                    <i class="fas fa-check-circle text-green-500 mr-3"></i>
                    使用搜尋功能尋找內容
                </li>
                <li class="flex items-center">
                    <i class="fas fa-check-circle text-green-500 mr-3"></i>
                    聯繫我們尋求協助
                </li>
            </ul>
        </div>
        
        <!-- 操作按鈕 -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="/" class="btn-hover inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors">
                <i class="fas fa-home mr-2"></i>
                返回首頁
            </a>
            
            <a href="/search" class="btn-hover inline-flex items-center px-6 py-3 bg-purple-600 text-white font-medium rounded-lg hover:bg-purple-700 transition-colors">
                <i class="fas fa-search mr-2"></i>
                搜尋內容
            </a>
            
            <a href="/contact" class="btn-hover inline-flex items-center px-6 py-3 bg-gray-600 text-white font-medium rounded-lg hover:bg-gray-700 transition-colors">
                <i class="fas fa-envelope mr-2"></i>
                聯繫我們
            </a>
        </div>
        
        <!-- 返回按鈕 -->
        <div class="mt-8">
            <button onclick="history.back()" class="text-gray-500 hover:text-gray-700 transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>
                返回上一頁
            </button>
        </div>
    </div>
    
    <!-- 背景裝飾 -->
    <div class="fixed inset-0 -z-10 overflow-hidden">
        <div class="absolute -top-40 -right-32 w-80 h-80 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse"></div>
        <div class="absolute -bottom-40 -left-32 w-80 h-80 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse"></div>
    </div>
</body>
</html>