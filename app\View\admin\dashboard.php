<?php include APP_ROOT . '/app/View/layout/header.php'; ?>

<div class="min-h-screen bg-gray-50">
    <!-- 管理後台導航 -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-semibold text-gray-900">管理後台</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-gray-500">
                        歡迎，<?= htmlspecialchars($_SESSION['user_name'] ?? 'Administrator') ?>
                    </span>
                    <form action="/auth/logout" method="POST" class="inline">
                        <?= csrf_field() ?>
                        <button type="submit" class="text-sm text-red-600 hover:text-red-800">
                            登出
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- 顯示錯誤訊息 -->
        <?php if (isset($error)): ?>
            <div class="mb-4 bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded">
                <span class="block sm:inline"><?= htmlspecialchars($error) ?></span>
            </div>
        <?php endif; ?>

        <!-- 統計卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- 新聞統計 -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-newspaper text-blue-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">
                                    新聞文章
                                </dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    <?= number_format($stats['news']['total']) ?>
                                </dd>
                                <dd class="text-sm text-gray-500">
                                    今日新增：<?= $stats['news']['today'] ?>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 文檔統計 -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-file-alt text-green-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">
                                    文檔數量
                                </dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    <?= number_format($stats['documents']['total']) ?>
                                </dd>
                                <dd class="text-sm text-gray-500">
                                    今日新增：<?= $stats['documents']['today'] ?>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 用戶統計 -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-users text-purple-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">
                                    用戶數量
                                </dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    <?= number_format($stats['users']['total']) ?>
                                </dd>
                                <dd class="text-sm text-gray-500">
                                    今日活躍：<?= $stats['users']['active_today'] ?>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系統狀態 -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-server text-orange-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">
                                    系統狀態
                                </dt>
                                <dd class="text-lg font-medium text-green-600">
                                    正常運行
                                </dd>
                                <dd class="text-sm text-gray-500">
                                    PHP <?= $stats['system']['php_version'] ?>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 詳細資訊 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 系統資訊 -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                        系統資訊
                    </h3>
                    <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">PHP 版本</dt>
                            <dd class="mt-1 text-sm text-gray-900"><?= $stats['system']['php_version'] ?></dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">記憶體使用</dt>
                            <dd class="mt-1 text-sm text-gray-900"><?= format_bytes($stats['system']['memory_usage']) ?></dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">記憶體峰值</dt>
                            <dd class="mt-1 text-sm text-gray-900"><?= format_bytes($stats['system']['memory_peak']) ?></dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">系統運行時間</dt>
                            <dd class="mt-1 text-sm text-gray-900"><?= $stats['system']['uptime'] ?></dd>
                        </div>
                    </dl>
                </div>
            </div>

            <!-- 快速操作 -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                        快速操作
                    </h3>
                    <div class="grid grid-cols-1 gap-4">
                        <a href="/admin/documents" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                            <i class="fas fa-file-alt mr-2"></i>
                            管理文檔
                        </a>
                        <a href="/admin/news" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                            <i class="fas fa-newspaper mr-2"></i>
                            管理新聞
                        </a>
                        <a href="/admin/settings" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700">
                            <i class="fas fa-cog mr-2"></i>
                            系統設定
                        </a>
                        <a href="/error/logs" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            <i class="fas fa-list-alt mr-2"></i>
                            查看日誌
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最近活動 -->
        <div class="mt-8">
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                        最近活動
                    </h3>
                    <div class="text-sm text-gray-500">
                        <p>目前沒有最近活動記錄。</p>
                        <p class="mt-2">
                            <a href="/api/docs" class="text-blue-600 hover:text-blue-800">查看 API 文檔</a> |
                            <a href="/api/status" class="text-blue-600 hover:text-blue-800">系統狀態</a> |
                            <a href="/api/test" class="text-blue-600 hover:text-blue-800">API 測試</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include APP_ROOT . '/app/View/layout/footer.php'; ?>