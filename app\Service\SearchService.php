<?php

namespace App\Service;

use App\Model\DocumentModel;
use App\Model\NewsArticleModel;
use App\Database\Database;
use Exception;

/**
 * 檢索服務
 * 處理文檔和新聞的檢索功能
 */
class SearchService extends BaseService
{
    private DocumentModel $documentModel;
    private NewsArticleModel $newsModel;
    
    public function __construct()
    {
        parent::__construct();
        $this->documentModel = new DocumentModel();
        $this->newsModel = new NewsArticleModel();
    }

    /**
     * 執行檢索
     * 
     * @param string $query 檢索關鍵字
     * @param array $options 檢索選項
     * @return array
     */
    public function search(string $query, array $options = []): array
    {
        try {
            $query = trim($query);
            
            if (empty($query)) {
                return $this->formatResponse(false, null, '檢索關鍵字不能為空');
            }

            if (mb_strlen($query) > $this->config['search']['max_query_length']) {
                return $this->formatResponse(false, null, '檢索關鍵字過長');
            }

            // 處理檢索選項
            $searchOptions = $this->processSearchOptions($options);
            
            // 記錄檢索歷史
            $searchHistoryId = $this->recordSearchHistory($query, $searchOptions);
            
            $results = [];
            $totalResults = 0;

            // 根據類型執行檢索
            switch ($searchOptions['type']) {
                case 'document':
                    $results = $this->searchDocuments($query, $searchOptions);
                    break;
                case 'news':
                    $results = $this->searchNews($query, $searchOptions);
                    break;
                case 'all':
                default:
                    $documentResults = $this->searchDocuments($query, $searchOptions);
                    $newsResults = $this->searchNews($query, $searchOptions);
                    
                    $results = $this->mergeSearchResults($documentResults, $newsResults, $searchOptions);
                    break;
            }

            $totalResults = count($results);

            // 更新檢索歷史
            $this->updateSearchHistory($searchHistoryId, $totalResults);

            // 分頁處理
            $paginatedResults = $this->paginateResults($results, $searchOptions['page'], $searchOptions['per_page']);

            $this->log('info', '檢索完成', [
                'query' => $query,
                'type' => $searchOptions['type'],
                'total_results' => $totalResults,
                'page' => $searchOptions['page']
            ]);

            return $this->formatResponse(true, $paginatedResults['data'], null, [
                'query' => $query,
                'total_results' => $totalResults,
                'pagination' => $paginatedResults['pagination'],
                'search_options' => $searchOptions
            ]);

        } catch (Exception $e) {
            return $this->handleException($e, '執行檢索');
        }
    }

    /**
     * 檢索文檔
     * 
     * @param string $query
     * @param array $options
     * @return array
     */
    public function searchDocuments(string $query, array $options = []): array
    {
        try {
            $searchOptions = $this->processSearchOptions($options);
            
            // 使用MySQL全文檢索
            $sql = "
                SELECT 
                    d.id,
                    d.title,
                    d.fileName,
                    d.fileType,
                    d.fileSize,
                    d.createdAt,
                    dc.wordCount,
                    MATCH(di.chunkContent, di.keywords) AGAINST(? IN NATURAL LANGUAGE MODE) as relevance_score,
                    SUBSTRING(di.chunkContent, 1, 200) as snippet
                FROM document d
                JOIN documentContent dc ON d.id = dc.documentId
                JOIN documentIndex di ON d.id = di.documentId
                WHERE d.status = 'indexed'
                AND MATCH(di.chunkContent, di.keywords) AGAINST(? IN NATURAL LANGUAGE MODE)
                GROUP BY d.id
                ORDER BY relevance_score DESC, d.createdAt DESC
                LIMIT ?
            ";

            $limit = $searchOptions['per_page'] * 3; // 取更多結果用於後續處理
            $results = $this->db->fetchAll($sql, [$query, $query, $limit]);

            // 格式化結果
            $formattedResults = [];
            foreach ($results as $result) {
                $formattedResults[] = [
                    'type' => 'document',
                    'id' => $result['id'],
                    'title' => $result['title'],
                    'snippet' => $this->highlightKeywords($result['snippet'], $query),
                    'file_name' => $result['fileName'],
                    'file_type' => $result['fileType'],
                    'file_size' => $result['fileSize'],
                    'word_count' => $result['wordCount'],
                    'relevance_score' => (float) $result['relevance_score'],
                    'created_at' => $result['createdAt'],
                    'url' => "/documents/{$result['id']}"
                ];
            }

            return $formattedResults;

        } catch (Exception $e) {
            $this->log('error', '文檔檢索失敗', ['query' => $query, 'error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * 檢索新聞
     * 
     * @param string $query
     * @param array $options
     * @return array
     */
    public function searchNews(string $query, array $options = []): array
    {
        try {
            $searchOptions = $this->processSearchOptions($options);
            
            // 使用MySQL全文檢索
            $sql = "
                SELECT 
                    na.id,
                    na.title,
                    na.summary,
                    na.author,
                    na.originalUrl,
                    na.imageUrl,
                    na.publishedAt,
                    na.viewCount,
                    ns.name as source_name,
                    MATCH(na.title, na.content, na.summary) AGAINST(? IN NATURAL LANGUAGE MODE) as relevance_score
                FROM newsArticle na
                JOIN newsSource ns ON na.sourceId = ns.id
                WHERE MATCH(na.title, na.content, na.summary) AGAINST(? IN NATURAL LANGUAGE MODE)
                ORDER BY relevance_score DESC, na.publishedAt DESC
                LIMIT ?
            ";

            $limit = $searchOptions['per_page'] * 3;
            $results = $this->db->fetchAll($sql, [$query, $query, $limit]);

            // 格式化結果
            $formattedResults = [];
            foreach ($results as $result) {
                $formattedResults[] = [
                    'type' => 'news',
                    'id' => $result['id'],
                    'title' => $result['title'],
                    'snippet' => $this->highlightKeywords($result['summary'] ?: '', $query),
                    'author' => $result['author'],
                    'source_name' => $result['source_name'],
                    'image_url' => $result['imageUrl'],
                    'view_count' => $result['viewCount'],
                    'relevance_score' => (float) $result['relevance_score'],
                    'published_at' => $result['publishedAt'],
                    'url' => "/news/{$result['id']}"
                ];
            }

            return $formattedResults;

        } catch (Exception $e) {
            $this->log('error', '新聞檢索失敗', ['query' => $query, 'error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * 取得檢索建議
     * 
     * @param string $query
     * @param int $limit
     * @return array
     */
    public function getSearchSuggestions(string $query, int $limit = 10): array
    {
        try {
            if (mb_strlen($query) < 2) {
                return $this->formatResponse(true, []);
            }

            // 從檢索歷史中取得建議
            $sql = "
                SELECT DISTINCT query, COUNT(*) as frequency
                FROM searchHistory 
                WHERE query LIKE ? 
                AND resultCount > 0
                GROUP BY query
                ORDER BY frequency DESC, query
                LIMIT ?
            ";

            $suggestions = $this->db->fetchAll($sql, ["%{$query}%", $limit]);

            $result = array_map(function($item) {
                return $item['query'];
            }, $suggestions);

            return $this->formatResponse(true, $result);

        } catch (Exception $e) {
            return $this->handleException($e, '取得檢索建議');
        }
    }

    /**
     * 取得熱門檢索關鍵字
     * 
     * @param int $days 天數範圍
     * @param int $limit 數量限制
     * @return array
     */
    public function getPopularSearches(int $days = 7, int $limit = 10): array
    {
        try {
            $startDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));
            
            // 檢查表是否存在
            try {
                $checkSql = "SHOW TABLES LIKE 'searchHistory'";
                $tableExists = $this->db->fetchOne($checkSql);
                
                if (!$tableExists) {
                    // 表不存在，返回空結果
                    return $this->formatResponse(true, []);
                }
            } catch (Exception $e) {
                // 如果檢查失敗，假設表存在並繼續
            }
            
            $sql = "
                SELECT query, COUNT(*) as search_count
                FROM searchHistory 
                WHERE createdAt >= ?
                AND resultCount > 0
                GROUP BY query
                ORDER BY search_count DESC
                LIMIT ?
            ";

            try {
                $results = $this->db->fetchAll($sql, [$startDate, $limit]);
                return $this->formatResponse(true, $results);
            } catch (Exception $e) {
                // 如果查詢失敗，返回空結果
                return $this->formatResponse(true, []);
            }

        } catch (Exception $e) {
            // 捕獲所有異常，返回空結果
            return $this->formatResponse(true, []);
        }
    }

    /**
     * 取得檢索統計
     * 
     * @param int $days 天數範圍
     * @return array
     */
    public function getSearchStatistics(int $days = 30): array
    {
        try {
            $startDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));
            
            $sql = "
                SELECT 
                    COUNT(*) as total_searches,
                    COUNT(DISTINCT query) as unique_queries,
                    AVG(resultCount) as avg_results,
                    AVG(responseTime) as avg_response_time,
                    COUNT(CASE WHEN resultCount = 0 THEN 1 END) as zero_result_searches
                FROM searchHistory 
                WHERE createdAt >= ?
            ";

            $stats = $this->db->fetchOne($sql, [$startDate]);

            // 取得每日檢索統計
            $dailyStats = $this->getDailySearchStats($days);

            $result = array_merge($stats ?: [], [
                'daily_stats' => $dailyStats,
                'period_days' => $days
            ]);

            return $this->formatResponse(true, $result);

        } catch (Exception $e) {
            return $this->handleException($e, '取得檢索統計');
        }
    }

    /**
     * 處理檢索選項
     * 
     * @param array $options
     * @return array
     */
    private function processSearchOptions(array $options): array
    {
        return [
            'type' => $options['type'] ?? 'all', // all, document, news
            'page' => max(1, $options['page'] ?? 1),
            'per_page' => min(max(1, $options['per_page'] ?? 20), 100),
            'sort' => $options['sort'] ?? 'relevance', // relevance, date
            'date_from' => $options['date_from'] ?? null,
            'date_to' => $options['date_to'] ?? null,
            'file_type' => $options['file_type'] ?? null
        ];
    }

    /**
     * 記錄檢索歷史
     * 
     * @param string $query
     * @param array $options
     * @return int
     */
    private function recordSearchHistory(string $query, array $options): int
    {
        $sql = "
            INSERT INTO searchHistory (query, queryType, userId, ipAddress, userAgent, createdAt)
            VALUES (?, ?, ?, ?, ?, NOW())
        ";

        return $this->db->insert($sql, [
            $query,
            'keyword',
            $_SESSION['user_id'] ?? null,
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null
        ]);
    }

    /**
     * 更新檢索歷史
     * 
     * @param int $historyId
     * @param int $resultCount
     * @param float|null $responseTime
     */
    private function updateSearchHistory(int $historyId, int $resultCount, ?float $responseTime = null): void
    {
        $sql = "UPDATE searchHistory SET resultCount = ?, responseTime = ? WHERE id = ?";
        $this->db->update($sql, [$resultCount, $responseTime, $historyId]);
    }

    /**
     * 合併檢索結果
     * 
     * @param array $documentResults
     * @param array $newsResults
     * @param array $options
     * @return array
     */
    private function mergeSearchResults(array $documentResults, array $newsResults, array $options): array
    {
        $allResults = array_merge($documentResults, $newsResults);

        // 根據相關性分數排序
        usort($allResults, function($a, $b) {
            return $b['relevance_score'] <=> $a['relevance_score'];
        });

        return $allResults;
    }

    /**
     * 分頁處理
     * 
     * @param array $results
     * @param int $page
     * @param int $perPage
     * @return array
     */
    private function paginateResults(array $results, int $page, int $perPage): array
    {
        $total = count($results);
        $offset = ($page - 1) * $perPage;
        $data = array_slice($results, $offset, $perPage);

        return [
            'data' => $data,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $total,
                'total_pages' => ceil($total / $perPage),
                'has_next' => $page < ceil($total / $perPage),
                'has_prev' => $page > 1
            ]
        ];
    }

    /**
     * 高亮關鍵字
     * 
     * @param string $text
     * @param string $query
     * @return string
     */
    private function highlightKeywords(string $text, string $query): string
    {
        $keywords = preg_split('/\s+/', $query);
        
        foreach ($keywords as $keyword) {
            if (mb_strlen($keyword) >= 2) {
                $text = preg_replace(
                    '/(' . preg_quote($keyword, '/') . ')/iu',
                    '<mark>$1</mark>',
                    $text
                );
            }
        }

        return $text;
    }

    /**
     * 取得每日檢索統計
     * 
     * @param int $days
     * @return array
     */
    private function getDailySearchStats(int $days): array
    {
        $sql = "
            SELECT 
                DATE(createdAt) as date,
                COUNT(*) as search_count,
                COUNT(DISTINCT query) as unique_queries
            FROM searchHistory 
            WHERE createdAt >= DATE_SUB(NOW(), INTERVAL ? DAY)
            GROUP BY DATE(createdAt)
            ORDER BY date DESC
        ";

        return $this->db->fetchAll($sql, [$days]);
    }
    
    /**
     * 記錄檢索歷史 (公開方法)
     * 
     * @param string $query 檢索關鍵字
     * @param string $type 檢索類型
     * @param int $resultCount 結果數量
     * @param float|null $responseTime 響應時間
     * @return int 歷史記錄ID
     */
    public function saveSearchHistory(string $query, string $type, int $resultCount = 0, ?float $responseTime = null): int
    {
        // 確保 queryType 是有效的值
        $queryType = 'keyword'; // 預設值
        if ($type === 'phrase') {
            $queryType = 'phrase';
        }
        
        $sql = "
            INSERT INTO searchHistory (query, queryType, resultCount, responseTime, userId, ipAddress, userAgent, createdAt)
            VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
        ";

        return $this->db->insert($sql, [
            $query,
            $queryType,
            $resultCount,
            $responseTime,
            $_SESSION['user_id'] ?? null,
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null
        ]);
    }
}