<?php
/**
 * 文檔詳情頁面
 */

$document = $document ?? [];
$error = $error ?? '';

include __DIR__ . '/../layout/header.php';
?>

<?php if ($error): ?>
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="bg-red-50 border border-red-200 rounded-md p-4">
            <div class="flex">
                <i class="fas fa-exclamation-circle text-red-400 mr-3 mt-1"></i>
                <div class="text-red-700"><?= htmlspecialchars($error) ?></div>
            </div>
        </div>
    </div>
<?php else: ?>
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 返回按鈕 -->
        <div class="mb-6">
            <a href="/documents" class="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>返回文檔列表
            </a>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <!-- 主要內容 -->
            <div class="lg:col-span-3">
                <!-- 文檔標題 -->
                <header class="mb-8">
                    <h1 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                        <?= htmlspecialchars($document['title'] ?? $document['filename'] ?? '') ?>
                    </h1>

                    <!-- 文檔元資訊 -->
                    <div class="flex flex-wrap items-center text-gray-600 text-sm space-x-4 mb-6">
                        <div class="flex items-center">
                            <i class="fas fa-file-alt mr-2"></i>
                            <?= strtoupper($document['file_type'] ?? 'unknown') ?> 檔案
                        </div>

                        <div class="flex items-center">
                            <i class="fas fa-hdd mr-2"></i>
                            <?= $this->formatBytes($document['file_size'] ?? 0) ?>
                        </div>

                        <div class="flex items-center">
                            <i class="fas fa-clock mr-2"></i>
                            <?= date('Y年m月d日 H:i', strtotime($document['created_at'] ?? 'now')) ?>
                        </div>

                        <div class="flex items-center">
                            <i class="fas fa-eye mr-2"></i>
                            <?= number_format($document['views'] ?? 0) ?> 次檢視
                        </div>

                        <?php
                        $status = $document['status'] ?? 'unknown';
                        $statusClass = [
                            'active' => 'bg-green-100 text-green-800',
                            'inactive' => 'bg-red-100 text-red-800',
                            'processing' => 'bg-yellow-100 text-yellow-800'
                        ][$status] ?? 'bg-gray-100 text-gray-800';
                        
                        $statusText = [
                            'active' => '已啟用',
                            'inactive' => '已停用',
                            'processing' => '處理中'
                        ][$status] ?? '未知';
                        ?>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $statusClass ?>">
                            <?= $statusText ?>
                        </span>
                    </div>

                    <!-- 操作按鈕 -->
                    <div class="flex flex-wrap items-center gap-3 mb-6">
                        <a href="/documents/<?= $document['id'] ?>/download" 
                           class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                            <i class="fas fa-download mr-2"></i>下載檔案
                        </a>

                        <a href="/documents/<?= $document['id'] ?>/edit" 
                           class="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 bg-white rounded-md hover:bg-gray-50 transition-colors">
                            <i class="fas fa-edit mr-2"></i>編輯
                        </a>

                        <button onclick="reindexDocument()" 
                                class="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 bg-white rounded-md hover:bg-gray-50 transition-colors">
                            <i class="fas fa-sync-alt mr-2"></i>重建索引
                        </button>

                        <button onclick="shareDocument()" 
                                class="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 bg-white rounded-md hover:bg-gray-50 transition-colors">
                            <i class="fas fa-share mr-2"></i>分享
                        </button>

                        <button onclick="deleteDocument()" 
                                class="inline-flex items-center px-4 py-2 border border-red-300 text-red-700 bg-white rounded-md hover:bg-red-50 transition-colors">
                            <i class="fas fa-trash mr-2"></i>刪除
                        </button>
                    </div>
                </header>

                <!-- 文檔內容 -->
                <div class="bg-white rounded-lg shadow-md">
                    <!-- 內容標籤 -->
                    <div class="border-b border-gray-200">
                        <nav class="flex space-x-8 px-6" aria-label="Tabs">
                            <button onclick="showTab('content')" 
                                    id="contentTab"
                                    class="tab-button border-b-2 border-blue-500 py-4 px-1 text-sm font-medium text-blue-600">
                                文檔內容
                            </button>
                            <button onclick="showTab('preview')" 
                                    id="previewTab"
                                    class="tab-button border-b-2 border-transparent py-4 px-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300">
                                預覽
                            </button>
                            <button onclick="showTab('metadata')" 
                                    id="metadataTab"
                                    class="tab-button border-b-2 border-transparent py-4 px-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300">
                                詳細資訊
                            </button>
                        </nav>
                    </div>

                    <!-- 內容區域 -->
                    <div class="p-6">
                        <!-- 原始內容 -->
                        <div id="contentPanel" class="tab-panel">
                            <?php if (!empty($document['content'])): ?>
                                <pre class="whitespace-pre-wrap text-sm text-gray-800 bg-gray-50 p-4 rounded-lg overflow-x-auto"><code><?= htmlspecialchars($document['content']) ?></code></pre>
                            <?php else: ?>
                                <div class="text-center py-8 text-gray-500">
                                    <i class="fas fa-file-alt text-4xl mb-4"></i>
                                    <p>文檔內容暫時無法顯示</p>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- 預覽 -->
                        <div id="previewPanel" class="tab-panel hidden">
                            <?php if (!empty($document['content'])): ?>
                                <?php if (($document['file_type'] ?? '') === 'md' || ($document['file_type'] ?? '') === 'markdown'): ?>
                                    <div id="markdownPreview" class="prose max-w-none">
                                        <!-- Markdown 預覽將在這裡渲染 -->
                                    </div>
                                <?php else: ?>
                                    <div class="text-sm text-gray-800 leading-relaxed">
                                        <?= nl2br(htmlspecialchars($document['content'])) ?>
                                    </div>
                                <?php endif; ?>
                            <?php else: ?>
                                <div class="text-center py-8 text-gray-500">
                                    <i class="fas fa-eye-slash text-4xl mb-4"></i>
                                    <p>無法預覽此文檔</p>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- 詳細資訊 -->
                        <div id="metadataPanel" class="tab-panel hidden">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 mb-4">基本資訊</h3>
                                    <dl class="space-y-3">
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">檔案名稱</dt>
                                            <dd class="text-sm text-gray-900"><?= htmlspecialchars($document['filename'] ?? '') ?></dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">檔案類型</dt>
                                            <dd class="text-sm text-gray-900"><?= strtoupper($document['file_type'] ?? 'unknown') ?></dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">檔案大小</dt>
                                            <dd class="text-sm text-gray-900"><?= $this->formatBytes($document['file_size'] ?? 0) ?></dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">上傳時間</dt>
                                            <dd class="text-sm text-gray-900"><?= date('Y-m-d H:i:s', strtotime($document['created_at'] ?? 'now')) ?></dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">最後更新</dt>
                                            <dd class="text-sm text-gray-900"><?= date('Y-m-d H:i:s', strtotime($document['updated_at'] ?? 'now')) ?></dd>
                                        </div>
                                    </dl>
                                </div>

                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 mb-4">索引資訊</h3>
                                    <dl class="space-y-3">
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">索引狀態</dt>
                                            <dd class="text-sm text-gray-900">
                                                <?php if (($document['indexed'] ?? false)): ?>
                                                    <span class="text-green-600">已建立索引</span>
                                                <?php else: ?>
                                                    <span class="text-yellow-600">待建立索引</span>
                                                <?php endif; ?>
                                            </dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">字數統計</dt>
                                            <dd class="text-sm text-gray-900"><?= number_format($document['word_count'] ?? 0) ?> 字</dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">檢視次數</dt>
                                            <dd class="text-sm text-gray-900"><?= number_format($document['views'] ?? 0) ?> 次</dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">檔案雜湊</dt>
                                            <dd class="text-sm text-gray-900 font-mono"><?= htmlspecialchars($document['hash'] ?? 'N/A') ?></dd>
                                        </div>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 側邊欄 -->
            <div class="lg:col-span-1">
                <!-- 快速操作 -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">快速操作</h3>
                    <div class="space-y-3">
                        <button onclick="copyContent()" 
                                class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors">
                            <i class="fas fa-copy mr-2"></i>複製內容
                        </button>
                        <button onclick="printDocument()" 
                                class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors">
                            <i class="fas fa-print mr-2"></i>列印文檔
                        </button>
                        <button onclick="exportDocument()" 
                                class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors">
                            <i class="fas fa-file-export mr-2"></i>匯出文檔
                        </button>
                    </div>
                </div>

                <!-- 相關文檔 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">相關文檔</h3>
                    <div class="text-center text-gray-500 text-sm py-4">
                        <i class="fas fa-search text-2xl mb-2"></i>
                        <p>暫無相關文檔</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<script>
    // 標籤切換
    function showTab(tabName) {
        // 隱藏所有面板
        document.querySelectorAll('.tab-panel').forEach(panel => {
            panel.classList.add('hidden');
        });
        
        // 重置所有標籤樣式
        document.querySelectorAll('.tab-button').forEach(button => {
            button.classList.remove('border-blue-500', 'text-blue-600');
            button.classList.add('border-transparent', 'text-gray-500');
        });
        
        // 顯示選中的面板
        document.getElementById(tabName + 'Panel').classList.remove('hidden');
        
        // 設定選中標籤樣式
        const activeTab = document.getElementById(tabName + 'Tab');
        activeTab.classList.remove('border-transparent', 'text-gray-500');
        activeTab.classList.add('border-blue-500', 'text-blue-600');
        
        // 如果是預覽標籤且是 Markdown 文件，渲染 Markdown
        if (tabName === 'preview' && (<?= json_encode(($document['file_type'] ?? '') === 'md' || ($document['file_type'] ?? '') === 'markdown') ?>)) {
            renderMarkdown();
        }
    }

    // 渲染 Markdown
    function renderMarkdown() {
        const content = <?= json_encode($document['content'] ?? '') ?>;
        const preview = document.getElementById('markdownPreview');
        
        if (content && preview) {
            // 簡單的 Markdown 渲染（實際應用中建議使用專業的 Markdown 解析器）
            let html = content
                .replace(/^### (.*$)/gim, '<h3>$1</h3>')
                .replace(/^## (.*$)/gim, '<h2>$1</h2>')
                .replace(/^# (.*$)/gim, '<h1>$1</h1>')
                .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
                .replace(/\*(.*)\*/gim, '<em>$1</em>')
                .replace(/\n/gim, '<br>');
            
            preview.innerHTML = html;
        }
    }

    // 重建索引
    function reindexDocument() {
        if (confirm('確定要重建此文檔的索引嗎？')) {
            ajaxRequest(`/documents/<?= $document['id'] ?>/reindex`, {
                method: 'POST'
            })
            .then(response => {
                if (response.success) {
                    showNotification('索引重建成功', 'success');
                } else {
                    showNotification(response.error || '重建索引失敗', 'error');
                }
            })
            .catch(error => {
                showNotification('重建索引失敗，請稍後再試', 'error');
            });
        }
    }

    // 分享文檔
    function shareDocument() {
        const url = window.location.href;
        if (navigator.share) {
            navigator.share({
                title: <?= json_encode($document['title'] ?? $document['filename'] ?? '') ?>,
                url: url
            });
        } else {
            navigator.clipboard.writeText(url).then(() => {
                showNotification('連結已複製到剪貼簿', 'success');
            });
        }
    }

    // 刪除文檔
    function deleteDocument() {
        if (confirm('確定要刪除此文檔嗎？此操作無法復原。')) {
            ajaxRequest(`/documents/<?= $document['id'] ?>`, {
                method: 'DELETE'
            })
            .then(response => {
                if (response.success) {
                    showNotification('文檔已刪除', 'success');
                    setTimeout(() => {
                        window.location.href = '/documents';
                    }, 1000);
                } else {
                    showNotification(response.error || '刪除失敗', 'error');
                }
            })
            .catch(error => {
                showNotification('刪除失敗，請稍後再試', 'error');
            });
        }
    }

    // 複製內容
    function copyContent() {
        const content = <?= json_encode($document['content'] ?? '') ?>;
        if (content) {
            navigator.clipboard.writeText(content).then(() => {
                showNotification('內容已複製到剪貼簿', 'success');
            });
        }
    }

    // 列印文檔
    function printDocument() {
        window.print();
    }

    // 匯出文檔
    function exportDocument() {
        window.location.href = `/documents/<?= $document['id'] ?>/download`;
    }
</script>

<?php include __DIR__ . '/../layout/footer.php'; ?>