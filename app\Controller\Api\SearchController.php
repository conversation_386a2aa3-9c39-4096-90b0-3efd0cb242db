<?php

namespace App\Controller\Api;

use App\Service\SearchService;

/**
 * 搜尋 API 控制器
 * 處理搜尋相關的 API 請求
 */
class SearchController extends BaseApiController
{
    private SearchService $searchService;

    public function __construct()
    {
        parent::__construct();
        $this->searchService = new SearchService();
    }

    /**
     * 執行搜尋
     * 
     * GET/POST /api/search
     * 
     * 參數：
     * - q: 搜尋關鍵字 (必填)
     * - type: 搜尋類型 (all, document, news)
     * - page: 頁碼
     * - per_page: 每頁項目數
     * - sort_by: 排序欄位 (relevance, date)
     * - sort_direction: 排序方向 (asc, desc)
     * - date_from: 開始日期
     * - date_to: 結束日期
     */
    public function search(): void
    {
        try {
            // 檢查速率限制
            $clientIp = $this->getClientIp();
            if (!$this->checkRateLimit($clientIp, 200, 3600)) {
                $this->errorResponse('請求過於頻繁，請稍後再試', 429, [], 'RATE_LIMIT_EXCEEDED');
            }

            // 取得請求資料
            $data = $this->getRequestData();
            
            // 驗證請求參數
            $rules = [
                'q' => 'required|min:1|max:500',
                'type' => 'in:all,document,news',
                'page' => 'integer|min:1',
                'per_page' => 'integer|min:1|max:100',
                'sort_by' => 'in:relevance,date,title',
                'sort_direction' => 'in:asc,desc',
                'date_from' => '',
                'date_to' => ''
            ];

            $errors = $this->validateRequest($rules, $data);
            if (!empty($errors)) {
                $this->errorResponse('請求參數驗證失敗', 400, $errors, 'VALIDATION_ERROR');
            }

            // 取得搜尋參數
            $query = trim($data['q']);
            $type = $data['type'] ?? 'all';
            $pagination = $this->getPaginationParams();
            $sort = $this->getSortParams(['relevance', 'date', 'title'], 'relevance', 'desc');
            
            // 建立搜尋選項
            $searchOptions = [
                'type' => $type,
                'page' => $pagination['page'],
                'per_page' => $pagination['per_page'],
                'sort_by' => $sort['sort_by'],
                'sort_direction' => $sort['sort_direction']
            ];

            // 日期篩選
            if (!empty($data['date_from'])) {
                $searchOptions['date_from'] = $data['date_from'];
            }
            if (!empty($data['date_to'])) {
                $searchOptions['date_to'] = $data['date_to'];
            }

            // 執行搜尋
            $startTime = microtime(true);
            $result = $this->searchService->search($query, $searchOptions);
            $searchTime = round(microtime(true) - $startTime, 3);

            if (!$result['success']) {
                $this->errorResponse($result['message'] ?? '搜尋失敗', 500, [], 'SEARCH_ERROR');
            }

            // 記錄搜尋歷史
            $this->searchService->recordSearchHistory(
                $query, 
                $type, 
                $result['meta']['total_results'] ?? 0, 
                $searchTime
            );

            // 準備響應資料
            $responseData = [
                'query' => $query,
                'results' => $result['data'] ?? [],
                'search_time' => $searchTime,
                'suggestions' => $result['suggestions'] ?? []
            ];

            // 準備元資料
            $meta = [
                'search_options' => $searchOptions,
                'total_results' => $result['meta']['total_results'] ?? 0,
                'pagination' => [
                    'current_page' => $pagination['page'],
                    'per_page' => $pagination['per_page'],
                    'total_pages' => $result['meta']['total_pages'] ?? 1,
                    'has_next_page' => ($pagination['page'] * $pagination['per_page']) < ($result['meta']['total_results'] ?? 0),
                    'has_prev_page' => $pagination['page'] > 1
                ]
            ];

            $this->successResponse($responseData, '搜尋完成', 200, $meta);

        } catch (\Exception $e) {
            $this->errorResponse('搜尋服務暫時不可用', 500, [], 'SEARCH_SERVICE_ERROR');
        }
    }

    /**
     * 取得搜尋建議
     * 
     * GET /api/search/suggestions
     * 
     * 參數：
     * - q: 搜尋關鍵字 (必填)
     * - limit: 建議數量限制 (預設: 5, 最大: 20)
     */
    public function suggestions(): void
    {
        try {
            // 檢查速率限制
            $clientIp = $this->getClientIp();
            if (!$this->checkRateLimit($clientIp, 500, 3600)) {
                $this->errorResponse('請求過於頻繁，請稍後再試', 429, [], 'RATE_LIMIT_EXCEEDED');
            }

            $query = trim($this->getGet('q', ''));
            $limit = min(20, max(1, (int)$this->getGet('limit', 5)));

            if (empty($query)) {
                $this->errorResponse('搜尋關鍵字不能為空', 400, [], 'MISSING_QUERY');
            }

            if (strlen($query) < 2) {
                $this->successResponse([], '關鍵字太短，無法提供建議');
            }

            $suggestions = $this->searchService->getSuggestions($query, $limit);

            $this->successResponse([
                'query' => $query,
                'suggestions' => $suggestions
            ], '建議取得成功');

        } catch (\Exception $e) {
            $this->errorResponse('建議服務暫時不可用', 500, [], 'SUGGESTION_SERVICE_ERROR');
        }
    }

    /**
     * 取得熱門搜尋
     * 
     * GET /api/search/popular
     * 
     * 參數：
     * - days: 統計天數 (預設: 7)
     * - limit: 結果數量限制 (預設: 10, 最大: 50)
     */
    public function popular(): void
    {
        try {
            $days = max(1, min(30, (int)$this->getGet('days', 7)));
            $limit = min(50, max(1, (int)$this->getGet('limit', 10)));

            $result = $this->searchService->getPopularSearches($days, $limit);

            if (!$result['success']) {
                $this->errorResponse($result['message'] ?? '取得熱門搜尋失敗', 500, [], 'POPULAR_SEARCH_ERROR');
            }

            $this->successResponse([
                'popular_searches' => $result['data'] ?? [],
                'period_days' => $days
            ], '熱門搜尋取得成功');

        } catch (\Exception $e) {
            $this->errorResponse('熱門搜尋服務暫時不可用', 500, [], 'POPULAR_SEARCH_SERVICE_ERROR');
        }
    }

    /**
     * 取得搜尋統計
     * 
     * GET /api/search/stats
     * 
     * 參數：
     * - days: 統計天數 (預設: 30)
     */
    public function stats(): void
    {
        try {
            $days = max(1, min(365, (int)$this->getGet('days', 30)));

            $result = $this->searchService->getSearchStatistics($days);

            if (!$result['success']) {
                $this->errorResponse($result['message'] ?? '取得搜尋統計失敗', 500, [], 'SEARCH_STATS_ERROR');
            }

            $this->successResponse([
                'statistics' => $result['data'] ?? [],
                'period_days' => $days
            ], '搜尋統計取得成功');

        } catch (\Exception $e) {
            $this->errorResponse('搜尋統計服務暫時不可用', 500, [], 'SEARCH_STATS_SERVICE_ERROR');
        }
    }

    /**
     * 進階搜尋
     * 
     * POST /api/search/advanced
     * 
     * 請求體：
     * - query: 主要搜尋關鍵字
     * - title: 標題搜尋
     * - content: 內容搜尋
     * - author: 作者搜尋
     * - type: 搜尋類型
     * - document_types: 文檔類型陣列
     * - news_categories: 新聞分類陣列
     * - date_range: 日期範圍
     * - date_from: 開始日期
     * - date_to: 結束日期
     * - sort_by: 排序欄位
     * - sort_direction: 排序方向
     */
    public function advanced(): void
    {
        try {
            // 只允許 POST 請求
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                $this->errorResponse('只支援 POST 請求', 405, [], 'METHOD_NOT_ALLOWED');
            }

            // 檢查速率限制
            $clientIp = $this->getClientIp();
            if (!$this->checkRateLimit($clientIp, 100, 3600)) {
                $this->errorResponse('請求過於頻繁，請稍後再試', 429, [], 'RATE_LIMIT_EXCEEDED');
            }

            $data = $this->getRequestData();

            // 驗證至少有一個搜尋條件
            if (empty($data['query']) && empty($data['title']) && empty($data['content']) && empty($data['author'])) {
                $this->errorResponse('至少需要提供一個搜尋條件', 400, [], 'NO_SEARCH_CRITERIA');
            }

            // 驗證請求參數
            $rules = [
                'query' => 'max:500',
                'title' => 'max:255',
                'content' => 'max:1000',
                'author' => 'max:100',
                'type' => 'in:all,document,news',
                'sort_by' => 'in:relevance,date,title',
                'sort_direction' => 'in:asc,desc'
            ];

            $errors = $this->validateRequest($rules, $data);
            if (!empty($errors)) {
                $this->errorResponse('請求參數驗證失敗', 400, $errors, 'VALIDATION_ERROR');
            }

            $pagination = $this->getPaginationParams();

            // 執行進階搜尋
            $startTime = microtime(true);
            $result = $this->searchService->advancedSearch($data, $pagination['page'], $pagination['per_page']);
            $searchTime = round(microtime(true) - $startTime, 3);

            if (!$result['success']) {
                $this->errorResponse($result['message'] ?? '進階搜尋失敗', 500, [], 'ADVANCED_SEARCH_ERROR');
            }

            // 記錄搜尋歷史
            $queryString = $this->buildAdvancedQueryString($data);
            $this->searchService->recordSearchHistory(
                $queryString, 
                'advanced', 
                $result['total'] ?? 0, 
                $searchTime
            );

            // 準備響應資料
            $responseData = [
                'search_criteria' => $data,
                'results' => $result['results'] ?? [],
                'search_time' => $searchTime
            ];

            // 準備元資料
            $meta = [
                'total_results' => $result['total'] ?? 0,
                'pagination' => [
                    'current_page' => $pagination['page'],
                    'per_page' => $pagination['per_page'],
                    'total_pages' => $result['totalPages'] ?? 1,
                    'has_next_page' => ($pagination['page'] * $pagination['per_page']) < ($result['total'] ?? 0),
                    'has_prev_page' => $pagination['page'] > 1
                ]
            ];

            $this->successResponse($responseData, '進階搜尋完成', 200, $meta);

        } catch (\Exception $e) {
            $this->errorResponse('進階搜尋服務暫時不可用', 500, [], 'ADVANCED_SEARCH_SERVICE_ERROR');
        }
    }

    /**
     * 建立進階搜尋查詢字串
     * 
     * @param array $params 搜尋參數
     * @return string
     */
    private function buildAdvancedQueryString(array $params): string
    {
        $parts = [];

        if (!empty($params['query'])) {
            $parts[] = $params['query'];
        }

        if (!empty($params['title'])) {
            $parts[] = "標題:{$params['title']}";
        }

        if (!empty($params['content'])) {
            $parts[] = "內容:{$params['content']}";
        }

        if (!empty($params['author'])) {
            $parts[] = "作者:{$params['author']}";
        }

        return implode(' ', $parts);
    }

    /**
     * 清除搜尋歷史
     * 
     * DELETE /api/search/history
     */
    public function clearHistory(): void
    {
        try {
            // 只允許 DELETE 請求
            if ($_SERVER['REQUEST_METHOD'] !== 'DELETE') {
                $this->errorResponse('只支援 DELETE 請求', 405, [], 'METHOD_NOT_ALLOWED');
            }

            $result = $this->searchService->clearSearchHistory();

            if (!$result['success']) {
                $this->errorResponse($result['message'] ?? '清除搜尋歷史失敗', 500, [], 'CLEAR_HISTORY_ERROR');
            }

            $this->successResponse(null, '搜尋歷史已清除');

        } catch (\Exception $e) {
            $this->errorResponse('清除搜尋歷史服務暫時不可用', 500, [], 'CLEAR_HISTORY_SERVICE_ERROR');
        }
    }

    /**
     * 取得搜尋歷史
     * 
     * GET /api/search/history
     * 
     * 參數：
     * - page: 頁碼
     * - per_page: 每頁項目數
     */
    public function history(): void
    {
        try {
            $pagination = $this->getPaginationParams();

            $history = $this->searchService->getSearchHistory($pagination['page'], $pagination['per_page']);
            $totalHistory = $this->searchService->getTotalSearchHistory();

            $this->paginatedResponse(
                $history,
                $totalHistory,
                $pagination['page'],
                $pagination['per_page'],
                '搜尋歷史取得成功'
            );

        } catch (\Exception $e) {
            $this->errorResponse('搜尋歷史服務暫時不可用', 500, [], 'SEARCH_HISTORY_SERVICE_ERROR');
        }
    }
}