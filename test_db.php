<?php
/**
 * 資料庫連接測試腳本
 */

// 載入環境變數
if (file_exists('.env')) {
    $lines = file('.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        $line = trim($line);
        if (!empty($line) && strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            [$key, $value] = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
            echo "載入環境變數: " . trim($key) . " = " . trim($value) . "\n";
        }
    }
    echo "\n";
} else {
    echo "❌ .env 文件不存在\n";
}

$host = $_ENV['DB_HOST'] ?? 'localhost';
$port = $_ENV['DB_PORT'] ?? '3306';
$database = $_ENV['DB_DATABASE'] ?? 'techvg';
$username = $_ENV['DB_USERNAME'] ?? 'root';
$password = $_ENV['DB_PASSWORD'] ?? '';

echo "測試資料庫連接...\n";
echo "主機: {$host}:{$port}\n";
echo "資料庫: {$database}\n";
echo "用戶: {$username}\n";
echo "密碼: " . (empty($password) ? '(空)' : '***') . "\n\n";

// 測試網路連接
echo "測試網路連接...\n";
$socket = @fsockopen($host, $port, $errno, $errstr, 10);
if ($socket) {
    echo "✅ 網路連接成功！\n";
    fclose($socket);
} else {
    echo "❌ 網路連接失敗: {$errstr} (錯誤代碼: {$errno})\n";
}
echo "\n";

try {
    $dsn = "mysql:host={$host};port={$port};dbname={$database};charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
        PDO::ATTR_TIMEOUT => 30,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
    ]);
    
    echo "✅ 資料庫連接成功！\n";
    
    // 測試查詢
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM document");
    $result = $stmt->fetch();
    echo "✅ 查詢測試成功！文檔表有 {$result['count']} 筆記錄\n";
    
} catch (PDOException $e) {
    echo "❌ 資料庫連接失敗: " . $e->getMessage() . "\n";
    echo "錯誤代碼: " . $e->getCode() . "\n";
}
?>
