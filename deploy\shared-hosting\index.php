<?php
/**
 * 共享主機主要入口點
 * 
 * 這個檔案應該放在 htdocs 根目錄
 */

// 錯誤報告設定（生產環境）
error_reporting(0);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// 設定時區
date_default_timezone_set('Asia/Taipei');

// 定義路徑常數
define('ROOT_PATH', __DIR__);
define('APP_PATH', ROOT_PATH . '/app');
define('CONFIG_PATH', ROOT_PATH . '/config');
define('STORAGE_PATH', ROOT_PATH . '/storage');
define('UPLOADS_PATH', ROOT_PATH . '/uploads');

// 檢查必要目錄
$requiredDirs = [APP_PATH, CONFIG_PATH, STORAGE_PATH, UPLOADS_PATH];
foreach ($requiredDirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// 載入環境配置
if (file_exists(ROOT_PATH . '/.env')) {
    $envContent = file_get_contents(ROOT_PATH . '/.env');
    $envLines = explode("\n", $envContent);
    
    foreach ($envLines as $line) {
        $line = trim($line);
        if (empty($line) || strpos($line, '#') === 0) {
            continue;
        }
        
        if (strpos($line, '=') !== false) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

// 設定錯誤處理
if (isset($_ENV['APP_DEBUG']) && $_ENV['APP_DEBUG'] === 'true') {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
}

// 自動載入器
spl_autoload_register(function ($className) {
    // 將命名空間轉換為檔案路徑
    $className = str_replace('\\', '/', $className);
    $className = str_replace('App/', '', $className);
    
    $file = APP_PATH . '/' . $className . '.php';
    
    if (file_exists($file)) {
        require_once $file;
    }
});

// 載入輔助函數
if (file_exists(APP_PATH . '/helpers.php')) {
    require_once APP_PATH . '/helpers.php';
}

// 啟動會話
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

try {
    // 載入路由配置
    if (file_exists(CONFIG_PATH . '/routes.php')) {
        $router = require CONFIG_PATH . '/routes.php';
        
        // 取得當前 URI
        $requestUri = $_SERVER['REQUEST_URI'] ?? '/';
        $requestMethod = $_SERVER['REQUEST_METHOD'] ?? 'GET';
        
        // 移除查詢字串
        $uri = parse_url($requestUri, PHP_URL_PATH);
        
        // 處理路由
        $router->dispatch($requestMethod, $uri);
    } else {
        throw new Exception('路由配置檔案不存在');
    }
    
} catch (Exception $e) {
    // 錯誤處理
    http_response_code(500);
    
    if (isset($_ENV['APP_DEBUG']) && $_ENV['APP_DEBUG'] === 'true') {
        echo '<h1>應用程式錯誤</h1>';
        echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '<pre>' . htmlspecialchars($e->getTraceAsString()) . '</pre>';
    } else {
        // 載入錯誤頁面
        if (file_exists(APP_PATH . '/View/errors/500.php')) {
            include APP_PATH . '/View/errors/500.php';
        } else {
            echo '<h1>伺服器錯誤</h1><p>很抱歉，發生了一個錯誤。請稍後再試。</p>';
        }
    }
    
    // 記錄錯誤
    error_log('Application Error: ' . $e->getMessage() . ' in ' . $e->getFile() . ':' . $e->getLine());
}