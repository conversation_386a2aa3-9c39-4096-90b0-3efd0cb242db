<?php

use App\Router\Router;

/**
 * 路由配置
 * 定義所有應用程式路由
 */

$router = new Router();

// 設定基礎路徑 (如果應用程式在子目錄中)
// $router->setBasePath('/techvg');

// 首頁路由
$router->get('/', 'App\Controller\HomeController@index');
$router->get('/home', 'App\Controller\HomeController@index');

// 檢索相關路由
$router->get('/search', 'App\Controller\SearchController@index');
$router->get('/search/api', 'App\Controller\SearchController@api');
$router->post('/search/api', 'App\Controller\SearchController@api');

// 文檔管理路由
$router->group('/documents', function($router) {
    $router->get('/', 'App\Controller\DocumentController@index');
    $router->get('/upload', 'App\Controller\DocumentController@uploadForm');
    $router->post('/upload', 'App\Controller\DocumentController@upload');
    $router->get('/{id}', 'App\Controller\DocumentController@show');
    $router->get('/{id}/download', 'App\Controller\DocumentController@download');
    $router->delete('/{id}', 'App\Controller\DocumentController@delete');
    $router->get('/{id}/edit', 'App\Controller\DocumentController@editForm');
    $router->post('/{id}/edit', 'App\Controller\DocumentController@update');
});

// 新聞相關路由
$router->group('/news', function($router) {
    $router->get('/', 'App\Controller\NewsController@index');
    $router->get('/{id}', 'App\Controller\NewsController@show');
    $router->get('/category/{category}', 'App\Controller\NewsController@category');
    $router->get('/source/{sourceId}', 'App\Controller\NewsController@source');
});

// API路由群組
$router->group('/api', function($router) {
    // API 版本和健康檢查
    $router->get('/', function() {
        header('Content-Type: application/json');
        echo json_encode([
            'name' => 'Tech.VG API',
            'version' => 'v1',
            'status' => 'active',
            'timestamp' => date('c'),
            'endpoints' => [
                'search' => '/api/search',
                'documents' => '/api/documents',
                'news' => '/api/news',
                'stats' => '/api/stats'
            ],
            'documentation' => [
                'interactive_docs' => '/api/docs?format=html',
                'openapi_spec' => '/api/docs/openapi',
                'code_examples' => '/api/docs/code',
                'changelog' => '/api/changelog?format=html'
            ],
            'tools' => [
                'api_tester' => '/api/test?format=html',
                'system_status' => '/api/status?format=html',
                'health_check' => '/api/health'
            ],
            'features' => [
                'rate_limiting' => true,
                'caching' => true,
                'pagination' => true,
                'search_suggestions' => true,
                'batch_operations' => true,
                'real_time_stats' => true,
                'file_upload' => true,
                'code_generation' => true
            ]
        ], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    });
    
    $router->get('/health', function() {
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'healthy',
            'timestamp' => date('c'),
            'version' => 'v1',
            'uptime' => '24/7'
        ], JSON_UNESCAPED_UNICODE);
    });
    
    // 檢索API
    $router->get('/search', 'App\Controller\Api\SearchController@search');
    $router->post('/search', 'App\Controller\Api\SearchController@search');
    $router->get('/search/suggestions', 'App\Controller\Api\SearchController@suggestions');
    $router->get('/search/popular', 'App\Controller\Api\SearchController@popular');
    $router->get('/search/stats', 'App\Controller\Api\SearchController@stats');
    $router->post('/search/advanced', 'App\Controller\Api\SearchController@advanced');
    $router->get('/search/history', 'App\Controller\Api\SearchController@history');
    $router->delete('/search/history', 'App\Controller\Api\SearchController@clearHistory');
    
    // 文檔API
    $router->get('/documents', 'App\Controller\Api\DocumentController@index');
    $router->post('/documents', 'App\Controller\Api\DocumentController@store');
    $router->get('/documents/stats', 'App\Controller\Api\DocumentController@stats');
    $router->post('/documents/batch', 'App\Controller\Api\DocumentController@batch');
    $router->get('/documents/{id}', 'App\Controller\Api\DocumentController@show');
    $router->put('/documents/{id}', 'App\Controller\Api\DocumentController@update');
    $router->delete('/documents/{id}', 'App\Controller\Api\DocumentController@destroy');
    $router->post('/documents/{id}/reindex', 'App\Controller\Api\DocumentController@reindex');
    
    // 新聞API
    $router->get('/news', 'App\Controller\Api\NewsController@index');
    $router->get('/news/latest', 'App\Controller\Api\NewsController@latest');
    $router->get('/news/popular', 'App\Controller\Api\NewsController@popular');
    $router->get('/news/categories', 'App\Controller\Api\NewsController@categories');
    $router->get('/news/sources', 'App\Controller\Api\NewsController@sources');
    $router->get('/news/stats', 'App\Controller\Api\NewsController@stats');
    $router->get('/news/search', 'App\Controller\Api\NewsController@search');
    $router->get('/news/category/{category}', 'App\Controller\Api\NewsController@category');
    $router->get('/news/source/{sourceId}', 'App\Controller\Api\NewsController@source');
    $router->get('/news/{id}', 'App\Controller\Api\NewsController@show');
    
    // 統計API
    $router->get('/stats/overview', 'App\Controller\Api\StatsController@overview');
    $router->get('/stats/realtime', 'App\Controller\Api\StatsController@realtime');
    $router->get('/stats/export', 'App\Controller\Api\StatsController@export');
    $router->get('/stats/documents', 'App\Controller\Api\StatsController@documents');
    $router->get('/stats/news', 'App\Controller\Api\StatsController@news');
    $router->get('/stats/search', 'App\Controller\Api\StatsController@search');
    
    // API 文檔
    $router->get('/docs', 'App\Controller\Api\DocsController@index');
    $router->get('/docs/openapi', 'App\Controller\Api\DocsController@openapi');
    $router->get('/docs/code', 'App\Controller\Api\DocsController@generateCode');
    
    // API 變更日誌
    $router->get('/changelog', 'App\Controller\Api\ChangelogController@index');
    $router->get('/v1/changelog', 'App\Controller\Api\ChangelogController@v1');
    
    // API 狀態監控
    $router->get('/status', 'App\Controller\Api\StatusController@index');
    $router->get('/health', 'App\Controller\Api\StatusController@health');
    $router->get('/status/detailed', 'App\Controller\Api\StatusController@detailed');
    
    // API 測試工具
    $router->get('/test', 'App\Controller\Api\TestController@index');
    $router->get('/test/ping', 'App\Controller\Api\TestController@ping');
    $router->post('/test/echo', 'App\Controller\Api\TestController@echo');
    $router->post('/test/validate', 'App\Controller\Api\TestController@validate');
    $router->get('/test/performance', 'App\Controller\Api\TestController@performance');
    $router->get('/test/endpoints', 'App\Controller\Api\TestController@endpoints');
    $router->post('/test/load', 'App\Controller\Api\TestController@load');
    
    // API 快取管理
    $router->get('/cache/status', 'App\Controller\Api\CacheController@status');
    $router->delete('/cache', 'App\Controller\Api\CacheController@clear');
    $router->post('/cache/warmup', 'App\Controller\Api\CacheController@warmup');
    $router->get('/cache/items', 'App\Controller\Api\CacheController@items');
    $router->put('/cache/set', 'App\Controller\Api\CacheController@set');
});

// 管理後台路由 (需要認證)
$router->group('/admin', function($router) {
    $router->get('/', 'App\Controller\Admin\DashboardController@index');
    $router->get('/dashboard', 'App\Controller\Admin\DashboardController@index');
    
    // 文檔管理
    $router->get('/documents', 'App\Controller\Admin\DocumentController@index');
    $router->get('/documents/{id}', 'App\Controller\Admin\DocumentController@show');
    $router->delete('/documents/{id}', 'App\Controller\Admin\DocumentController@delete');
    
    // 新聞管理
    $router->get('/news', 'App\Controller\Admin\NewsController@index');
    $router->get('/news/sources', 'App\Controller\Admin\NewsController@sources');
    $router->post('/news/crawl', 'App\Controller\Admin\NewsController@crawl');
    
    // 系統設定
    $router->get('/settings', 'App\Controller\Admin\SettingsController@index');
    $router->post('/settings', 'App\Controller\Admin\SettingsController@update');
    
    // 用戶管理
    $router->get('/users', 'App\Controller\Admin\UserController@index');
    $router->get('/users/{id}', 'App\Controller\Admin\UserController@show');
    
}, ['App\Middleware\AuthMiddleware', 'App\Middleware\AdminMiddleware']);

// 用戶認證路由
$router->group('/auth', function($router) {
    $router->get('/login', 'App\Controller\AuthController@loginForm');
    $router->post('/login', 'App\Controller\AuthController@login');
    $router->get('/register', 'App\Controller\AuthController@registerForm');
    $router->post('/register', 'App\Controller\AuthController@register');
    $router->post('/logout', 'App\Controller\AuthController@logout');
    $router->get('/forgot-password', 'App\Controller\AuthController@forgotPasswordForm');
    $router->post('/forgot-password', 'App\Controller\AuthController@forgotPassword');
});

// 靜態頁面路由
$router->get('/about', 'App\Controller\PageController@about');
$router->get('/contact', 'App\Controller\PageController@contact');
$router->post('/contact', 'App\Controller\PageController@contactSubmit');
$router->get('/privacy', 'App\Controller\PageController@privacy');
$router->get('/terms', 'App\Controller\PageController@terms');

// RSS訂閱
$router->get('/rss', 'App\Controller\RssController@index');
$router->get('/rss/news', 'App\Controller\RssController@news');

// Sitemap
$router->get('/sitemap.xml', 'App\Controller\SitemapController@index');

// 健康檢查
$router->get('/health', function() {
    header('Content-Type: application/json');
    echo json_encode([
        'status' => 'ok',
        'timestamp' => date('Y-m-d H:i:s'),
        'version' => '1.0.0'
    ]);
});

// 錯誤頁面路由 (放在最後)
$router->get('/error/404', 'App\Controller\ErrorController@notFound');
$router->get('/error/403', 'App\Controller\ErrorController@forbidden');
$router->get('/error/500', 'App\Controller\ErrorController@internalServerError');
$router->get('/error/503', 'App\Controller\ErrorController@serviceUnavailable');

// 錯誤日誌管理路由（僅開發模式）
$router->get('/error/logs', 'App\Controller\ErrorController@logs');
$router->post('/error/logs/clear', 'App\Controller\ErrorController@clearLogs');

// 向後兼容的404路由
$router->get('/404', 'App\Controller\ErrorController@notFound');

return $router;