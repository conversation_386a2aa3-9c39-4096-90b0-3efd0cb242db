<?php

/**
 * 維護模式管理腳本
 * 用於啟用和停用網站維護模式
 */

// 確保只能從命令行執行
if (php_sapi_name() !== 'cli') {
    die('此腳本只能從命令行執行');
}

// 設定應用程式根目錄
define('APP_ROOT', dirname(__DIR__));

// 維護模式文件路徑
$maintenanceFile = APP_ROOT . '/storage/maintenance.json';
$storageDir = dirname($maintenanceFile);

// 確保storage目錄存在
if (!is_dir($storageDir)) {
    mkdir($storageDir, 0755, true);
}

/**
 * 顯示幫助信息
 */
function showHelp() {
    echo "維護模式管理工具\n";
    echo "用法: php maintenance.php [命令] [選項]\n\n";
    echo "命令:\n";
    echo "  up                    啟用維護模式\n";
    echo "  down                  停用維護模式\n";
    echo "  status                查看維護模式狀態\n";
    echo "  help                  顯示此幫助信息\n\n";
    echo "選項 (僅用於 up 命令):\n";
    echo "  --message=MESSAGE     自定義維護訊息\n";
    echo "  --until=DATETIME      維護結束時間 (格式: Y-m-d H:i:s)\n";
    echo "  --allow-ip=IP         允許訪問的IP地址 (可多次使用)\n\n";
    echo "範例:\n";
    echo "  php maintenance.php up --message=\"系統升級中\" --until=\"2024-01-01 12:00:00\"\n";
    echo "  php maintenance.php up --allow-ip=127.0.0.1 --allow-ip=***********/24\n";
    echo "  php maintenance.php down\n";
}

/**
 * 啟用維護模式
 */
function enableMaintenance($options = []) {
    global $maintenanceFile;
    
    $config = [
        'enabled' => true,
        'message' => $options['message'] ?? '網站正在進行維護升級，預計很快就會恢復正常。感謝您的耐心等待。',
        'created_at' => date('Y-m-d H:i:s'),
        'allowed_ips' => $options['allowed_ips'] ?? []
    ];
    
    if (isset($options['until'])) {
        $config['until'] = $options['until'];
    }
    
    if (file_put_contents($maintenanceFile, json_encode($config, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE))) {
        echo "✓ 維護模式已啟用\n";
        if (isset($config['until'])) {
            echo "  預計結束時間: {$config['until']}\n";
        }
        if (!empty($config['allowed_ips'])) {
            echo "  允許的IP: " . implode(', ', $config['allowed_ips']) . "\n";
        }
    } else {
        echo "✗ 啟用維護模式失敗\n";
        exit(1);
    }
}

/**
 * 停用維護模式
 */
function disableMaintenance() {
    global $maintenanceFile;
    
    if (file_exists($maintenanceFile)) {
        if (unlink($maintenanceFile)) {
            echo "✓ 維護模式已停用\n";
        } else {
            echo "✗ 停用維護模式失敗\n";
            exit(1);
        }
    } else {
        echo "ℹ 維護模式未啟用\n";
    }
}

/**
 * 查看維護模式狀態
 */
function showStatus() {
    global $maintenanceFile;
    
    if (file_exists($maintenanceFile)) {
        $config = json_decode(file_get_contents($maintenanceFile), true);
        
        echo "維護模式狀態: 已啟用\n";
        echo "啟用時間: {$config['created_at']}\n";
        echo "維護訊息: {$config['message']}\n";
        
        if (isset($config['until'])) {
            echo "預計結束時間: {$config['until']}\n";
            
            $until = strtotime($config['until']);
            $now = time();
            
            if ($until > $now) {
                $remaining = $until - $now;
                $hours = floor($remaining / 3600);
                $minutes = floor(($remaining % 3600) / 60);
                echo "剩餘時間: {$hours}小時{$minutes}分鐘\n";
            } else {
                echo "⚠ 維護時間已過期\n";
            }
        }
        
        if (!empty($config['allowed_ips'])) {
            echo "允許的IP: " . implode(', ', $config['allowed_ips']) . "\n";
        }
    } else {
        echo "維護模式狀態: 未啟用\n";
    }
}

/**
 * 解析命令行參數
 */
function parseArguments($argv) {
    $command = $argv[1] ?? 'help';
    $options = [];
    
    for ($i = 2; $i < count($argv); $i++) {
        $arg = $argv[$i];
        
        if (strpos($arg, '--') === 0) {
            if (strpos($arg, '=') !== false) {
                [$key, $value] = explode('=', substr($arg, 2), 2);
                
                if ($key === 'allow-ip') {
                    $options['allowed_ips'][] = $value;
                } else {
                    $options[str_replace('-', '_', $key)] = $value;
                }
            } else {
                $options[str_replace('-', '_', substr($arg, 2))] = true;
            }
        }
    }
    
    return [$command, $options];
}

// 主程式
[$command, $options] = parseArguments($argv);

switch ($command) {
    case 'up':
        enableMaintenance($options);
        break;
        
    case 'down':
        disableMaintenance();
        break;
        
    case 'status':
        showStatus();
        break;
        
    case 'help':
    default:
        showHelp();
        break;
}