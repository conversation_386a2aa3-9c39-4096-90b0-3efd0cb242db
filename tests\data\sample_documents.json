[{"title": "Machine Learning Fundamentals", "content": "Machine learning is a subset of artificial intelligence that focuses on algorithms that can learn from and make predictions on data. This document covers the basic concepts including supervised learning, unsupervised learning, and reinforcement learning.", "file_type": "txt", "tags": "machine learning, AI, algorithms", "category": "education"}, {"title": "Deep Learning with Neural Networks", "content": "# Deep Learning Guide\n\nDeep learning is a machine learning technique that teaches computers to learn by example. This guide covers:\n\n## Neural Networks\n- Perceptrons\n- Multi-layer networks\n- Backpropagation\n\n## Applications\n- Image recognition\n- Natural language processing\n- Speech recognition", "file_type": "md", "tags": "deep learning, neural networks, AI", "category": "technical"}, {"title": "Blockchain Technology Overview", "content": "Blockchain is a distributed ledger technology that maintains a continuously growing list of records, called blocks, which are linked and secured using cryptography. Each block contains a cryptographic hash of the previous block, a timestamp, and transaction data.", "file_type": "txt", "tags": "blockchain, cryptocurrency, distributed systems", "category": "technology"}, {"title": "Web Development Best Practices", "content": "# Web Development Best Practices\n\n## Frontend Development\n- Use semantic HTML\n- Implement responsive design\n- Optimize for performance\n\n## Backend Development\n- Follow RESTful API design\n- Implement proper error handling\n- Use secure authentication\n\n## Testing\n- Write unit tests\n- Implement integration tests\n- Perform security testing", "file_type": "md", "tags": "web development, best practices, programming", "category": "development"}, {"title": "Cybersecurity Fundamentals", "content": "Cybersecurity involves protecting systems, networks, and programs from digital attacks. These cyberattacks are usually aimed at accessing, changing, or destroying sensitive information; extorting money from users; or interrupting normal business processes.", "file_type": "txt", "tags": "cybersecurity, security, protection", "category": "security"}]