<?php

namespace App\Controller\Api;

use App\Service\DocumentService;
use App\Service\IndexService;

/**
 * 文檔 API 控制器
 * 處理文檔相關的 API 請求
 */
class DocumentController extends BaseApiController
{
    private DocumentService $documentService;
    private IndexService $indexService;

    public function __construct()
    {
        parent::__construct();
        $this->documentService = new DocumentService();
        $this->indexService = new IndexService();
    }

    /**
     * 取得文檔列表
     * 
     * GET /api/documents
     * 
     * 參數：
     * - page: 頁碼
     * - per_page: 每頁項目數
     * - sort_by: 排序欄位 (id, title, created_at, updated_at, file_size)
     * - sort_direction: 排序方向 (asc, desc)
     * - status: 狀態篩選 (active, inactive, processing)
     * - file_type: 檔案類型篩選 (txt, md, markdown)
     * - search: 搜尋關鍵字
     */
    public function index(): void
    {
        try {
            // 檢查速率限制
            $clientIp = $this->getClientIp();
            if (!$this->checkRateLimit($clientIp, 300, 3600)) {
                $this->errorResponse('請求過於頻繁，請稍後再試', 429, [], 'RATE_LIMIT_EXCEEDED');
            }

            // 取得分頁參數
            $pagination = $this->getPaginationParams();
            
            // 取得排序參數
            $sort = $this->getSortParams(['id', 'title', 'created_at', 'updated_at', 'file_size'], 'created_at', 'desc');
            
            // 取得篩選參數
            $filters = $this->getFilterParams(['status', 'file_type', 'search']);

            // 取得文檔列表
            $result = $this->documentService->getDocuments($filters, $pagination['page'], $pagination['per_page'], $sort);

            if (!$result['success']) {
                $this->errorResponse($result['message'] ?? '取得文檔列表失敗', 500, [], 'DOCUMENT_LIST_ERROR');
            }

            // 處理響應資料
            $documents = array_map(function($doc) {
                return $this->formatDocumentResponse($doc, false);
            }, $result['data'] ?? []);

            $this->paginatedResponse(
                $documents,
                $result['meta']['total'] ?? 0,
                $pagination['page'],
                $pagination['per_page'],
                '文檔列表取得成功'
            );

        } catch (\Exception $e) {
            $this->errorResponse('文檔服務暫時不可用', 500, [], 'DOCUMENT_SERVICE_ERROR');
        }
    }

    /**
     * 取得單一文檔詳情
     * 
     * GET /api/documents/{id}
     * 
     * 參數：
     * - include_content: 是否包含文檔內容 (true/false)
     */
    public function show(int $id): void
    {
        try {
            // 檢查速率限制
            $clientIp = $this->getClientIp();
            if (!$this->checkRateLimit($clientIp, 500, 3600)) {
                $this->errorResponse('請求過於頻繁，請稍後再試', 429, [], 'RATE_LIMIT_EXCEEDED');
            }

            $includeContent = $this->getGet('include_content', 'false') === 'true';

            $result = $this->documentService->getDocument($id, $includeContent);

            if (!$result['success']) {
                if (strpos($result['message'], '不存在') !== false) {
                    $this->errorResponse('文檔不存在', 404, [], 'DOCUMENT_NOT_FOUND');
                }
                $this->errorResponse($result['message'] ?? '取得文檔失敗', 500, [], 'DOCUMENT_GET_ERROR');
            }

            $document = $this->formatDocumentResponse($result['data'], $includeContent);

            $this->successResponse($document, '文檔詳情取得成功');

        } catch (\Exception $e) {
            $this->errorResponse('文檔服務暫時不可用', 500, [], 'DOCUMENT_SERVICE_ERROR');
        }
    }

    /**
     * 建立新文檔
     * 
     * POST /api/documents
     * 
     * 請求體 (multipart/form-data):
     * - document: 文檔檔案 (必填)
     * - title: 文檔標題 (選填)
     * - description: 文檔描述 (選填)
     * - tags: 標籤，逗號分隔 (選填)
     */
    public function store(): void
    {
        try {
            // 只允許 POST 請求
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                $this->errorResponse('只支援 POST 請求', 405, [], 'METHOD_NOT_ALLOWED');
            }

            // 檢查速率限制
            $clientIp = $this->getClientIp();
            if (!$this->checkRateLimit($clientIp, 50, 3600)) {
                $this->errorResponse('請求過於頻繁，請稍後再試', 429, [], 'RATE_LIMIT_EXCEEDED');
            }

            // 檢查檔案上傳
            if (!isset($_FILES['document']) || $_FILES['document']['error'] === UPLOAD_ERR_NO_FILE) {
                $this->errorResponse('請選擇要上傳的檔案', 400, [], 'NO_FILE_UPLOADED');
            }

            $file = $_FILES['document'];
            $title = trim($this->getPost('title', ''));
            $description = trim($this->getPost('description', ''));
            $tags = trim($this->getPost('tags', ''));

            // 如果沒有提供標題，使用檔案名稱
            if (empty($title)) {
                $title = pathinfo($file['name'], PATHINFO_FILENAME);
            }

            // 驗證檔案
            $validation = $this->validateFileUpload($file, ['txt', 'md', 'markdown'], 10 * 1024 * 1024);
            if (!$validation['valid']) {
                $this->errorResponse($validation['error'], 400, [], 'FILE_VALIDATION_ERROR');
            }

            // 驗證其他參數
            $rules = [
                'title' => 'required|max:255',
                'description' => 'max:1000',
                'tags' => 'max:500'
            ];

            $data = [
                'title' => $title,
                'description' => $description,
                'tags' => $tags
            ];

            $errors = $this->validateRequest($rules, $data);
            if (!empty($errors)) {
                $this->errorResponse('請求參數驗證失敗', 400, $errors, 'VALIDATION_ERROR');
            }

            // 上傳文檔
            $result = $this->documentService->uploadDocument($file, [
                'title' => $title,
                'description' => $description,
                'tags' => $tags
            ]);

            if (!$result['success']) {
                $this->errorResponse($result['message'] ?? '文檔上傳失敗', 500, [], 'DOCUMENT_UPLOAD_ERROR');
            }

            // 取得完整的文檔資訊
            $documentResult = $this->documentService->getDocument($result['data']['document_id'], false);
            $document = $documentResult['success'] ? $this->formatDocumentResponse($documentResult['data'], false) : null;

            $this->successResponse($document, '文檔上傳成功', 201);

        } catch (\Exception $e) {
            $this->errorResponse('文檔上傳服務暫時不可用', 500, [], 'DOCUMENT_UPLOAD_SERVICE_ERROR');
        }
    }

    /**
     * 更新文檔
     * 
     * PUT /api/documents/{id}
     * 
     * 請求體 (JSON):
     * - title: 文檔標題
     * - description: 文檔描述
     * - tags: 標籤，逗號分隔
     * - status: 狀態 (active, inactive)
     */
    public function update(int $id): void
    {
        try {
            // 只允許 PUT 請求
            if ($_SERVER['REQUEST_METHOD'] !== 'PUT') {
                $this->errorResponse('只支援 PUT 請求', 405, [], 'METHOD_NOT_ALLOWED');
            }

            // 檢查速率限制
            $clientIp = $this->getClientIp();
            if (!$this->checkRateLimit($clientIp, 100, 3600)) {
                $this->errorResponse('請求過於頻繁，請稍後再試', 429, [], 'RATE_LIMIT_EXCEEDED');
            }

            $data = $this->getRequestData();

            // 驗證請求參數
            $rules = [
                'title' => 'max:255',
                'description' => 'max:1000',
                'tags' => 'max:500',
                'status' => 'in:active,inactive'
            ];

            $errors = $this->validateRequest($rules, $data);
            if (!empty($errors)) {
                $this->errorResponse('請求參數驗證失敗', 400, $errors, 'VALIDATION_ERROR');
            }

            // 檢查文檔是否存在
            $existingResult = $this->documentService->getDocument($id, false);
            if (!$existingResult['success']) {
                $this->errorResponse('文檔不存在', 404, [], 'DOCUMENT_NOT_FOUND');
            }

            // 更新文檔
            $result = $this->documentService->updateDocument($id, $data);

            if (!$result['success']) {
                $this->errorResponse($result['message'] ?? '文檔更新失敗', 500, [], 'DOCUMENT_UPDATE_ERROR');
            }

            // 取得更新後的文檔資訊
            $documentResult = $this->documentService->getDocument($id, false);
            $document = $documentResult['success'] ? $this->formatDocumentResponse($documentResult['data'], false) : null;

            $this->successResponse($document, '文檔更新成功');

        } catch (\Exception $e) {
            $this->errorResponse('文檔更新服務暫時不可用', 500, [], 'DOCUMENT_UPDATE_SERVICE_ERROR');
        }
    }

    /**
     * 刪除文檔
     * 
     * DELETE /api/documents/{id}
     */
    public function destroy(int $id): void
    {
        try {
            // 只允許 DELETE 請求
            if ($_SERVER['REQUEST_METHOD'] !== 'DELETE') {
                $this->errorResponse('只支援 DELETE 請求', 405, [], 'METHOD_NOT_ALLOWED');
            }

            // 檢查速率限制
            $clientIp = $this->getClientIp();
            if (!$this->checkRateLimit($clientIp, 50, 3600)) {
                $this->errorResponse('請求過於頻繁，請稍後再試', 429, [], 'RATE_LIMIT_EXCEEDED');
            }

            // 檢查文檔是否存在
            $existingResult = $this->documentService->getDocument($id, false);
            if (!$existingResult['success']) {
                $this->errorResponse('文檔不存在', 404, [], 'DOCUMENT_NOT_FOUND');
            }

            // 刪除文檔
            $result = $this->documentService->deleteDocument($id);

            if (!$result['success']) {
                $this->errorResponse($result['message'] ?? '文檔刪除失敗', 500, [], 'DOCUMENT_DELETE_ERROR');
            }

            $this->successResponse(null, '文檔刪除成功');

        } catch (\Exception $e) {
            $this->errorResponse('文檔刪除服務暫時不可用', 500, [], 'DOCUMENT_DELETE_SERVICE_ERROR');
        }
    }

    /**
     * 重建文檔索引
     * 
     * POST /api/documents/{id}/reindex
     */
    public function reindex(int $id): void
    {
        try {
            // 只允許 POST 請求
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                $this->errorResponse('只支援 POST 請求', 405, [], 'METHOD_NOT_ALLOWED');
            }

            // 檢查速率限制
            $clientIp = $this->getClientIp();
            if (!$this->checkRateLimit($clientIp, 20, 3600)) {
                $this->errorResponse('請求過於頻繁，請稍後再試', 429, [], 'RATE_LIMIT_EXCEEDED');
            }

            // 檢查文檔是否存在
            $existingResult = $this->documentService->getDocument($id, false);
            if (!$existingResult['success']) {
                $this->errorResponse('文檔不存在', 404, [], 'DOCUMENT_NOT_FOUND');
            }

            // 重建索引
            $result = $this->documentService->reindexDocument($id);

            if (!$result['success']) {
                $this->errorResponse($result['message'] ?? '重建索引失敗', 500, [], 'REINDEX_ERROR');
            }

            $this->successResponse(null, '索引重建成功');

        } catch (\Exception $e) {
            $this->errorResponse('重建索引服務暫時不可用', 500, [], 'REINDEX_SERVICE_ERROR');
        }
    }

    /**
     * 取得文檔統計
     * 
     * GET /api/documents/stats
     */
    public function stats(): void
    {
        try {
            $result = $this->documentService->getStatistics();

            if (!$result['success']) {
                $this->errorResponse($result['message'] ?? '取得文檔統計失敗', 500, [], 'DOCUMENT_STATS_ERROR');
            }

            $this->successResponse($result['data'], '文檔統計取得成功');

        } catch (\Exception $e) {
            $this->errorResponse('文檔統計服務暫時不可用', 500, [], 'DOCUMENT_STATS_SERVICE_ERROR');
        }
    }

    /**
     * 批量操作文檔
     * 
     * POST /api/documents/batch
     * 
     * 請求體 (JSON):
     * - action: 操作類型 (delete, reindex, update_status)
     * - document_ids: 文檔 ID 陣列
     * - data: 操作相關資料 (對於 update_status 操作)
     */
    public function batch(): void
    {
        try {
            // 只允許 POST 請求
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                $this->errorResponse('只支援 POST 請求', 405, [], 'METHOD_NOT_ALLOWED');
            }

            // 檢查速率限制
            $clientIp = $this->getClientIp();
            if (!$this->checkRateLimit($clientIp, 10, 3600)) {
                $this->errorResponse('請求過於頻繁，請稍後再試', 429, [], 'RATE_LIMIT_EXCEEDED');
            }

            $data = $this->getRequestData();

            // 驗證請求參數
            $rules = [
                'action' => 'required|in:delete,reindex,update_status',
                'document_ids' => 'required'
            ];

            $errors = $this->validateRequest($rules, $data);
            if (!empty($errors)) {
                $this->errorResponse('請求參數驗證失敗', 400, $errors, 'VALIDATION_ERROR');
            }

            $action = $data['action'];
            $documentIds = $data['document_ids'];

            if (!is_array($documentIds) || empty($documentIds)) {
                $this->errorResponse('document_ids 必須是非空陣列', 400, [], 'INVALID_DOCUMENT_IDS');
            }

            if (count($documentIds) > 100) {
                $this->errorResponse('一次最多只能操作 100 個文檔', 400, [], 'TOO_MANY_DOCUMENTS');
            }

            $results = [];
            $successCount = 0;
            $errorCount = 0;

            foreach ($documentIds as $documentId) {
                try {
                    switch ($action) {
                        case 'delete':
                            $result = $this->documentService->deleteDocument($documentId);
                            break;

                        case 'reindex':
                            $result = $this->documentService->reindexDocument($documentId);
                            break;

                        case 'update_status':
                            if (empty($data['data']['status'])) {
                                $results[$documentId] = ['success' => false, 'message' => '缺少狀態參數'];
                                $errorCount++;
                                continue 2;
                            }
                            $result = $this->documentService->updateDocument($documentId, ['status' => $data['data']['status']]);
                            break;

                        default:
                            $results[$documentId] = ['success' => false, 'message' => '不支援的操作'];
                            $errorCount++;
                            continue 2;
                    }

                    $results[$documentId] = $result;
                    if ($result['success']) {
                        $successCount++;
                    } else {
                        $errorCount++;
                    }

                } catch (\Exception $e) {
                    $results[$documentId] = ['success' => false, 'message' => '操作失敗'];
                    $errorCount++;
                }
            }

            $this->successResponse([
                'action' => $action,
                'total_processed' => count($documentIds),
                'success_count' => $successCount,
                'error_count' => $errorCount,
                'results' => $results
            ], "批量操作完成：成功 {$successCount} 個，失敗 {$errorCount} 個");

        } catch (\Exception $e) {
            $this->errorResponse('批量操作服務暫時不可用', 500, [], 'BATCH_OPERATION_SERVICE_ERROR');
        }
    }

    /**
     * 格式化文檔響應資料
     * 
     * @param array $document 文檔資料
     * @param bool $includeContent 是否包含內容
     * @return array
     */
    private function formatDocumentResponse(array $document, bool $includeContent = false): array
    {
        $response = [
            'id' => $document['id'] ?? null,
            'title' => $document['title'] ?? null,
            'filename' => $document['filename'] ?? null,
            'file_type' => $document['file_type'] ?? null,
            'file_size' => $document['file_size'] ?? 0,
            'file_size_formatted' => $this->formatBytes($document['file_size'] ?? 0),
            'status' => $document['status'] ?? null,
            'description' => $document['description'] ?? null,
            'tags' => $document['tags'] ?? null,
            'word_count' => $document['word_count'] ?? 0,
            'views' => $document['views'] ?? 0,
            'indexed' => $document['indexed'] ?? false,
            'hash' => $document['hash'] ?? null,
            'created_at' => $document['created_at'] ?? null,
            'updated_at' => $document['updated_at'] ?? null
        ];

        if ($includeContent) {
            $response['content'] = $document['content'] ?? null;
        }

        // 添加 API 連結
        $response['links'] = [
            'self' => "/api/documents/{$document['id']}",
            'download' => "/documents/{$document['id']}/download",
            'edit' => "/documents/{$document['id']}/edit",
            'reindex' => "/api/documents/{$document['id']}/reindex"
        ];

        return $response;
    }
}