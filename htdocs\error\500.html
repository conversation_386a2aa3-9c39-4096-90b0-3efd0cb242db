<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>500 - 伺服器錯誤 | Tech.VG 科技新知檢索</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;700&display=swap');
        body {
            font-family: 'Noto Sans TC', sans-serif;
        }
        .gradient-bg {
            background: linear-gradient(90deg, #3b82f6, #8b5cf6);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center">
    <div class="max-w-md w-full mx-4">
        <div class="bg-white rounded-lg shadow-lg p-8 text-center">
            <!-- Logo -->
            <div class="flex justify-center mb-6">
                <div class="w-16 h-16 gradient-bg rounded-lg flex items-center justify-center">
                    <i class="fas fa-search text-white text-2xl"></i>
                </div>
            </div>
            
            <!-- 錯誤圖示 -->
            <div class="mb-6">
                <i class="fas fa-exclamation-circle text-red-500 text-6xl"></i>
            </div>
            
            <!-- 錯誤代碼 -->
            <h1 class="text-6xl font-bold text-red-600 mb-4">500</h1>
            
            <!-- 錯誤標題 -->
            <h2 class="text-2xl font-semibold text-gray-900 mb-4">伺服器內部錯誤</h2>
            
            <!-- 錯誤描述 -->
            <p class="text-gray-600 mb-8 leading-relaxed">
                抱歉，伺服器發生內部錯誤。<br>
                我們的技術團隊已收到通知，正在努力修復此問題。
            </p>
            
            <!-- 操作按鈕 -->
            <div class="space-y-3">
                <a href="/" class="w-full inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white gradient-bg hover:opacity-90 transition-opacity">
                    <i class="fas fa-home mr-2"></i>
                    返回首頁
                </a>
                <button onclick="location.reload()" class="w-full inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                    <i class="fas fa-redo mr-2"></i>
                    重新載入
                </button>
            </div>
            
            <!-- 額外資訊 -->
            <div class="mt-8 pt-6 border-t border-gray-200">
                <p class="text-sm text-gray-500 mb-4">如果問題持續發生，請：</p>
                <div class="flex flex-wrap justify-center gap-4 text-sm">
                    <a href="/contact" class="text-blue-600 hover:text-blue-800">回報問題</a>
                    <a href="/status" class="text-blue-600 hover:text-blue-800">系統狀態</a>
                    <a href="/help" class="text-blue-600 hover:text-blue-800">取得協助</a>
                </div>
            </div>
        </div>
        
        <!-- 網站資訊 -->
        <div class="text-center mt-6">
            <p class="text-gray-500 text-sm">
                © <?= date('Y') ?> Tech.VG 科技新知檢索平台
            </p>
        </div>
    </div>
</body>
</html>
