<?php

namespace Tests\Unit\Controller\Api;

use Tests\TestCase;
use App\Controller\Api\SearchController;
use App\Service\SearchService;

/**
 * 搜尋控制器單元測試
 */
class SearchControllerTest extends TestCase
{
    private SearchController $controller;
    private $mockSearchService;

    protected function setUp(): void
    {
        parent::setUp();
        
        // 建立模擬的 SearchService
        $this->mockSearchService = $this->createMock(SearchService::class);
        
        // 建立控制器實例
        $this->controller = new SearchController();
        
        // 注入模擬服務（這裡需要修改控制器以支援依賴注入）
        $reflection = new \ReflectionClass($this->controller);
        $property = $reflection->getProperty('searchService');
        $property->setAccessible(true);
        $property->setValue($this->controller, $this->mockSearchService);
    }

    /**
     * 測試基本搜尋功能
     */
    public function testBasicSearch(): void
    {
        // 準備測試資料
        $query = 'test query';
        $expectedResults = [
            'success' => true,
            'data' => [
                [
                    'id' => 1,
                    'title' => 'Test Document',
                    'type' => 'document',
                    'relevance' => 0.95
                ]
            ],
            'meta' => [
                'total_results' => 1,
                'total_pages' => 1
            ]
        ];

        // 設定模擬服務的期望行為
        $this->mockSearchService
            ->expects($this->once())
            ->method('search')
            ->with($query, $this->isType('array'))
            ->willReturn($expectedResults);

        // 模擬 HTTP 請求參數
        $_GET = ['q' => $query];
        $_SERVER['REQUEST_METHOD'] = 'GET';
        $_SERVER['REQUEST_URI'] = '/api/search';

        // 捕獲輸出
        ob_start();
        $this->controller->search();
        $output = ob_get_clean();

        // 驗證響應
        $response = json_decode($output, true);
        $this->assertNotNull($response, 'Response should be valid JSON');
        $this->assertTrue($response['success'], 'Search should be successful');
        $this->assertArrayHasKey('data', $response);
        $this->assertArrayHasKey('query', $response['data']);
        $this->assertEquals($query, $response['data']['query']);
    }

    /**
     * 測試搜尋參數驗證
     */
    public function testSearchValidation(): void
    {
        // 測試空查詢
        $_GET = ['q' => ''];
        $_SERVER['REQUEST_METHOD'] = 'GET';

        ob_start();
        $this->controller->search();
        $output = ob_get_clean();

        $response = json_decode($output, true);
        $this->assertFalse($response['success'], 'Empty query should fail validation');
        $this->assertArrayHasKey('error_code', $response);
        $this->assertEquals('VALIDATION_ERROR', $response['error_code']);
    }

    /**
     * 測試搜尋建議功能
     */
    public function testSearchSuggestions(): void
    {
        $query = 'tech';
        $expectedSuggestions = [
            'technology',
            'technical',
            'technique'
        ];

        $this->mockSearchService
            ->expects($this->once())
            ->method('getSuggestions')
            ->with($query, 5)
            ->willReturn($expectedSuggestions);

        $_GET = ['q' => $query, 'limit' => '5'];
        $_SERVER['REQUEST_METHOD'] = 'GET';

        ob_start();
        $this->controller->suggestions();
        $output = ob_get_clean();

        $response = json_decode($output, true);
        $this->assertTrue($response['success']);
        $this->assertArrayHasKey('suggestions', $response['data']);
        $this->assertEquals($expectedSuggestions, $response['data']['suggestions']);
    }

    /**
     * 測試熱門搜尋功能
     */
    public function testPopularSearches(): void
    {
        $expectedPopular = [
            'success' => true,
            'data' => [
                ['query' => 'artificial intelligence', 'count' => 150],
                ['query' => 'machine learning', 'count' => 120],
                ['query' => 'blockchain', 'count' => 100]
            ]
        ];

        $this->mockSearchService
            ->expects($this->once())
            ->method('getPopularSearches')
            ->with(7, 10)
            ->willReturn($expectedPopular);

        $_GET = ['days' => '7', 'limit' => '10'];
        $_SERVER['REQUEST_METHOD'] = 'GET';

        ob_start();
        $this->controller->popular();
        $output = ob_get_clean();

        $response = json_decode($output, true);
        $this->assertTrue($response['success']);
        $this->assertArrayHasKey('popular_searches', $response['data']);
    }

    /**
     * 測試進階搜尋功能
     */
    public function testAdvancedSearch(): void
    {
        $searchData = [
            'query' => 'machine learning',
            'title' => 'deep learning',
            'type' => 'document',
            'date_from' => '2024-01-01',
            'date_to' => '2024-12-31'
        ];

        $expectedResults = [
            'success' => true,
            'results' => [
                [
                    'id' => 1,
                    'title' => 'Deep Learning Guide',
                    'type' => 'document'
                ]
            ],
            'total' => 1,
            'totalPages' => 1
        ];

        $this->mockSearchService
            ->expects($this->once())
            ->method('advancedSearch')
            ->with($searchData, 1, 20)
            ->willReturn($expectedResults);

        // 模擬 POST 請求
        $_SERVER['REQUEST_METHOD'] = 'POST';
        $_SERVER['CONTENT_TYPE'] = 'application/json';
        
        // 模擬請求體
        $this->mockRequestBody(json_encode($searchData));

        ob_start();
        $this->controller->advanced();
        $output = ob_get_clean();

        $response = json_decode($output, true);
        $this->assertTrue($response['success']);
        $this->assertArrayHasKey('results', $response['data']);
    }

    /**
     * 測試搜尋統計功能
     */
    public function testSearchStats(): void
    {
        $expectedStats = [
            'success' => true,
            'data' => [
                'total_searches' => 1000,
                'unique_queries' => 500,
                'avg_results_per_search' => 15.5,
                'top_categories' => ['technology', 'science']
            ]
        ];

        $this->mockSearchService
            ->expects($this->once())
            ->method('getSearchStatistics')
            ->with(30)
            ->willReturn($expectedStats);

        $_GET = ['days' => '30'];
        $_SERVER['REQUEST_METHOD'] = 'GET';

        ob_start();
        $this->controller->stats();
        $output = ob_get_clean();

        $response = json_decode($output, true);
        $this->assertTrue($response['success']);
        $this->assertArrayHasKey('statistics', $response['data']);
    }

    /**
     * 測試速率限制
     */
    public function testRateLimit(): void
    {
        // 模擬超出速率限制的情況
        $_SERVER['REMOTE_ADDR'] = '192.168.1.100';
        $_GET = ['q' => 'test'];
        $_SERVER['REQUEST_METHOD'] = 'GET';

        // 建立大量請求來觸發速率限制
        for ($i = 0; $i < 250; $i++) {
            ob_start();
            $this->controller->search();
            $output = ob_get_clean();
            
            $response = json_decode($output, true);
            
            // 檢查是否觸發速率限制
            if (!$response['success'] && isset($response['error_code']) && $response['error_code'] === 'RATE_LIMIT_EXCEEDED') {
                $this->assertEquals(429, http_response_code());
                return;
            }
        }

        $this->markTestIncomplete('Rate limit not triggered as expected');
    }

    /**
     * 測試錯誤處理
     */
    public function testErrorHandling(): void
    {
        // 模擬服務拋出異常
        $this->mockSearchService
            ->expects($this->once())
            ->method('search')
            ->willThrowException(new \Exception('Database connection failed'));

        $_GET = ['q' => 'test query'];
        $_SERVER['REQUEST_METHOD'] = 'GET';

        ob_start();
        $this->controller->search();
        $output = ob_get_clean();

        $response = json_decode($output, true);
        $this->assertFalse($response['success']);
        $this->assertArrayHasKey('error_code', $response);
        $this->assertEquals('SEARCH_SERVICE_ERROR', $response['error_code']);
    }

    /**
     * 測試分頁參數處理
     */
    public function testPaginationParameters(): void
    {
        $expectedResults = [
            'success' => true,
            'data' => [],
            'meta' => ['total_results' => 0]
        ];

        $this->mockSearchService
            ->expects($this->once())
            ->method('search')
            ->with('test', $this->callback(function($options) {
                return $options['page'] === 2 && 
                       $options['per_page'] === 50 &&
                       $options['sort_by'] === 'date' &&
                       $options['sort_direction'] === 'asc';
            }))
            ->willReturn($expectedResults);

        $_GET = [
            'q' => 'test',
            'page' => '2',
            'per_page' => '50',
            'sort_by' => 'date',
            'sort_direction' => 'asc'
        ];
        $_SERVER['REQUEST_METHOD'] = 'GET';

        ob_start();
        $this->controller->search();
        $output = ob_get_clean();

        $response = json_decode($output, true);
        $this->assertTrue($response['success']);
    }

    /**
     * 模擬請求體
     * 
     * @param string $body
     */
    private function mockRequestBody(string $body): void
    {
        // 建立臨時檔案來模擬 php://input
        $temp = tmpfile();
        fwrite($temp, $body);
        rewind($temp);
        
        // 這裡需要更複雜的模擬邏輯來替換 php://input
        // 暫時使用全域變數來模擬
        $GLOBALS['mock_request_body'] = $body;
    }

    protected function tearDown(): void
    {
        // 清理全域變數
        unset($_GET, $_POST, $_SERVER['REQUEST_METHOD'], $_SERVER['CONTENT_TYPE']);
        unset($GLOBALS['mock_request_body']);
        
        parent::tearDown();
    }
}