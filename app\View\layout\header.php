<?php
/**
 * 網站頭部模板
 * 包含導航欄和通用頭部元素
 */

$pageTitle = $pageTitle ?? 'Tech.VG - 科技新知檢索平台';
$currentPath = $_SERVER['REQUEST_URI'] ?? '/';
?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($pageTitle) ?></title>
    <meta name="description" content="Tech.VG 是專業的科技新知檢索平台，提供最新科技資訊、文檔管理和智能檢索功能。">
    <meta name="keywords" content="科技新聞,技術文檔,檢索,AI,區塊鏈,行動科技">
    <meta name="author" content="Tech.VG">
    
    <!-- Open Graph -->
    <meta property="og:title" content="<?= htmlspecialchars($pageTitle) ?>">
    <meta property="og:description" content="專業的科技新知檢索平台">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?= htmlspecialchars($_SERVER['REQUEST_URI'] ?? '') ?>">
    
    <!-- CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;700&display=swap');
        
        body {
            font-family: 'Noto Sans TC', sans-serif;
            background-color: #f8f9fa;
        }
        
        .nav-link {
            position: relative;
            transition: all 0.3s ease;
        }
        
        .nav-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -2px;
            left: 0;
            background-color: #3b82f6;
            transition: width 0.3s ease;
        }
        
        .nav-link:hover::after,
        .nav-link.active::after {
            width: 100%;
        }
        
        .gradient-bg {
            background: linear-gradient(90deg, #3b82f6, #8b5cf6);
        }
        
        .card-hover {
            transition: all 0.3s ease;
        }
        
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        
        .search-input {
            transition: all 0.3s ease;
        }
        
        .search-input:focus {
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .btn-primary {
            background: linear-gradient(90deg, #3b82f6, #8b5cf6);
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        }
        
        .loading {
            display: none;
        }
        
        .loading.show {
            display: inline-block;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 導航欄 -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <!-- Logo 和主導航 -->
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <a href="/" class="flex items-center">
                            <div class="w-8 h-8 gradient-bg rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-search text-white text-sm"></i>
                            </div>
                            <span class="text-xl font-bold text-gray-900">Tech.VG</span>
                        </a>
                    </div>
                    
                    <!-- 桌面導航 -->
                    <div class="hidden md:ml-10 md:flex md:space-x-8">
                        <a href="/" class="nav-link <?= $currentPath === '/' ? 'active' : '' ?> text-gray-900 hover:text-blue-600 px-3 py-2 text-sm font-medium">
                            <i class="fas fa-home mr-1"></i>首頁
                        </a>
                        <a href="/search" class="nav-link <?= strpos($currentPath, '/search') === 0 ? 'active' : '' ?> text-gray-900 hover:text-blue-600 px-3 py-2 text-sm font-medium">
                            <i class="fas fa-search mr-1"></i>檢索
                        </a>
                        <a href="/documents" class="nav-link <?= strpos($currentPath, '/documents') === 0 ? 'active' : '' ?> text-gray-900 hover:text-blue-600 px-3 py-2 text-sm font-medium">
                            <i class="fas fa-file-alt mr-1"></i>文檔
                        </a>
                        <a href="/news" class="nav-link <?= strpos($currentPath, '/news') === 0 ? 'active' : '' ?> text-gray-900 hover:text-blue-600 px-3 py-2 text-sm font-medium">
                            <i class="fas fa-newspaper mr-1"></i>新聞
                        </a>
                    </div>
                </div>
                
                <!-- 搜尋欄 -->
                <div class="flex-1 flex items-center justify-center px-2 lg:ml-6 lg:justify-end">
                    <div class="max-w-lg w-full lg:max-w-xs">
                        <form action="/search" method="GET" class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-search text-gray-400"></i>
                            </div>
                            <input type="text" 
                                   name="q" 
                                   value="<?= htmlspecialchars($query ?? '') ?>"
                                   class="search-input block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm" 
                                   placeholder="搜尋文檔或新聞..."
                                   autocomplete="off">
                        </form>
                    </div>
                </div>
                
                <!-- 右側選單 -->
                <div class="flex items-center space-x-4">
                    <!-- 上傳按鈕 -->
                    <a href="/documents/upload" class="hidden md:inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white btn-primary">
                        <i class="fas fa-upload mr-2"></i>上傳文檔
                    </a>
                    
                    <!-- 行動選單按鈕 -->
                    <div class="md:hidden">
                        <button type="button" class="mobile-menu-button bg-gray-100 inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500">
                            <span class="sr-only">開啟主選單</span>
                            <i class="fas fa-bars"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 行動選單 -->
        <div class="mobile-menu hidden md:hidden">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t">
                <a href="/" class="<?= $currentPath === '/' ? 'bg-blue-50 text-blue-700' : 'text-gray-900' ?> hover:bg-gray-50 block px-3 py-2 rounded-md text-base font-medium">
                    <i class="fas fa-home mr-2"></i>首頁
                </a>
                <a href="/search" class="<?= strpos($currentPath, '/search') === 0 ? 'bg-blue-50 text-blue-700' : 'text-gray-900' ?> hover:bg-gray-50 block px-3 py-2 rounded-md text-base font-medium">
                    <i class="fas fa-search mr-2"></i>檢索
                </a>
                <a href="/documents" class="<?= strpos($currentPath, '/documents') === 0 ? 'bg-blue-50 text-blue-700' : 'text-gray-900' ?> hover:bg-gray-50 block px-3 py-2 rounded-md text-base font-medium">
                    <i class="fas fa-file-alt mr-2"></i>文檔
                </a>
                <a href="/news" class="<?= strpos($currentPath, '/news') === 0 ? 'bg-blue-50 text-blue-700' : 'text-gray-900' ?> hover:bg-gray-50 block px-3 py-2 rounded-md text-base font-medium">
                    <i class="fas fa-newspaper mr-2"></i>新聞
                </a>
                <a href="/documents/upload" class="text-gray-900 hover:bg-gray-50 block px-3 py-2 rounded-md text-base font-medium">
                    <i class="fas fa-upload mr-2"></i>上傳文檔
                </a>
            </div>
        </div>
    </nav>

    <!-- 主要內容區域 -->
    <main class="min-h-screen">