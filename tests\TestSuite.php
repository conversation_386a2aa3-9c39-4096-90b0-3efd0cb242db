<?php

namespace Tests;

use PHPUnit\Framework\TestSuite as PHPUnitTestSuite;

/**
 * 測試套件配置
 * 組織和管理所有測試
 */
class TestSuite
{
    /**
     * 建立完整測試套件
     */
    public static function suite(): PHPUnitTestSuite
    {
        $suite = new PHPUnitTestSuite('Tech.VG Website Test Suite');

        // 單元測試
        $suite->addTestSuite(\Tests\Unit\Controller\HomeControllerTest::class);
        $suite->addTestSuite(\Tests\Unit\Controller\ErrorControllerTest::class);
        $suite->addTestSuite(\Tests\Unit\Controller\Api\DocumentControllerTest::class);
        $suite->addTestSuite(\Tests\Unit\Controller\Api\NewsControllerTest::class);
        $suite->addTestSuite(\Tests\Unit\Controller\Api\SearchControllerTest::class);
        $suite->addTestSuite(\Tests\Unit\Controller\Api\CacheControllerTest::class);
        $suite->addTestSuite(\Tests\Unit\Controller\Api\StatsControllerTest::class);

        // 整合測試
        $suite->addTestSuite(\Tests\Integration\ControllerIntegrationTest::class);
        $suite->addTestSuite(\Tests\Integration\Api\ApiIntegrationTest::class);

        // 效能測試
        $suite->addTestSuite(\Tests\Performance\ApiPerformanceTest::class);

        return $suite;
    }

    /**
     * 建立單元測試套件
     */
    public static function unitTestSuite(): PHPUnitTestSuite
    {
        $suite = new PHPUnitTestSuite('Unit Tests');

        // 控制器單元測試
        $suite->addTestSuite(\Tests\Unit\Controller\HomeControllerTest::class);
        $suite->addTestSuite(\Tests\Unit\Controller\ErrorControllerTest::class);

        // API 控制器單元測試
        $suite->addTestSuite(\Tests\Unit\Controller\Api\DocumentControllerTest::class);
        $suite->addTestSuite(\Tests\Unit\Controller\Api\NewsControllerTest::class);
        $suite->addTestSuite(\Tests\Unit\Controller\Api\SearchControllerTest::class);
        $suite->addTestSuite(\Tests\Unit\Controller\Api\CacheControllerTest::class);
        $suite->addTestSuite(\Tests\Unit\Controller\Api\StatsControllerTest::class);

        return $suite;
    }

    /**
     * 建立整合測試套件
     */
    public static function integrationTestSuite(): PHPUnitTestSuite
    {
        $suite = new PHPUnitTestSuite('Integration Tests');

        $suite->addTestSuite(\Tests\Integration\ControllerIntegrationTest::class);
        $suite->addTestSuite(\Tests\Integration\Api\ApiIntegrationTest::class);

        return $suite;
    }

    /**
     * 建立效能測試套件
     */
    public static function performanceTestSuite(): PHPUnitTestSuite
    {
        $suite = new PHPUnitTestSuite('Performance Tests');

        $suite->addTestSuite(\Tests\Performance\ApiPerformanceTest::class);

        return $suite;
    }

    /**
     * 建立 API 測試套件
     */
    public static function apiTestSuite(): PHPUnitTestSuite
    {
        $suite = new PHPUnitTestSuite('API Tests');

        // API 單元測試
        $suite->addTestSuite(\Tests\Unit\Controller\Api\DocumentControllerTest::class);
        $suite->addTestSuite(\Tests\Unit\Controller\Api\NewsControllerTest::class);
        $suite->addTestSuite(\Tests\Unit\Controller\Api\SearchControllerTest::class);
        $suite->addTestSuite(\Tests\Unit\Controller\Api\CacheControllerTest::class);
        $suite->addTestSuite(\Tests\Unit\Controller\Api\StatsControllerTest::class);

        // API 整合測試
        $suite->addTestSuite(\Tests\Integration\Api\ApiIntegrationTest::class);

        // API 效能測試
        $suite->addTestSuite(\Tests\Performance\ApiPerformanceTest::class);

        return $suite;
    }

    /**
     * 建立控制器測試套件
     */
    public static function controllerTestSuite(): PHPUnitTestSuite
    {
        $suite = new PHPUnitTestSuite('Controller Tests');

        // 主要控制器測試
        $suite->addTestSuite(\Tests\Unit\Controller\HomeControllerTest::class);
        $suite->addTestSuite(\Tests\Unit\Controller\ErrorControllerTest::class);

        // API 控制器測試
        $suite->addTestSuite(\Tests\Unit\Controller\Api\DocumentControllerTest::class);
        $suite->addTestSuite(\Tests\Unit\Controller\Api\NewsControllerTest::class);
        $suite->addTestSuite(\Tests\Unit\Controller\Api\SearchControllerTest::class);
        $suite->addTestSuite(\Tests\Unit\Controller\Api\CacheControllerTest::class);
        $suite->addTestSuite(\Tests\Unit\Controller\Api\StatsControllerTest::class);

        // 控制器整合測試
        $suite->addTestSuite(\Tests\Integration\ControllerIntegrationTest::class);

        return $suite;
    }
}