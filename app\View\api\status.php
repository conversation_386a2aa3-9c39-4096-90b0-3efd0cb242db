<?php
/**
 * API 狀態監控頁面
 */

$title = 'Tech.VG API 系統狀態';
$description = 'Tech.VG API 系統狀態監控和健康檢查';
$status = $data['status'] ?? [];
$baseUrl = $data['base_url'] ?? '';
?>

<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($title) ?></title>
    <meta name="description" content="<?= htmlspecialchars($description) ?>">
    <meta http-equiv="refresh" content="30">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --dark-color: #1e293b;
            --light-color: #f8fafc;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--light-color);
            color: var(--dark-color);
        }

        .status-header {
            background: linear-gradient(135deg, var(--primary-color), #3b82f6);
            color: white;
            padding: 2rem 0;
        }

        .status-card {
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 1.5rem;
            overflow: hidden;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 0.5rem;
        }

        .status-healthy { background: var(--success-color); }
        .status-warning { background: var(--warning-color); }
        .status-critical { background: var(--danger-color); }
        .status-unhealthy { background: var(--danger-color); }
        .status-degraded { background: var(--warning-color); }

        .metric-card {
            text-align: center;
            padding: 1.5rem;
        }

        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .metric-label {
            color: var(--secondary-color);
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .progress-ring {
            width: 80px;
            height: 80px;
            margin: 0 auto 1rem;
        }

        .progress-ring-circle {
            stroke-width: 4;
            fill: transparent;
            stroke-dasharray: 251.2;
            stroke-dashoffset: 251.2;
            stroke-linecap: round;
            transform: rotate(-90deg);
            transform-origin: 50% 50%;
            transition: stroke-dashoffset 0.5s ease-in-out;
        }

        .service-list {
            list-style: none;
            padding: 0;
        }

        .service-item {
            display: flex;
            justify-content: between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid #f1f5f9;
        }

        .service-item:last-child {
            border-bottom: none;
        }

        .service-name {
            flex: 1;
            font-weight: 500;
        }

        .service-status {
            display: flex;
            align-items: center;
            font-size: 0.875rem;
        }

        .response-time {
            color: var(--secondary-color);
            font-size: 0.75rem;
            margin-left: 0.5rem;
        }

        .uptime-badge {
            background: var(--success-color);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.875rem;
            font-weight: 600;
        }

        .auto-refresh {
            position: fixed;
            top: 1rem;
            right: 1rem;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.25rem;
            font-size: 0.875rem;
            z-index: 1000;
        }

        .chart-container {
            height: 200px;
            display: flex;
            align-items: end;
            justify-content: space-between;
            padding: 1rem;
            background: var(--light-color);
            border-radius: 0.25rem;
            margin: 1rem 0;
        }

        .chart-bar {
            background: var(--primary-color);
            width: 20px;
            border-radius: 2px 2px 0 0;
            margin: 0 1px;
            transition: height 0.3s ease;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .pulse {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <!-- 自動刷新提示 -->
    <div class="auto-refresh">
        <i class="fas fa-sync-alt"></i> 自動刷新: 30秒
    </div>

    <!-- 標題區 -->
    <div class="status-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1>
                        <span class="status-indicator status-<?= htmlspecialchars($status['overall_status']) ?>"></span>
                        系統狀態監控
                    </h1>
                    <p class="mb-0">即時監控 Tech.VG API 系統健康狀態</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="uptime-badge">
                        運行時間: <?= htmlspecialchars($status['uptime']['human_readable'] ?? '未知') ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container mt-4">
        <!-- 總覽指標 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="status-card">
                    <div class="metric-card">
                        <div class="progress-ring">
                            <svg class="progress-ring" width="80" height="80">
                                <circle class="progress-ring-circle" 
                                        cx="40" cy="40" r="36"
                                        stroke="<?= $status['overall_status'] === 'healthy' ? '#059669' : ($status['overall_status'] === 'degraded' ? '#d97706' : '#dc2626') ?>"
                                        style="stroke-dashoffset: <?= 251.2 - (251.2 * 0.95) ?>"></circle>
                            </svg>
                        </div>
                        <div class="metric-value text-<?= $status['overall_status'] === 'healthy' ? 'success' : ($status['overall_status'] === 'degraded' ? 'warning' : 'danger') ?>">
                            <?= $status['overall_status'] === 'healthy' ? '正常' : ($status['overall_status'] === 'degraded' ? '部分異常' : '異常') ?>
                        </div>
                        <div class="metric-label">系統狀態</div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="status-card">
                    <div class="metric-card">
                        <div class="metric-value text-primary">
                            <?= htmlspecialchars($status['metrics']['requests_per_minute'] ?? '0') ?>
                        </div>
                        <div class="metric-label">每分鐘請求數</div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="status-card">
                    <div class="metric-card">
                        <div class="metric-value text-info">
                            <?= htmlspecialchars($status['metrics']['average_response_time'] ?? '0') ?>ms
                        </div>
                        <div class="metric-label">平均響應時間</div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="status-card">
                    <div class="metric-card">
                        <div class="metric-value text-<?= ($status['metrics']['error_rate'] ?? 0) > 0.05 ? 'danger' : 'success' ?>">
                            <?= number_format(($status['metrics']['error_rate'] ?? 0) * 100, 2) ?>%
                        </div>
                        <div class="metric-label">錯誤率</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 服務狀態 -->
        <div class="row">
            <div class="col-lg-8">
                <div class="status-card">
                    <div class="card-header bg-light">
                        <h5 class="mb-0"><i class="fas fa-server"></i> 服務狀態</h5>
                    </div>
                    <div class="card-body">
                        <ul class="service-list">
                            <?php foreach ($status['services'] ?? [] as $serviceName => $service): ?>
                                <li class="service-item">
                                    <div class="service-name">
                                        <i class="fas fa-<?= getServiceIcon($serviceName) ?>"></i>
                                        <?= htmlspecialchars(getServiceDisplayName($serviceName)) ?>
                                    </div>
                                    <div class="service-status">
                                        <span class="status-indicator status-<?= htmlspecialchars($service['status']) ?>"></span>
                                        <?= htmlspecialchars(ucfirst($service['status'])) ?>
                                        <?php if (isset($service['response_time'])): ?>
                                            <span class="response-time"><?= htmlspecialchars($service['response_time']) ?>ms</span>
                                        <?php endif; ?>
                                    </div>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                </div>

                <!-- API 端點狀態 -->
                <?php if (isset($status['services']['api_endpoints']['endpoints'])): ?>
                    <div class="status-card">
                        <div class="card-header bg-light">
                            <h5 class="mb-0"><i class="fas fa-plug"></i> API 端點</h5>
                        </div>
                        <div class="card-body">
                            <ul class="service-list">
                                <?php foreach ($status['services']['api_endpoints']['endpoints'] as $endpoint => $endpointStatus): ?>
                                    <li class="service-item">
                                        <div class="service-name">
                                            <span class="badge bg-secondary me-2"><?= htmlspecialchars($endpointStatus['method']) ?></span>
                                            <?= htmlspecialchars($endpoint) ?>
                                        </div>
                                        <div class="service-status">
                                            <span class="status-indicator status-<?= htmlspecialchars($endpointStatus['status']) ?>"></span>
                                            <?= htmlspecialchars(ucfirst($endpointStatus['status'])) ?>
                                            <?php if (isset($endpointStatus['response_time_ms'])): ?>
                                                <span class="response-time"><?= htmlspecialchars($endpointStatus['response_time_ms']) ?>ms</span>
                                            <?php endif; ?>
                                        </div>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- 系統資源 -->
            <div class="col-lg-4">
                <!-- 記憶體使用量 -->
                <?php if (isset($status['services']['memory'])): ?>
                    <div class="status-card">
                        <div class="card-header bg-light">
                            <h6 class="mb-0"><i class="fas fa-memory"></i> 記憶體使用量</h6>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>使用率</span>
                                <span class="fw-bold"><?= htmlspecialchars($status['services']['memory']['percentage']) ?>%</span>
                            </div>
                            <div class="progress mb-2">
                                <div class="progress-bar bg-<?= $status['services']['memory']['status'] === 'healthy' ? 'success' : ($status['services']['memory']['status'] === 'warning' ? 'warning' : 'danger') ?>" 
                                     style="width: <?= htmlspecialchars($status['services']['memory']['percentage']) ?>%"></div>
                            </div>
                            <small class="text-muted">
                                <?= htmlspecialchars($status['services']['memory']['usage_mb']) ?>MB / 
                                <?= htmlspecialchars($status['services']['memory']['limit_mb']) ?>MB
                            </small>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- 磁碟使用量 -->
                <?php if (isset($status['services']['disk'])): ?>
                    <div class="status-card">
                        <div class="card-header bg-light">
                            <h6 class="mb-0"><i class="fas fa-hdd"></i> 磁碟使用量</h6>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>使用率</span>
                                <span class="fw-bold"><?= htmlspecialchars($status['services']['disk']['used_percentage']) ?>%</span>
                            </div>
                            <div class="progress mb-2">
                                <div class="progress-bar bg-<?= $status['services']['disk']['status'] === 'healthy' ? 'success' : ($status['services']['disk']['status'] === 'warning' ? 'warning' : 'danger') ?>" 
                                     style="width: <?= htmlspecialchars($status['services']['disk']['used_percentage']) ?>%"></div>
                            </div>
                            <small class="text-muted">
                                <?= htmlspecialchars($status['services']['disk']['free_gb']) ?>GB 可用 / 
                                <?= htmlspecialchars($status['services']['disk']['total_gb']) ?>GB 總計
                            </small>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- 系統資訊 -->
                <div class="status-card">
                    <div class="card-header bg-light">
                        <h6 class="mb-0"><i class="fas fa-info-circle"></i> 系統資訊</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li class="mb-2">
                                <strong>API 版本:</strong> <?= htmlspecialchars($status['version']) ?>
                            </li>
                            <li class="mb-2">
                                <strong>環境:</strong> <?= htmlspecialchars($status['environment']['operating_system'] ?? '未知') ?>
                            </li>
                            <li class="mb-2">
                                <strong>PHP 版本:</strong> <?= htmlspecialchars($status['environment']['php_version'] ?? '未知') ?>
                            </li>
                            <li class="mb-2">
                                <strong>時區:</strong> <?= htmlspecialchars($status['environment']['timezone'] ?? '未知') ?>
                            </li>
                            <li>
                                <strong>最後更新:</strong> 
                                <span class="text-muted"><?= date('Y-m-d H:i:s') ?></span>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- 快速連結 -->
                <div class="status-card">
                    <div class="card-header bg-light">
                        <h6 class="mb-0"><i class="fas fa-external-link-alt"></i> 快速連結</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="<?= htmlspecialchars($baseUrl) ?>/api/docs?format=html" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-book"></i> API 文檔
                            </a>
                            <a href="<?= htmlspecialchars($baseUrl) ?>/api/test?format=html" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-flask"></i> 測試工具
                            </a>
                            <a href="<?= htmlspecialchars($baseUrl) ?>/api/changelog?format=html" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-history"></i> 變更日誌
                            </a>
                            <a href="<?= htmlspecialchars($baseUrl) ?>/api/health" class="btn btn-outline-success btn-sm">
                                <i class="fas fa-heartbeat"></i> 健康檢查
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 自動刷新頁面
        let refreshTimer = 30;
        const refreshElement = document.querySelector('.auto-refresh');
        
        setInterval(() => {
            refreshTimer--;
            refreshElement.innerHTML = `<i class="fas fa-sync-alt"></i> 自動刷新: ${refreshTimer}秒`;
            
            if (refreshTimer <= 0) {
                location.reload();
            }
        }, 1000);

        // 添加脈衝效果到異常狀態
        document.querySelectorAll('.status-critical, .status-unhealthy').forEach(element => {
            element.classList.add('pulse');
        });

        // 模擬即時資料更新
        function updateMetrics() {
            // 這裡可以通過 AJAX 更新指標
            // 為了演示，我們只是添加一些動畫效果
            
            const progressBars = document.querySelectorAll('.progress-bar');
            progressBars.forEach(bar => {
                bar.style.transition = 'width 0.5s ease';
            });
        }

        // 每 5 秒更新一次指標
        setInterval(updateMetrics, 5000);
    </script>
</body>
</html>

<?php
/**
 * 取得服務圖示
 */
function getServiceIcon($serviceName): string {
    $icons = [
        'database' => 'database',
        'filesystem' => 'folder',
        'memory' => 'memory',
        'disk' => 'hdd',
        'api_endpoints' => 'plug'
    ];
    
    return $icons[$serviceName] ?? 'cog';
}

/**
 * 取得服務顯示名稱
 */
function getServiceDisplayName($serviceName): string {
    $names = [
        'database' => '資料庫',
        'filesystem' => '檔案系統',
        'memory' => '記憶體',
        'disk' => '磁碟空間',
        'api_endpoints' => 'API 端點'
    ];
    
    return $names[$serviceName] ?? $serviceName;
}
?>