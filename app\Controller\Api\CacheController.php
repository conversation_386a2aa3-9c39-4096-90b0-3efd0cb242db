<?php

namespace App\Controller\Api;

/**
 * API 快取控制器
 * 管理 API 快取相關功能
 */
class CacheController extends BaseApiController
{
    private string $cacheDir;

    public function __construct()
    {
        parent::__construct();
        $this->cacheDir = __DIR__ . '/../../../storage/cache/api';
        
        // 確保快取目錄存在
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
    }

    /**
     * 取得快取狀態
     * 
     * GET /api/cache/status
     */
    public function status(): void
    {
        try {
            $cacheStats = $this->getCacheStatistics();
            
            $this->successResponse([
                'cache_enabled' => true,
                'cache_directory' => $this->cacheDir,
                'statistics' => $cacheStats,
                'cache_policies' => $this->getCachePolicies()
            ], '快取狀態取得成功');

        } catch (\Exception $e) {
            $this->errorResponse('無法取得快取狀態', 500, [], 'CACHE_STATUS_ERROR');
        }
    }

    /**
     * 清除快取
     * 
     * DELETE /api/cache
     * 
     * 參數：
     * - type: 快取類型 (all, search, documents, news, stats)
     * - pattern: 快取鍵模式
     */
    public function clear(): void
    {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'DELETE') {
                $this->errorResponse('只支援 DELETE 請求', 405);
            }

            $data = $this->getRequestData();
            $type = $data['type'] ?? 'all';
            $pattern = $data['pattern'] ?? '';

            $clearedCount = 0;

            switch ($type) {
                case 'all':
                    $clearedCount = $this->clearAllCache();
                    break;
                    
                case 'search':
                    $clearedCount = $this->clearCacheByPattern('search_*');
                    break;
                    
                case 'documents':
                    $clearedCount = $this->clearCacheByPattern('documents_*');
                    break;
                    
                case 'news':
                    $clearedCount = $this->clearCacheByPattern('news_*');
                    break;
                    
                case 'stats':
                    $clearedCount = $this->clearCacheByPattern('stats_*');
                    break;
                    
                case 'pattern':
                    if (empty($pattern)) {
                        $this->errorResponse('使用 pattern 類型時必須提供 pattern 參數', 400);
                    }
                    $clearedCount = $this->clearCacheByPattern($pattern);
                    break;
                    
                default:
                    $this->errorResponse('不支援的快取類型', 400);
            }

            $this->successResponse([
                'type' => $type,
                'pattern' => $pattern,
                'cleared_count' => $clearedCount
            ], "成功清除 {$clearedCount} 個快取項目");

        } catch (\Exception $e) {
            $this->errorResponse('清除快取失敗', 500, [], 'CACHE_CLEAR_ERROR');
        }
    }

    /**
     * 預熱快取
     * 
     * POST /api/cache/warmup
     * 
     * 參數：
     * - endpoints: 要預熱的端點陣列
     */
    public function warmup(): void
    {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                $this->errorResponse('只支援 POST 請求', 405);
            }

            $data = $this->getRequestData();
            $endpoints = $data['endpoints'] ?? $this->getDefaultWarmupEndpoints();

            $results = [];
            $successCount = 0;

            foreach ($endpoints as $endpoint) {
                $result = $this->warmupEndpoint($endpoint);
                $results[] = $result;
                
                if ($result['success']) {
                    $successCount++;
                }
            }

            $this->successResponse([
                'total_endpoints' => count($endpoints),
                'successful' => $successCount,
                'failed' => count($endpoints) - $successCount,
                'results' => $results
            ], "快取預熱完成：成功 {$successCount} 個端點");

        } catch (\Exception $e) {
            $this->errorResponse('快取預熱失敗', 500, [], 'CACHE_WARMUP_ERROR');
        }
    }

    /**
     * 取得快取項目
     * 
     * GET /api/cache/items
     * 
     * 參數：
     * - pattern: 快取鍵模式
     * - limit: 結果數量限制
     */
    public function items(): void
    {
        try {
            $pattern = $this->getGet('pattern', '*');
            $limit = min(1000, max(1, (int)$this->getGet('limit', 100)));

            $items = $this->getCacheItems($pattern, $limit);

            $this->successResponse([
                'pattern' => $pattern,
                'total_items' => count($items),
                'limit' => $limit,
                'items' => $items
            ], '快取項目取得成功');

        } catch (\Exception $e) {
            $this->errorResponse('取得快取項目失敗', 500, [], 'CACHE_ITEMS_ERROR');
        }
    }

    /**
     * 設定快取
     * 
     * PUT /api/cache/set
     * 
     * 請求體：
     * - key: 快取鍵
     * - value: 快取值
     * - ttl: 過期時間（秒）
     */
    public function set(): void
    {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'PUT') {
                $this->errorResponse('只支援 PUT 請求', 405);
            }

            $data = $this->getRequestData();
            
            $rules = [
                'key' => 'required|max:255',
                'value' => 'required',
                'ttl' => 'integer|min:1|max:86400'
            ];

            $errors = $this->validateRequest($rules, $data);
            if (!empty($errors)) {
                $this->errorResponse('請求參數驗證失敗', 400, $errors);
            }

            $key = $data['key'];
            $value = $data['value'];
            $ttl = $data['ttl'] ?? 3600;

            $success = $this->setCache($key, $value, $ttl);

            if ($success) {
                $this->successResponse([
                    'key' => $key,
                    'ttl' => $ttl,
                    'expires_at' => date('c', time() + $ttl)
                ], '快取設定成功');
            } else {
                $this->errorResponse('快取設定失敗', 500);
            }

        } catch (\Exception $e) {
            $this->errorResponse('快取設定失敗', 500, [], 'CACHE_SET_ERROR');
        }
    }

    /**
     * 取得快取統計
     * 
     * @return array
     */
    private function getCacheStatistics(): array
    {
        $files = glob($this->cacheDir . '/*');
        $totalFiles = count($files);
        $totalSize = 0;
        $expiredCount = 0;
        $now = time();

        foreach ($files as $file) {
            if (is_file($file)) {
                $totalSize += filesize($file);
                
                // 檢查是否過期
                $data = json_decode(file_get_contents($file), true);
                if ($data && isset($data['expires_at']) && $data['expires_at'] < $now) {
                    $expiredCount++;
                }
            }
        }

        return [
            'total_items' => $totalFiles,
            'total_size' => $this->formatBytes($totalSize),
            'expired_items' => $expiredCount,
            'hit_rate' => $this->calculateHitRate(),
            'last_cleanup' => $this->getLastCleanupTime()
        ];
    }

    /**
     * 取得快取策略
     * 
     * @return array
     */
    private function getCachePolicies(): array
    {
        return [
            'search_results' => ['ttl' => 300, 'description' => '搜尋結果快取 5 分鐘'],
            'document_list' => ['ttl' => 600, 'description' => '文檔列表快取 10 分鐘'],
            'news_list' => ['ttl' => 180, 'description' => '新聞列表快取 3 分鐘'],
            'statistics' => ['ttl' => 1800, 'description' => '統計資料快取 30 分鐘'],
            'popular_content' => ['ttl' => 3600, 'description' => '熱門內容快取 1 小時']
        ];
    }

    /**
     * 清除所有快取
     * 
     * @return int
     */
    private function clearAllCache(): int
    {
        $files = glob($this->cacheDir . '/*');
        $count = 0;

        foreach ($files as $file) {
            if (is_file($file) && unlink($file)) {
                $count++;
            }
        }

        return $count;
    }

    /**
     * 根據模式清除快取
     * 
     * @param string $pattern
     * @return int
     */
    private function clearCacheByPattern(string $pattern): int
    {
        $files = glob($this->cacheDir . '/' . $pattern);
        $count = 0;

        foreach ($files as $file) {
            if (is_file($file) && unlink($file)) {
                $count++;
            }
        }

        return $count;
    }

    /**
     * 取得預設預熱端點
     * 
     * @return array
     */
    private function getDefaultWarmupEndpoints(): array
    {
        $baseUrl = $this->getBaseUrl();
        
        return [
            $baseUrl . '/api/stats/overview',
            $baseUrl . '/api/news/latest?limit=10',
            $baseUrl . '/api/documents?per_page=20',
            $baseUrl . '/api/search/popular',
            $baseUrl . '/api/news/categories'
        ];
    }

    /**
     * 預熱單一端點
     * 
     * @param string $endpoint
     * @return array
     */
    private function warmupEndpoint(string $endpoint): array
    {
        try {
            $startTime = microtime(true);
            
            // 這裡應該實際發送 HTTP 請求來預熱快取
            // 暫時模擬
            usleep(rand(50000, 200000)); // 模擬 50-200ms 的響應時間
            
            $responseTime = (microtime(true) - $startTime) * 1000;
            
            return [
                'endpoint' => $endpoint,
                'success' => true,
                'response_time_ms' => round($responseTime, 2),
                'cached' => true
            ];

        } catch (\Exception $e) {
            return [
                'endpoint' => $endpoint,
                'success' => false,
                'error' => $e->getMessage(),
                'cached' => false
            ];
        }
    }

    /**
     * 取得快取項目
     * 
     * @param string $pattern
     * @param int $limit
     * @return array
     */
    private function getCacheItems(string $pattern, int $limit): array
    {
        $files = glob($this->cacheDir . '/' . $pattern);
        $items = [];
        $count = 0;

        foreach ($files as $file) {
            if ($count >= $limit) {
                break;
            }

            if (is_file($file)) {
                $key = basename($file);
                $data = json_decode(file_get_contents($file), true);
                
                $items[] = [
                    'key' => $key,
                    'size' => $this->formatBytes(filesize($file)),
                    'created_at' => date('c', filectime($file)),
                    'expires_at' => isset($data['expires_at']) ? date('c', $data['expires_at']) : null,
                    'expired' => isset($data['expires_at']) && $data['expires_at'] < time()
                ];
                
                $count++;
            }
        }

        return $items;
    }

    /**
     * 設定快取
     * 
     * @param string $key
     * @param mixed $value
     * @param int $ttl
     * @return bool
     */
    private function setCache(string $key, $value, int $ttl): bool
    {
        $data = [
            'value' => $value,
            'created_at' => time(),
            'expires_at' => time() + $ttl
        ];

        $file = $this->cacheDir . '/' . $key;
        return file_put_contents($file, json_encode($data)) !== false;
    }

    /**
     * 計算快取命中率
     * 
     * @return string
     */
    private function calculateHitRate(): string
    {
        // 這裡應該實現實際的命中率計算
        // 暫時返回模擬數據
        return '85.2%';
    }

    /**
     * 取得最後清理時間
     * 
     * @return string
     */
    private function getLastCleanupTime(): string
    {
        $cleanupFile = $this->cacheDir . '/.last_cleanup';
        
        if (file_exists($cleanupFile)) {
            $timestamp = (int)file_get_contents($cleanupFile);
            return date('c', $timestamp);
        }
        
        return 'Never';
    }

    /**
     * 取得基礎 URL
     * 
     * @return string
     */
    private function getBaseUrl(): string
    {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        return "{$protocol}://{$host}";
    }
}