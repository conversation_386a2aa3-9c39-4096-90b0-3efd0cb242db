<?php

namespace App\Controller\Api;

use App\Service\DocumentService;
use App\Service\NewsService;
use App\Service\SearchService;

/**
 * 統計 API 控制器
 * 處理系統統計相關的 API 請求
 */
class StatsController extends BaseApiController
{
    private DocumentService $documentService;
    private NewsService $newsService;
    private SearchService $searchService;

    public function __construct()
    {
        parent::__construct();
        $this->documentService = new DocumentService();
        $this->newsService = new NewsService();
        $this->searchService = new SearchService();
    }

    /**
     * 取得文檔統計
     * 
     * GET /api/stats/documents
     * 
     * 參數：
     * - days: 統計天數 (預設: 30)
     * - group_by: 分組方式 (day, week, month)
     */
    public function documents(): void
    {
        try {
            // 檢查速率限制
            $clientIp = $this->getClientIp();
            if (!$this->checkRateLimit($clientIp, 100, 3600)) {
                $this->errorResponse('請求過於頻繁，請稍後再試', 429, [], 'RATE_LIMIT_EXCEEDED');
            }

            $days = max(1, min(365, (int)$this->getGet('days', 30)));
            $groupBy = $this->getGet('group_by', 'day');

            if (!in_array($groupBy, ['day', 'week', 'month'])) {
                $groupBy = 'day';
            }

            // 取得基本統計
            $basicStatsResult = $this->documentService->getStatistics();
            if (!$basicStatsResult['success']) {
                $this->errorResponse($basicStatsResult['message'] ?? '取得文檔統計失敗', 500, [], 'DOCUMENT_STATS_ERROR');
            }

            // 取得時間序列統計
            $timeSeriesResult = $this->documentService->getTimeSeriesStats($days, $groupBy);
            if (!$timeSeriesResult['success']) {
                $this->errorResponse($timeSeriesResult['message'] ?? '取得文檔時間統計失敗', 500, [], 'DOCUMENT_TIME_STATS_ERROR');
            }

            // 取得檔案類型統計
            $fileTypeStatsResult = $this->documentService->getFileTypeStats();
            if (!$fileTypeStatsResult['success']) {
                $this->errorResponse($fileTypeStatsResult['message'] ?? '取得檔案類型統計失敗', 500, [], 'FILE_TYPE_STATS_ERROR');
            }

            // 取得狀態統計
            $statusStatsResult = $this->documentService->getStatusStats();
            if (!$statusStatsResult['success']) {
                $this->errorResponse($statusStatsResult['message'] ?? '取得文檔狀態統計失敗', 500, [], 'DOCUMENT_STATUS_STATS_ERROR');
            }

            $this->successResponse([
                'basic_stats' => $basicStatsResult['data'],
                'time_series' => $timeSeriesResult['data'],
                'file_type_distribution' => $fileTypeStatsResult['data'],
                'status_distribution' => $statusStatsResult['data'],
                'period' => [
                    'days' => $days,
                    'group_by' => $groupBy,
                    'start_date' => date('Y-m-d', strtotime("-{$days} days")),
                    'end_date' => date('Y-m-d')
                ]
            ], '文檔統計取得成功');

        } catch (\Exception $e) {
            $this->errorResponse('文檔統計服務暫時不可用', 500, [], 'DOCUMENT_STATS_SERVICE_ERROR');
        }
    }

    /**
     * 取得新聞統計
     * 
     * GET /api/stats/news
     * 
     * 參數：
     * - days: 統計天數 (預設: 30)
     * - group_by: 分組方式 (day, week, month)
     */
    public function news(): void
    {
        try {
            // 檢查速率限制
            $clientIp = $this->getClientIp();
            if (!$this->checkRateLimit($clientIp, 100, 3600)) {
                $this->errorResponse('請求過於頻繁，請稍後再試', 429, [], 'RATE_LIMIT_EXCEEDED');
            }

            $days = max(1, min(365, (int)$this->getGet('days', 30)));
            $groupBy = $this->getGet('group_by', 'day');

            if (!in_array($groupBy, ['day', 'week', 'month'])) {
                $groupBy = 'day';
            }

            // 取得基本統計
            $basicStatsResult = $this->newsService->getNewsStatistics($days);
            if (!$basicStatsResult['success']) {
                $this->errorResponse($basicStatsResult['message'] ?? '取得新聞統計失敗', 500, [], 'NEWS_STATS_ERROR');
            }

            // 取得時間序列統計
            $timeSeriesResult = $this->newsService->getTimeSeriesStats($days, $groupBy);
            if (!$timeSeriesResult['success']) {
                $this->errorResponse($timeSeriesResult['message'] ?? '取得新聞時間統計失敗', 500, [], 'NEWS_TIME_STATS_ERROR');
            }

            // 取得分類統計
            $categoryStatsResult = $this->newsService->getCategoryStats($days);
            if (!$categoryStatsResult['success']) {
                $this->errorResponse($categoryStatsResult['message'] ?? '取得新聞分類統計失敗', 500, [], 'NEWS_CATEGORY_STATS_ERROR');
            }

            // 取得新聞源統計
            $sourceStatsResult = $this->newsService->getSourceStats($days);
            if (!$sourceStatsResult['success']) {
                $this->errorResponse($sourceStatsResult['message'] ?? '取得新聞源統計失敗', 500, [], 'NEWS_SOURCE_STATS_ERROR');
            }

            // 取得熱門新聞
            $popularNewsResult = $this->newsService->getPopularNews(10, $days);
            if (!$popularNewsResult['success']) {
                $this->errorResponse($popularNewsResult['message'] ?? '取得熱門新聞失敗', 500, [], 'POPULAR_NEWS_ERROR');
            }

            $this->successResponse([
                'basic_stats' => $basicStatsResult['data'],
                'time_series' => $timeSeriesResult['data'],
                'category_distribution' => $categoryStatsResult['data'],
                'source_distribution' => $sourceStatsResult['data'],
                'popular_news' => $popularNewsResult['data'],
                'period' => [
                    'days' => $days,
                    'group_by' => $groupBy,
                    'start_date' => date('Y-m-d', strtotime("-{$days} days")),
                    'end_date' => date('Y-m-d')
                ]
            ], '新聞統計取得成功');

        } catch (\Exception $e) {
            $this->errorResponse('新聞統計服務暫時不可用', 500, [], 'NEWS_STATS_SERVICE_ERROR');
        }
    }

    /**
     * 取得搜尋統計
     * 
     * GET /api/stats/search
     * 
     * 參數：
     * - days: 統計天數 (預設: 30)
     * - group_by: 分組方式 (day, week, month)
     */
    public function search(): void
    {
        try {
            // 檢查速率限制
            $clientIp = $this->getClientIp();
            if (!$this->checkRateLimit($clientIp, 100, 3600)) {
                $this->errorResponse('請求過於頻繁，請稍後再試', 429, [], 'RATE_LIMIT_EXCEEDED');
            }

            $days = max(1, min(365, (int)$this->getGet('days', 30)));
            $groupBy = $this->getGet('group_by', 'day');

            if (!in_array($groupBy, ['day', 'week', 'month'])) {
                $groupBy = 'day';
            }

            // 取得基本統計
            $basicStatsResult = $this->searchService->getSearchStatistics($days);
            if (!$basicStatsResult['success']) {
                $this->errorResponse($basicStatsResult['message'] ?? '取得搜尋統計失敗', 500, [], 'SEARCH_STATS_ERROR');
            }

            // 取得時間序列統計
            $timeSeriesResult = $this->searchService->getTimeSeriesStats($days, $groupBy);
            if (!$timeSeriesResult['success']) {
                $this->errorResponse($timeSeriesResult['message'] ?? '取得搜尋時間統計失敗', 500, [], 'SEARCH_TIME_STATS_ERROR');
            }

            // 取得熱門搜尋
            $popularSearchesResult = $this->searchService->getPopularSearches($days, 20);
            if (!$popularSearchesResult['success']) {
                $this->errorResponse($popularSearchesResult['message'] ?? '取得熱門搜尋失敗', 500, [], 'POPULAR_SEARCHES_ERROR');
            }

            // 取得搜尋類型統計
            $typeStatsResult = $this->searchService->getSearchTypeStats($days);
            if (!$typeStatsResult['success']) {
                $this->errorResponse($typeStatsResult['message'] ?? '取得搜尋類型統計失敗', 500, [], 'SEARCH_TYPE_STATS_ERROR');
            }

            $this->successResponse([
                'basic_stats' => $basicStatsResult['data'],
                'time_series' => $timeSeriesResult['data'],
                'popular_searches' => $popularSearchesResult['data'],
                'type_distribution' => $typeStatsResult['data'],
                'period' => [
                    'days' => $days,
                    'group_by' => $groupBy,
                    'start_date' => date('Y-m-d', strtotime("-{$days} days")),
                    'end_date' => date('Y-m-d')
                ]
            ], '搜尋統計取得成功');

        } catch (\Exception $e) {
            $this->errorResponse('搜尋統計服務暫時不可用', 500, [], 'SEARCH_STATS_SERVICE_ERROR');
        }
    }

    /**
     * 取得系統總覽統計
     * 
     * GET /api/stats/overview
     * 
     * 參數：
     * - days: 統計天數 (預設: 30)
     */
    public function overview(): void
    {
        try {
            // 檢查速率限制
            $clientIp = $this->getClientIp();
            if (!$this->checkRateLimit($clientIp, 50, 3600)) {
                $this->errorResponse('請求過於頻繁，請稍後再試', 429, [], 'RATE_LIMIT_EXCEEDED');
            }

            $days = max(1, min(365, (int)$this->getGet('days', 30)));

            // 取得各項統計
            $documentStats = $this->documentService->getStatistics();
            $newsStats = $this->newsService->getNewsStatistics($days);
            $searchStats = $this->searchService->getSearchStatistics($days);

            // 計算成長率
            $previousDays = $days;
            $documentGrowth = $this->calculateGrowthRate('documents', $days, $previousDays);
            $newsGrowth = $this->calculateGrowthRate('news', $days, $previousDays);
            $searchGrowth = $this->calculateGrowthRate('searches', $days, $previousDays);

            // 取得系統健康狀態
            $systemHealth = $this->getSystemHealth();

            $this->successResponse([
                'summary' => [
                    'total_documents' => $documentStats['data']['total'] ?? 0,
                    'total_news' => $newsStats['data']['total_articles'] ?? 0,
                    'total_searches' => $searchStats['data']['total_searches'] ?? 0,
                    'total_words' => $documentStats['data']['total_words'] ?? 0,
                    'indexed_documents' => $documentStats['data']['indexed'] ?? 0
                ],
                'growth_rates' => [
                    'documents' => $documentGrowth,
                    'news' => $newsGrowth,
                    'searches' => $searchGrowth
                ],
                'system_health' => $systemHealth,
                'period' => [
                    'days' => $days,
                    'start_date' => date('Y-m-d', strtotime("-{$days} days")),
                    'end_date' => date('Y-m-d')
                ],
                'last_updated' => date('Y-m-d H:i:s')
            ], '系統總覽統計取得成功');

        } catch (\Exception $e) {
            $this->errorResponse('系統統計服務暫時不可用', 500, [], 'SYSTEM_STATS_SERVICE_ERROR');
        }
    }

    /**
     * 取得即時統計
     * 
     * GET /api/stats/realtime
     */
    public function realtime(): void
    {
        try {
            // 檢查速率限制
            $clientIp = $this->getClientIp();
            if (!$this->checkRateLimit($clientIp, 200, 3600)) {
                $this->errorResponse('請求過於頻繁，請稍後再試', 429, [], 'RATE_LIMIT_EXCEEDED');
            }

            // 取得最近1小時的統計
            $realtimeStats = [
                'searches_last_hour' => $this->searchService->getRealtimeSearchCount(1),
                'documents_uploaded_today' => $this->documentService->getTodayUploadCount(),
                'news_added_today' => $this->newsService->getTodayNewsCount(),
                'popular_searches_now' => $this->searchService->getPopularSearches(1, 5)['data'] ?? [],
                'system_load' => $this->getSystemLoad(),
                'active_users' => $this->getActiveUsersCount()
            ];

            $this->successResponse([
                'realtime_stats' => $realtimeStats,
                'timestamp' => date('Y-m-d H:i:s'),
                'timezone' => date_default_timezone_get()
            ], '即時統計取得成功');

        } catch (\Exception $e) {
            $this->errorResponse('即時統計服務暫時不可用', 500, [], 'REALTIME_STATS_SERVICE_ERROR');
        }
    }

    /**
     * 匯出統計報告
     * 
     * GET /api/stats/export
     * 
     * 參數：
     * - type: 報告類型 (documents, news, search, overview)
     * - format: 匯出格式 (json, csv)
     * - days: 統計天數
     */
    public function export(): void
    {
        try {
            // 檢查速率限制
            $clientIp = $this->getClientIp();
            if (!$this->checkRateLimit($clientIp, 10, 3600)) {
                $this->errorResponse('請求過於頻繁，請稍後再試', 429, [], 'RATE_LIMIT_EXCEEDED');
            }

            $type = $this->getGet('type', 'overview');
            $format = $this->getGet('format', 'json');
            $days = max(1, min(365, (int)$this->getGet('days', 30)));

            if (!in_array($type, ['documents', 'news', 'search', 'overview'])) {
                $this->errorResponse('不支援的報告類型', 400, [], 'INVALID_REPORT_TYPE');
            }

            if (!in_array($format, ['json', 'csv'])) {
                $this->errorResponse('不支援的匯出格式', 400, [], 'INVALID_EXPORT_FORMAT');
            }

            // 取得統計資料
            $data = $this->getStatsData($type, $days);

            if ($format === 'csv') {
                $this->exportToCsv($data, $type, $days);
            } else {
                $this->successResponse([
                    'report_type' => $type,
                    'export_format' => $format,
                    'period_days' => $days,
                    'generated_at' => date('Y-m-d H:i:s'),
                    'data' => $data
                ], '統計報告匯出成功');
            }

        } catch (\Exception $e) {
            $this->errorResponse('統計報告匯出服務暫時不可用', 500, [], 'STATS_EXPORT_SERVICE_ERROR');
        }
    }

    /**
     * 計算成長率
     * 
     * @param string $type 統計類型
     * @param int $currentDays 當前期間天數
     * @param int $previousDays 比較期間天數
     * @return float
     */
    private function calculateGrowthRate(string $type, int $currentDays, int $previousDays): float
    {
        try {
            // 這裡應該實現實際的成長率計算邏輯
            // 暫時返回模擬數據
            $growthRates = [
                'documents' => 5.2,
                'news' => 12.8,
                'searches' => 8.7
            ];

            return $growthRates[$type] ?? 0.0;

        } catch (\Exception $e) {
            return 0.0;
        }
    }

    /**
     * 取得系統健康狀態
     * 
     * @return array
     */
    private function getSystemHealth(): array
    {
        try {
            return [
                'database' => 'healthy',
                'search_engine' => 'healthy',
                'file_system' => 'healthy',
                'memory_usage' => '65%',
                'disk_usage' => '42%',
                'uptime' => '15 days',
                'last_backup' => date('Y-m-d H:i:s', strtotime('-2 hours'))
            ];

        } catch (\Exception $e) {
            return [
                'status' => 'unknown',
                'error' => 'Unable to retrieve system health'
            ];
        }
    }

    /**
     * 取得系統負載
     * 
     * @return array
     */
    private function getSystemLoad(): array
    {
        try {
            return [
                'cpu_usage' => '45%',
                'memory_usage' => '68%',
                'disk_io' => 'low',
                'network_io' => 'medium'
            ];

        } catch (\Exception $e) {
            return [
                'status' => 'unknown'
            ];
        }
    }

    /**
     * 取得活躍用戶數
     * 
     * @return int
     */
    private function getActiveUsersCount(): int
    {
        try {
            // 這裡應該實現實際的活躍用戶統計邏輯
            // 暫時返回模擬數據
            return rand(50, 200);

        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * 取得統計資料
     * 
     * @param string $type 統計類型
     * @param int $days 天數
     * @return array
     */
    private function getStatsData(string $type, int $days): array
    {
        switch ($type) {
            case 'documents':
                $result = $this->documentService->getStatistics();
                return $result['data'] ?? [];

            case 'news':
                $result = $this->newsService->getNewsStatistics($days);
                return $result['data'] ?? [];

            case 'search':
                $result = $this->searchService->getSearchStatistics($days);
                return $result['data'] ?? [];

            case 'overview':
                return [
                    'documents' => $this->documentService->getStatistics()['data'] ?? [],
                    'news' => $this->newsService->getNewsStatistics($days)['data'] ?? [],
                    'search' => $this->searchService->getSearchStatistics($days)['data'] ?? []
                ];

            default:
                return [];
        }
    }

    /**
     * 匯出為 CSV 格式
     * 
     * @param array $data 資料
     * @param string $type 類型
     * @param int $days 天數
     */
    private function exportToCsv(array $data, string $type, int $days): void
    {
        $filename = "techvg_stats_{$type}_" . date('Y-m-d') . '.csv';
        
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        // 寫入 BOM 以支援中文
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
        
        // 寫入標題行
        fputcsv($output, ['統計項目', '數值', '說明']);
        
        // 寫入資料
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $value = json_encode($value, JSON_UNESCAPED_UNICODE);
            }
            fputcsv($output, [$key, $value, '']);
        }
        
        fclose($output);
        exit;
    }
}