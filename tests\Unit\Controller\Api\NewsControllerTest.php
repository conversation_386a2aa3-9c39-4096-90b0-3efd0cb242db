<?php

namespace Tests\Unit\Controller\Api;

use Tests\TestCase;
use App\Controller\Api\NewsController;
use App\Service\NewsService;

/**
 * 新聞控制器單元測試
 */
class NewsControllerTest extends TestCase
{
    private NewsController $controller;
    private $mockNewsService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->mockNewsService = $this->createMock(NewsService::class);
        $this->controller = new NewsController();
        
        // 注入模擬服務
        $reflection = new \ReflectionClass($this->controller);
        $property = $reflection->getProperty('newsService');
        $property->setAccessible(true);
        $property->setValue($this->controller, $this->mockNewsService);
    }

    /**
     * 測試取得新聞列表
     */
    public function testGetNewsList(): void
    {
        $expectedResult = [
            'success' => true,
            'data' => [
                [
                    'id' => 1,
                    'title' => 'AI Breakthrough',
                    'summary' => 'New AI technology...',
                    'category' => 'ai',
                    'published_at' => '2024-01-15 10:30:00'
                ],
                [
                    'id' => 2,
                    'title' => 'Blockchain Update',
                    'summary' => 'Latest blockchain news...',
                    'category' => 'blockchain',
                    'published_at' => '2024-01-14 14:20:00'
                ]
            ],
            'meta' => [
                'total' => 2
            ]
        ];

        $this->mockNewsService
            ->expects($this->once())
            ->method('getLatestNews')
            ->with(20, [], 1, ['sort_by' => 'published_at', 'sort_direction' => 'desc'])
            ->willReturn($expectedResult);

        $_GET = ['page' => '1', 'per_page' => '20'];
        $_SERVER['REQUEST_METHOD'] = 'GET';

        ob_start();
        $this->controller->index();
        $output = ob_get_clean();

        $response = json_decode($output, true);
        $this->assertTrue($response['success']);
        $this->assertCount(2, $response['data']);
        $this->assertArrayHasKey('meta', $response);
    }

    /**
     * 測試取得單一新聞詳情
     */
    public function testGetSingleNews(): void
    {
        $newsId = 1;
        $expectedResult = [
            'success' => true,
            'data' => [
                'id' => $newsId,
                'title' => 'AI Breakthrough',
                'summary' => 'New AI technology...',
                'content' => 'Full article content...',
                'category' => 'ai',
                'views' => 150,
                'published_at' => '2024-01-15 10:30:00'
            ]
        ];

        $this->mockNewsService
            ->expects($this->once())
            ->method('getNewsDetail')
            ->with($newsId, true)
            ->willReturn($expectedResult);

        $_GET = ['include_content' => 'true', 'increment_views' => 'true'];
        $_SERVER['REQUEST_METHOD'] = 'GET';

        ob_start();
        $this->controller->show($newsId);
        $output = ob_get_clean();

        $response = json_decode($output, true);
        $this->assertTrue($response['success']);
        $this->assertEquals($newsId, $response['data']['id']);
        $this->assertArrayHasKey('content', $response['data']);
    }

    /**
     * 測試新聞不存在的情況
     */
    public function testNewsNotFound(): void
    {
        $newsId = 999;
        $expectedResult = [
            'success' => false,
            'message' => '新聞不存在'
        ];

        $this->mockNewsService
            ->expects($this->once())
            ->method('getNewsDetail')
            ->with($newsId, true)
            ->willReturn($expectedResult);

        $_SERVER['REQUEST_METHOD'] = 'GET';

        ob_start();
        $this->controller->show($newsId);
        $output = ob_get_clean();

        $response = json_decode($output, true);
        $this->assertFalse($response['success']);
        $this->assertEquals('NEWS_NOT_FOUND', $response['error_code']);
    }

    /**
     * 測試取得最新新聞
     */
    public function testGetLatestNews(): void
    {
        $expectedResult = [
            'success' => true,
            'data' => [
                [
                    'id' => 1,
                    'title' => 'Latest AI News',
                    'category' => 'ai',
                    'published_at' => '2024-01-15 10:30:00'
                ]
            ]
        ];

        $this->mockNewsService
            ->expects($this->once())
            ->method('getLatestNews')
            ->with(10, ['date_from' => $this->isType('string')])
            ->willReturn($expectedResult);

        $_GET = ['limit' => '10', 'hours' => '24'];
        $_SERVER['REQUEST_METHOD'] = 'GET';

        ob_start();
        $this->controller->latest();
        $output = ob_get_clean();

        $response = json_decode($output, true);
        $this->assertTrue($response['success']);
        $this->assertArrayHasKey('news', $response['data']);
    }

    /**
     * 測試取得熱門新聞
     */
    public function testGetPopularNews(): void
    {
        $expectedResult = [
            'success' => true,
            'data' => [
                [
                    'id' => 1,
                    'title' => 'Popular AI News',
                    'views' => 1500,
                    'category' => 'ai'
                ],
                [
                    'id' => 2,
                    'title' => 'Popular Blockchain News',
                    'views' => 1200,
                    'category' => 'blockchain'
                ]
            ]
        ];

        $this->mockNewsService
            ->expects($this->once())
            ->method('getPopularNews')
            ->with(10, 7, '')
            ->willReturn($expectedResult);

        $_GET = ['limit' => '10', 'days' => '7'];
        $_SERVER['REQUEST_METHOD'] = 'GET';

        ob_start();
        $this->controller->popular();
        $output = ob_get_clean();

        $response = json_decode($output, true);
        $this->assertTrue($response['success']);
        $this->assertArrayHasKey('news', $response['data']);
    }

    /**
     * 測試取得新聞分類
     */
    public function testGetNewsCategories(): void
    {
        $expectedResult = [
            'success' => true,
            'data' => [
                ['name' => 'ai', 'label' => '人工智慧', 'count' => 50],
                ['name' => 'blockchain', 'label' => '區塊鏈', 'count' => 30],
                ['name' => 'mobile', 'label' => '行動科技', 'count' => 25]
            ]
        ];

        $this->mockNewsService
            ->expects($this->once())
            ->method('getCategories')
            ->willReturn($expectedResult);

        $_SERVER['REQUEST_METHOD'] = 'GET';

        ob_start();
        $this->controller->categories();
        $output = ob_get_clean();

        $response = json_decode($output, true);
        $this->assertTrue($response['success']);
        $this->assertCount(3, $response['data']);
    }

    /**
     * 測試取得新聞源
     */
    public function testGetNewsSources(): void
    {
        $expectedResult = [
            'success' => true,
            'data' => [
                ['id' => 1, 'name' => 'Tech News Daily', 'url' => 'https://technews.com', 'active' => true],
                ['id' => 2, 'name' => 'AI Weekly', 'url' => 'https://aiweekly.com', 'active' => true]
            ]
        ];

        $this->mockNewsService
            ->expects($this->once())
            ->method('getNewsSources')
            ->with(true)
            ->willReturn($expectedResult);

        $_GET = ['active_only' => 'true'];
        $_SERVER['REQUEST_METHOD'] = 'GET';

        ob_start();
        $this->controller->sources();
        $output = ob_get_clean();

        $response = json_decode($output, true);
        $this->assertTrue($response['success']);
        $this->assertCount(2, $response['data']);
    }

    /**
     * 測試分類新聞
     */
    public function testGetCategoryNews(): void
    {
        $category = 'ai';
        $expectedResult = [
            'success' => true,
            'data' => [
                [
                    'id' => 1,
                    'title' => 'AI News 1',
                    'category' => 'ai'
                ]
            ],
            'meta' => ['total' => 1]
        ];

        $this->mockNewsService
            ->expects($this->once())
            ->method('getLatestNews')
            ->with(20, ['category' => $category], 1, $this->isType('array'))
            ->willReturn($expectedResult);

        $_GET = ['page' => '1', 'per_page' => '20'];
        $_SERVER['REQUEST_METHOD'] = 'GET';

        ob_start();
        $this->controller->category($category);
        $output = ob_get_clean();

        $response = json_decode($output, true);
        $this->assertTrue($response['success']);
        $this->assertStringContains($category, $response['message']);
    }

    /**
     * 測試新聞源新聞
     */
    public function testGetSourceNews(): void
    {
        $sourceId = 1;
        $expectedResult = [
            'success' => true,
            'data' => [
                [
                    'id' => 1,
                    'title' => 'Source News 1',
                    'source_id' => $sourceId,
                    'source_name' => 'Tech News Daily'
                ]
            ],
            'meta' => ['total' => 1]
        ];

        $this->mockNewsService
            ->expects($this->once())
            ->method('getLatestNews')
            ->with(20, ['source_id' => $sourceId], 1, $this->isType('array'))
            ->willReturn($expectedResult);

        $_GET = ['page' => '1', 'per_page' => '20'];
        $_SERVER['REQUEST_METHOD'] = 'GET';

        ob_start();
        $this->controller->source($sourceId);
        $output = ob_get_clean();

        $response = json_decode($output, true);
        $this->assertTrue($response['success']);
    }

    /**
     * 測試新聞搜尋
     */
    public function testSearchNews(): void
    {
        $query = 'artificial intelligence';
        $expectedResult = [
            'success' => true,
            'data' => [
                [
                    'id' => 1,
                    'title' => 'AI Research Breakthrough',
                    'summary' => 'New artificial intelligence research...'
                ]
            ],
            'meta' => ['total' => 1]
        ];

        $this->mockNewsService
            ->expects($this->once())
            ->method('searchNews')
            ->with($query, ['search' => $query], 1, 20)
            ->willReturn($expectedResult);

        $_GET = ['q' => $query, 'page' => '1', 'per_page' => '20'];
        $_SERVER['REQUEST_METHOD'] = 'GET';

        ob_start();
        $this->controller->search();
        $output = ob_get_clean();

        $response = json_decode($output, true);
        $this->assertTrue($response['success']);
        $this->assertStringContains($query, $response['message']);
    }

    /**
     * 測試新聞搜尋參數驗證
     */
    public function testSearchNewsValidation(): void
    {
        // 測試空查詢
        $_GET = ['q' => ''];
        $_SERVER['REQUEST_METHOD'] = 'GET';

        ob_start();
        $this->controller->search();
        $output = ob_get_clean();

        $response = json_decode($output, true);
        $this->assertFalse($response['success']);
        $this->assertEquals('MISSING_QUERY', $response['error_code']);
    }

    /**
     * 測試新聞統計
     */
    public function testGetNewsStats(): void
    {
        $expectedResult = [
            'success' => true,
            'data' => [
                'total_articles' => 500,
                'categories' => [
                    'ai' => 150,
                    'blockchain' => 100,
                    'mobile' => 80
                ],
                'sources' => [
                    'Tech News Daily' => 200,
                    'AI Weekly' => 150
                ],
                'avg_views_per_article' => 250.5
            ]
        ];

        $this->mockNewsService
            ->expects($this->once())
            ->method('getNewsStatistics')
            ->with(30)
            ->willReturn($expectedResult);

        $_GET = ['days' => '30'];
        $_SERVER['REQUEST_METHOD'] = 'GET';

        ob_start();
        $this->controller->stats();
        $output = ob_get_clean();

        $response = json_decode($output, true);
        $this->assertTrue($response['success']);
        $this->assertEquals(500, $response['data']['statistics']['total_articles']);
    }

    /**
     * 測試錯誤處理
     */
    public function testErrorHandling(): void
    {
        // 模擬服務拋出異常
        $this->mockNewsService
            ->expects($this->once())
            ->method('getLatestNews')
            ->willThrowException(new \Exception('Database connection failed'));

        $_GET = ['limit' => '10'];
        $_SERVER['REQUEST_METHOD'] = 'GET';

        ob_start();
        $this->controller->latest();
        $output = ob_get_clean();

        $response = json_decode($output, true);
        $this->assertFalse($response['success']);
        $this->assertEquals('LATEST_NEWS_SERVICE_ERROR', $response['error_code']);
    }

    /**
     * 測試分頁參數處理
     */
    public function testPaginationParameters(): void
    {
        $expectedResult = [
            'success' => true,
            'data' => [],
            'meta' => ['total' => 0]
        ];

        $this->mockNewsService
            ->expects($this->once())
            ->method('getLatestNews')
            ->with(50, [], 3, ['sort_by' => 'views', 'sort_direction' => 'desc'])
            ->willReturn($expectedResult);

        $_GET = [
            'page' => '3',
            'per_page' => '50',
            'sort_by' => 'views',
            'sort_direction' => 'desc'
        ];
        $_SERVER['REQUEST_METHOD'] = 'GET';

        ob_start();
        $this->controller->index();
        $output = ob_get_clean();

        $response = json_decode($output, true);
        $this->assertTrue($response['success']);
    }

    /**
     * 測試篩選參數處理
     */
    public function testFilterParameters(): void
    {
        $expectedResult = [
            'success' => true,
            'data' => [],
            'meta' => ['total' => 0]
        ];

        $this->mockNewsService
            ->expects($this->once())
            ->method('getLatestNews')
            ->with(20, [
                'category' => 'ai',
                'source_id' => '1',
                'date_from' => '2024-01-01',
                'date_to' => '2024-01-31'
            ], 1, $this->isType('array'))
            ->willReturn($expectedResult);

        $_GET = [
            'category' => 'ai',
            'source_id' => '1',
            'date_from' => '2024-01-01',
            'date_to' => '2024-01-31'
        ];
        $_SERVER['REQUEST_METHOD'] = 'GET';

        ob_start();
        $this->controller->index();
        $output = ob_get_clean();

        $response = json_decode($output, true);
        $this->assertTrue($response['success']);
    }

    protected function tearDown(): void
    {
        unset($_GET, $_SERVER['REQUEST_METHOD']);
        parent::tearDown();
    }
}