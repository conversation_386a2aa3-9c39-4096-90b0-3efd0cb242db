<?php

namespace App\Middleware;

/**
 * 管理員中間件
 * 檢查用戶是否具有管理員權限
 */
class AdminMiddleware implements MiddlewareInterface
{
    /**
     * 處理請求
     * 
     * @param callable $next
     * @return mixed
     */
    public function handle(callable $next)
    {
        // 檢查會話是否已啟動
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // 檢查用戶是否已登入
        if (!isset($_SESSION['user_id']) || empty($_SESSION['user_id'])) {
            // 返回 403 錯誤
            http_response_code(403);
            header('Location: /error.php?code=403');
            exit;
        }

        // 檢查用戶是否具有管理員權限
        if (!isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
            // 記錄未授權訪問嘗試
            $this->logUnauthorizedAccess();
            
            // 返回 403 錯誤
            http_response_code(403);
            header('Location: /error.php?code=403');
            exit;
        }

        // 繼續處理請求
        return $next();
    }

    /**
     * 記錄未授權訪問嘗試
     */
    private function logUnauthorizedAccess(): void
    {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'event' => 'unauthorized_admin_access',
            'user_id' => $_SESSION['user_id'] ?? null,
            'user_email' => $_SESSION['user_email'] ?? null,
            'request_uri' => $_SERVER['REQUEST_URI'] ?? '',
            'ip' => $_SERVER['REMOTE_ADDR'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ];

        $logFile = APP_ROOT . '/storage/logs/security.log';
        $logDir = dirname($logFile);
        
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }

        file_put_contents($logFile, json_encode($logData, JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND | LOCK_EX);
    }
}