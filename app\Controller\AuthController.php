<?php

namespace App\Controller;

/**
 * 認證控制器
 * 處理用戶登入、註冊等認證相關功能
 */
class AuthController extends BaseController
{
    /**
     * 顯示登入表單
     */
    public function loginForm(): void
    {
        $this->setViewData('pageTitle', '登入 - Tech.VG 科技新知檢索');
        $this->setViewData('currentPage', 'login');
        
        $this->render('auth/login');
    }

    /**
     * 處理登入
     */
    public function login(): void
    {
        try {
            // 驗證 CSRF 令牌
            if (!$this->validateCsrfToken()) {
                throw new \Exception('無效的請求');
            }

            $email = $this->getPost('email');
            $password = $this->getPost('password');
            $remember = $this->getPost('remember') === '1';

            // 驗證必要欄位
            if (empty($email) || empty($password)) {
                throw new \Exception('請輸入電子郵件和密碼');
            }

            // 這裡應該實作實際的用戶驗證邏輯
            // 目前只是示例實作
            if ($email === '<EMAIL>' && $password === 'admin123') {
                // 設定會話
                $_SESSION['user_id'] = 1;
                $_SESSION['user_email'] = $email;
                $_SESSION['user_name'] = 'Administrator';
                $_SESSION['is_admin'] = true;
                $_SESSION['login_time'] = time();

                // 記錄登入日誌
                $this->logUserAction('login', [
                    'email' => $email,
                    'ip' => $_SERVER['REMOTE_ADDR'] ?? '',
                    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
                ]);

                flash('success', '登入成功！歡迎回來。');
                
                // 重定向到管理後台或原來的頁面
                $redirectTo = $_SESSION['intended_url'] ?? '/admin';
                unset($_SESSION['intended_url']);
                $this->redirect($redirectTo);
            } else {
                throw new \Exception('電子郵件或密碼錯誤');
            }

        } catch (\Exception $e) {
            flash('error', $e->getMessage());
            
            // 保存舊的輸入值（除了密碼）
            $_SESSION['old_input'] = [
                'email' => $this->getPost('email')
            ];
            
            $this->redirect('/auth/login');
        }
    }

    /**
     * 顯示註冊表單
     */
    public function registerForm(): void
    {
        $this->setViewData('pageTitle', '註冊 - Tech.VG 科技新知檢索');
        $this->setViewData('currentPage', 'register');
        
        $this->render('auth/register');
    }

    /**
     * 處理註冊
     */
    public function register(): void
    {
        try {
            // 驗證 CSRF 令牌
            if (!$this->validateCsrfToken()) {
                throw new \Exception('無效的請求');
            }

            $name = $this->getPost('name');
            $email = $this->getPost('email');
            $password = $this->getPost('password');
            $confirmPassword = $this->getPost('confirm_password');

            // 驗證必要欄位
            if (empty($name) || empty($email) || empty($password) || empty($confirmPassword)) {
                throw new \Exception('請填寫所有必要欄位');
            }

            // 驗證電子郵件格式
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                throw new \Exception('請輸入有效的電子郵件地址');
            }

            // 驗證密碼
            if (strlen($password) < 8) {
                throw new \Exception('密碼長度至少需要8個字元');
            }

            if ($password !== $confirmPassword) {
                throw new \Exception('密碼確認不符');
            }

            // 這裡應該實作實際的用戶註冊邏輯
            // 目前只是示例實作
            
            // 記錄註冊嘗試
            $this->logUserAction('register_attempt', [
                'name' => $name,
                'email' => $email,
                'ip' => $_SERVER['REMOTE_ADDR'] ?? '',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);

            flash('success', '註冊成功！請登入您的帳戶。');
            $this->redirect('/auth/login');

        } catch (\Exception $e) {
            flash('error', $e->getMessage());
            
            // 保存舊的輸入值（除了密碼）
            $_SESSION['old_input'] = [
                'name' => $this->getPost('name'),
                'email' => $this->getPost('email')
            ];
            
            $this->redirect('/auth/register');
        }
    }

    /**
     * 登出
     */
    public function logout(): void
    {
        // 記錄登出日誌
        if (isset($_SESSION['user_id'])) {
            $this->logUserAction('logout', [
                'user_id' => $_SESSION['user_id'],
                'ip' => $_SERVER['REMOTE_ADDR'] ?? ''
            ]);
        }

        // 清除會話
        session_destroy();
        
        flash('success', '您已成功登出。');
        $this->redirect('/');
    }

    /**
     * 顯示忘記密碼表單
     */
    public function forgotPasswordForm(): void
    {
        $this->setViewData('pageTitle', '忘記密碼 - Tech.VG 科技新知檢索');
        $this->setViewData('currentPage', 'forgot-password');
        
        $this->render('auth/forgot-password');
    }

    /**
     * 處理忘記密碼
     */
    public function forgotPassword(): void
    {
        try {
            // 驗證 CSRF 令牌
            if (!$this->validateCsrfToken()) {
                throw new \Exception('無效的請求');
            }

            $email = $this->getPost('email');

            // 驗證必要欄位
            if (empty($email)) {
                throw new \Exception('請輸入電子郵件地址');
            }

            // 驗證電子郵件格式
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                throw new \Exception('請輸入有效的電子郵件地址');
            }

            // 這裡應該實作實際的密碼重設邏輯
            // 目前只是示例實作
            
            // 記錄密碼重設請求
            $this->logUserAction('password_reset_request', [
                'email' => $email,
                'ip' => $_SERVER['REMOTE_ADDR'] ?? '',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);

            flash('success', '如果該電子郵件地址存在於我們的系統中，您將收到密碼重設指示。');
            $this->redirect('/auth/login');

        } catch (\Exception $e) {
            flash('error', $e->getMessage());
            
            // 保存舊的輸入值
            $_SESSION['old_input'] = [
                'email' => $this->getPost('email')
            ];
            
            $this->redirect('/auth/forgot-password');
        }
    }

    /**
     * 記錄用戶操作
     */
    private function logUserAction(string $action, array $data): void
    {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'action' => $action,
            'data' => $data
        ];

        $logFile = APP_ROOT . '/storage/logs/auth.log';
        $logDir = dirname($logFile);
        
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }

        file_put_contents($logFile, json_encode($logData, JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND | LOCK_EX);
    }
}