<?php

namespace Tests\Unit\Controller\Api;

use Tests\TestCase;
use App\Controller\Api\DocumentController;
use App\Service\DocumentService;

/**
 * 文檔控制器單元測試
 */
class DocumentControllerTest extends TestCase
{
    private DocumentController $controller;
    private $mockDocumentService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->mockDocumentService = $this->createMock(DocumentService::class);
        $this->controller = new DocumentController();
        
        // 注入模擬服務
        $reflection = new \ReflectionClass($this->controller);
        $property = $reflection->getProperty('documentService');
        $property->setAccessible(true);
        $property->setValue($this->controller, $this->mockDocumentService);
    }

    /**
     * 測試取得文檔列表
     */
    public function testGetDocumentsList(): void
    {
        $expectedResult = [
            'success' => true,
            'data' => [
                [
                    'id' => 1,
                    'title' => 'Test Document 1',
                    'filename' => 'test1.txt',
                    'file_type' => 'txt',
                    'status' => 'active'
                ],
                [
                    'id' => 2,
                    'title' => 'Test Document 2',
                    'filename' => 'test2.md',
                    'file_type' => 'md',
                    'status' => 'active'
                ]
            ],
            'meta' => [
                'total' => 2
            ]
        ];

        $this->mockDocumentService
            ->expects($this->once())
            ->method('getDocuments')
            ->with([], 1, 20, ['sort_by' => 'created_at', 'sort_direction' => 'desc'])
            ->willReturn($expectedResult);

        $_GET = ['page' => '1', 'per_page' => '20'];
        $_SERVER['REQUEST_METHOD'] = 'GET';

        ob_start();
        $this->controller->index();
        $output = ob_get_clean();

        $response = json_decode($output, true);
        $this->assertTrue($response['success']);
        $this->assertCount(2, $response['data']);
        $this->assertArrayHasKey('meta', $response);
    }

    /**
     * 測試取得單一文檔
     */
    public function testGetSingleDocument(): void
    {
        $documentId = 1;
        $expectedResult = [
            'success' => true,
            'data' => [
                'id' => $documentId,
                'title' => 'Test Document',
                'filename' => 'test.txt',
                'file_type' => 'txt',
                'content' => 'Test content',
                'status' => 'active'
            ]
        ];

        $this->mockDocumentService
            ->expects($this->once())
            ->method('getDocument')
            ->with($documentId, true)
            ->willReturn($expectedResult);

        $_GET = ['include_content' => 'true'];
        $_SERVER['REQUEST_METHOD'] = 'GET';

        ob_start();
        $this->controller->show($documentId);
        $output = ob_get_clean();

        $response = json_decode($output, true);
        $this->assertTrue($response['success']);
        $this->assertEquals($documentId, $response['data']['id']);
        $this->assertArrayHasKey('content', $response['data']);
    }

    /**
     * 測試文檔不存在的情況
     */
    public function testDocumentNotFound(): void
    {
        $documentId = 999;
        $expectedResult = [
            'success' => false,
            'message' => '文檔不存在'
        ];

        $this->mockDocumentService
            ->expects($this->once())
            ->method('getDocument')
            ->with($documentId, false)
            ->willReturn($expectedResult);

        $_SERVER['REQUEST_METHOD'] = 'GET';

        ob_start();
        $this->controller->show($documentId);
        $output = ob_get_clean();

        $response = json_decode($output, true);
        $this->assertFalse($response['success']);
        $this->assertEquals('DOCUMENT_NOT_FOUND', $response['error_code']);
    }

    /**
     * 測試文檔上傳
     */
    public function testDocumentUpload(): void
    {
        $uploadResult = [
            'success' => true,
            'data' => [
                'document_id' => 1
            ]
        ];

        $documentResult = [
            'success' => true,
            'data' => [
                'id' => 1,
                'title' => 'Uploaded Document',
                'filename' => 'upload.txt',
                'file_type' => 'txt',
                'status' => 'active'
            ]
        ];

        // 模擬檔案上傳
        $_FILES = [
            'document' => [
                'name' => 'test.txt',
                'type' => 'text/plain',
                'tmp_name' => $this->createTestFile('test.txt', 'Test content'),
                'error' => UPLOAD_ERR_OK,
                'size' => 12
            ]
        ];

        $_POST = [
            'title' => 'Uploaded Document',
            'description' => 'Test description'
        ];

        $_SERVER['REQUEST_METHOD'] = 'POST';

        $this->mockDocumentService
            ->expects($this->once())
            ->method('uploadDocument')
            ->willReturn($uploadResult);

        $this->mockDocumentService
            ->expects($this->once())
            ->method('getDocument')
            ->with(1, false)
            ->willReturn($documentResult);

        ob_start();
        $this->controller->store();
        $output = ob_get_clean();

        $response = json_decode($output, true);
        $this->assertTrue($response['success']);
        $this->assertEquals(1, $response['data']['id']);
    }

    /**
     * 測試文檔上傳驗證失敗
     */
    public function testDocumentUploadValidationFailure(): void
    {
        // 沒有上傳檔案
        $_FILES = [];
        $_SERVER['REQUEST_METHOD'] = 'POST';

        ob_start();
        $this->controller->store();
        $output = ob_get_clean();

        $response = json_decode($output, true);
        $this->assertFalse($response['success']);
        $this->assertEquals('NO_FILE_UPLOADED', $response['error_code']);
    }

    /**
     * 測試文檔更新
     */
    public function testDocumentUpdate(): void
    {
        $documentId = 1;
        $updateData = [
            'title' => 'Updated Title',
            'description' => 'Updated description',
            'status' => 'active'
        ];

        $existingResult = [
            'success' => true,
            'data' => ['id' => $documentId]
        ];

        $updateResult = [
            'success' => true
        ];

        $updatedResult = [
            'success' => true,
            'data' => array_merge(['id' => $documentId], $updateData)
        ];

        $_SERVER['REQUEST_METHOD'] = 'PUT';
        $_SERVER['CONTENT_TYPE'] = 'application/json';
        $this->mockRequestBody(json_encode($updateData));

        $this->mockDocumentService
            ->expects($this->once())
            ->method('getDocument')
            ->with($documentId, false)
            ->willReturn($existingResult);

        $this->mockDocumentService
            ->expects($this->once())
            ->method('updateDocument')
            ->with($documentId, $updateData)
            ->willReturn($updateResult);

        $this->mockDocumentService
            ->expects($this->once())
            ->method('getDocument')
            ->with($documentId, false)
            ->willReturn($updatedResult);

        ob_start();
        $this->controller->update($documentId);
        $output = ob_get_clean();

        $response = json_decode($output, true);
        $this->assertTrue($response['success']);
        $this->assertEquals('Updated Title', $response['data']['title']);
    }

    /**
     * 測試文檔刪除
     */
    public function testDocumentDelete(): void
    {
        $documentId = 1;

        $existingResult = [
            'success' => true,
            'data' => ['id' => $documentId]
        ];

        $deleteResult = [
            'success' => true
        ];

        $_SERVER['REQUEST_METHOD'] = 'DELETE';

        $this->mockDocumentService
            ->expects($this->once())
            ->method('getDocument')
            ->with($documentId, false)
            ->willReturn($existingResult);

        $this->mockDocumentService
            ->expects($this->once())
            ->method('deleteDocument')
            ->with($documentId)
            ->willReturn($deleteResult);

        ob_start();
        $this->controller->destroy($documentId);
        $output = ob_get_clean();

        $response = json_decode($output, true);
        $this->assertTrue($response['success']);
        $this->assertEquals('文檔刪除成功', $response['message']);
    }

    /**
     * 測試重建索引
     */
    public function testReindexDocument(): void
    {
        $documentId = 1;

        $existingResult = [
            'success' => true,
            'data' => ['id' => $documentId]
        ];

        $reindexResult = [
            'success' => true
        ];

        $_SERVER['REQUEST_METHOD'] = 'POST';

        $this->mockDocumentService
            ->expects($this->once())
            ->method('getDocument')
            ->with($documentId, false)
            ->willReturn($existingResult);

        $this->mockDocumentService
            ->expects($this->once())
            ->method('reindexDocument')
            ->with($documentId)
            ->willReturn($reindexResult);

        ob_start();
        $this->controller->reindex($documentId);
        $output = ob_get_clean();

        $response = json_decode($output, true);
        $this->assertTrue($response['success']);
        $this->assertEquals('索引重建成功', $response['message']);
    }

    /**
     * 測試批量操作
     */
    public function testBatchOperation(): void
    {
        $batchData = [
            'action' => 'update_status',
            'document_ids' => [1, 2, 3],
            'data' => ['status' => 'active']
        ];

        $_SERVER['REQUEST_METHOD'] = 'POST';
        $_SERVER['CONTENT_TYPE'] = 'application/json';
        $this->mockRequestBody(json_encode($batchData));

        // 模擬批量操作結果
        $this->mockDocumentService
            ->expects($this->exactly(3))
            ->method('updateDocument')
            ->willReturn(['success' => true]);

        ob_start();
        $this->controller->batch();
        $output = ob_get_clean();

        $response = json_decode($output, true);
        $this->assertTrue($response['success']);
        $this->assertEquals(3, $response['data']['total_processed']);
        $this->assertEquals(3, $response['data']['success_count']);
        $this->assertEquals(0, $response['data']['error_count']);
    }

    /**
     * 測試文檔統計
     */
    public function testDocumentStats(): void
    {
        $expectedStats = [
            'success' => true,
            'data' => [
                'total' => 100,
                'active' => 85,
                'inactive' => 10,
                'processing' => 5,
                'total_size' => 1024000,
                'indexed' => 90
            ]
        ];

        $this->mockDocumentService
            ->expects($this->once())
            ->method('getStatistics')
            ->willReturn($expectedStats);

        $_SERVER['REQUEST_METHOD'] = 'GET';

        ob_start();
        $this->controller->stats();
        $output = ob_get_clean();

        $response = json_decode($output, true);
        $this->assertTrue($response['success']);
        $this->assertEquals(100, $response['data']['total']);
        $this->assertEquals(85, $response['data']['active']);
    }

    /**
     * 測試檔案類型驗證
     */
    public function testFileTypeValidation(): void
    {
        // 測試不支援的檔案類型
        $_FILES = [
            'document' => [
                'name' => 'test.pdf',
                'type' => 'application/pdf',
                'tmp_name' => $this->createTestFile('test.pdf', 'PDF content'),
                'error' => UPLOAD_ERR_OK,
                'size' => 1024
            ]
        ];

        $_POST = ['title' => 'Test PDF'];
        $_SERVER['REQUEST_METHOD'] = 'POST';

        ob_start();
        $this->controller->store();
        $output = ob_get_clean();

        $response = json_decode($output, true);
        $this->assertFalse($response['success']);
        $this->assertEquals('FILE_VALIDATION_ERROR', $response['error_code']);
    }

    /**
     * 測試檔案大小限制
     */
    public function testFileSizeLimit(): void
    {
        // 建立超過大小限制的檔案
        $largeContent = str_repeat('A', 11 * 1024 * 1024); // 11MB
        
        $_FILES = [
            'document' => [
                'name' => 'large.txt',
                'type' => 'text/plain',
                'tmp_name' => $this->createTestFile('large.txt', $largeContent),
                'error' => UPLOAD_ERR_OK,
                'size' => strlen($largeContent)
            ]
        ];

        $_POST = ['title' => 'Large File'];
        $_SERVER['REQUEST_METHOD'] = 'POST';

        ob_start();
        $this->controller->store();
        $output = ob_get_clean();

        $response = json_decode($output, true);
        $this->assertFalse($response['success']);
        $this->assertEquals('FILE_VALIDATION_ERROR', $response['error_code']);
    }

    /**
     * 模擬請求體
     */
    private function mockRequestBody(string $body): void
    {
        $GLOBALS['mock_request_body'] = $body;
    }

    protected function tearDown(): void
    {
        unset($_GET, $_POST, $_FILES, $_SERVER['REQUEST_METHOD'], $_SERVER['CONTENT_TYPE']);
        unset($GLOBALS['mock_request_body']);
        
        parent::tearDown();
    }
}