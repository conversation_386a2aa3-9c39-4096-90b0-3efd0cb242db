<?php

namespace App\Controller;

/**
 * 基礎控制器類別
 * 提供所有控制器的共用功能
 */
abstract class BaseController
{
    /**
     * 視圖資料
     * @var array
     */
    protected array $viewData = [];

    /**
     * 設定視圖資料
     * 
     * @param string $key 資料鍵名
     * @param mixed $value 資料值
     * @return void
     */
    protected function setViewData(string $key, $value): void
    {
        $this->viewData[$key] = $value;
    }

    /**
     * 取得視圖資料
     * 
     * @param string|null $key 資料鍵名，為null時返回所有資料
     * @return mixed
     */
    protected function getViewData(?string $key = null)
    {
        if ($key === null) {
            return $this->viewData;
        }
        
        return $this->viewData[$key] ?? null;
    }

    /**
     * 渲染視圖
     * 
     * @param string $viewName 視圖名稱
     * @param array $data 額外資料
     * @return void
     */
    protected function render(string $viewName, array $data = []): void
    {
        // 載入視圖輔助函數
        require_once __DIR__ . '/../View/helpers.php';
        
        // 合併視圖資料
        $viewData = array_merge($this->viewData, $data);
        
        // 提取變數到當前作用域
        extract($viewData);
        
        // 載入視圖文件
        $viewPath = __DIR__ . '/../View/' . $viewName . '.php';
        
        if (!file_exists($viewPath)) {
            throw new \Exception("視圖文件不存在: {$viewName}");
        }
        
        include $viewPath;
    }
    
    /**
     * 格式化位元組大小
     * 
     * @param int $bytes
     * @return string
     */
    protected function formatBytes(int $bytes): string
    {
        if ($bytes === 0) return '0 Bytes';
        
        $k = 1024;
        $sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        $i = floor(log($bytes) / log($k));
        
        return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
    }
    
    /**
     * 高亮搜尋關鍵字
     * 
     * @param string $text 原始文字
     * @param string $query 搜尋關鍵字
     * @return string 高亮後的文字
     */
    protected function highlightSearchTerms(string $text, string $query): string
    {
        if (empty($query) || empty($text)) {
            return htmlspecialchars($text);
        }
        
        $text = htmlspecialchars($text);
        $keywords = explode(' ', $query);
        
        foreach ($keywords as $keyword) {
            $keyword = trim($keyword);
            if (!empty($keyword)) {
                $text = preg_replace(
                    '/(' . preg_quote($keyword, '/') . ')/iu',
                    '<mark class="bg-yellow-200 px-1 rounded">$1</mark>',
                    $text
                );
            }
        }
        
        return $text;
    }

    /**
     * 返回JSON響應
     * 
     * @param array $data 響應資料
     * @param int $statusCode HTTP狀態碼
     * @return void
     */
    protected function jsonResponse(array $data, int $statusCode = 200): void
    {
        http_response_code($statusCode);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }

    /**
     * 重定向到指定URL
     * 
     * @param string $url 目標URL
     * @return void
     */
    protected function redirect(string $url): void
    {
        header("Location: {$url}");
        exit;
    }

    /**
     * 取得POST資料
     * 
     * @param string|null $key 資料鍵名
     * @param mixed $default 預設值
     * @return mixed
     */
    protected function getPost(?string $key = null, $default = null)
    {
        if ($key === null) {
            return $_POST;
        }
        
        return $_POST[$key] ?? $default;
    }

    /**
     * 取得GET資料
     * 
     * @param string|null $key 資料鍵名
     * @param mixed $default 預設值
     * @return mixed
     */
    protected function getGet(?string $key = null, $default = null)
    {
        if ($key === null) {
            return $_GET;
        }
        
        return $_GET[$key] ?? $default;
    }

    /**
     * 驗證CSRF令牌
     * 
     * @return bool
     */
    protected function validateCsrfToken(): bool
    {
        $token = $this->getPost('csrf_token');
        
        if (empty($token)) {
            return false;
        }
        
        return hash_equals($_SESSION['csrf_token'] ?? '', $token);
    }

    /**
     * 生成CSRF令牌
     * 
     * @return string
     */
    protected function generateCsrfToken(): string
    {
        try {
            if (session_status() === PHP_SESSION_NONE) {
                session_start();
            }
            
            $token = bin2hex(random_bytes(32));
            $_SESSION['csrf_token'] = $token;
            
            return $token;
        } catch (\Exception $e) {
            // 如果無法生成隨機字節或啟動會話，則使用替代方法
            $this->logError('無法生成CSRF令牌', ['error' => $e->getMessage()]);
            return md5(uniqid(mt_rand(), true));
        }
    }

    /**
     * 驗證檔案上傳
     * 
     * @param array $file $_FILES中的檔案資料
     * @param array $allowedTypes 允許的檔案類型
     * @param int $maxSize 最大檔案大小(位元組)
     * @return array 驗證結果
     */
    protected function validateFileUpload(array $file, array $allowedTypes = ['txt', 'md'], int $maxSize = 10485760): array
    {
        $result = [
            'valid' => false,
            'error' => null
        ];

        // 檢查是否有錯誤
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $result['error'] = '檔案上傳失敗';
            return $result;
        }

        // 檢查檔案大小
        if ($file['size'] > $maxSize) {
            $result['error'] = '檔案大小超過限制';
            return $result;
        }

        // 檢查檔案類型
        $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($fileExtension, $allowedTypes)) {
            $result['error'] = '不支援的檔案類型';
            return $result;
        }

        $result['valid'] = true;
        return $result;
    }

    /**
     * 記錄錯誤日誌
     * 
     * @param string $message 錯誤訊息
     * @param array $context 上下文資料
     * @return void
     */
    protected function logError(string $message, array $context = []): void
    {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'message' => $message,
            'context' => $context,
            'request_uri' => $_SERVER['REQUEST_URI'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ];

        $logFile = __DIR__ . '/../../storage/logs/error.log';
        $logDir = dirname($logFile);
        
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }

        file_put_contents($logFile, json_encode($logData, JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND | LOCK_EX);
    }
}