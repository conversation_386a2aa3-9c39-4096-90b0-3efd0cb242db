<?php
/**
 * 檢索頁面 - 極簡版本
 */

// 確保所有變量都有默認值，防止未定義錯誤
$query = $query ?? '';
$pageTitle = $pageTitle ?? '檢索 - Tech.VG';
?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($pageTitle) ?></title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background-color: white; padding: 20px; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        h1 { color: #333; }
        .search-form { margin-bottom: 20px; }
        .search-input { padding: 10px; width: 70%; font-size: 16px; border: 1px solid #ddd; border-radius: 4px; }
        .search-button { padding: 10px 20px; background-color: #4285f4; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; }
        .search-button:hover { background-color: #3367d6; }
        .error { color: red; padding: 10px; margin-bottom: 20px; border: 1px solid red; background-color: #ffeeee; }
        footer { margin-top: 20px; text-align: center; font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <h1>檢索系統</h1>
        
        <!-- 搜尋表單 -->
        <div class="search-form">
            <form action="/search" method="GET">
                <input type="text" 
                       name="q" 
                       value="<?= htmlspecialchars($query) ?>"
                       class="search-input"
                       placeholder="輸入關鍵字搜尋..."
                       autocomplete="off">
                <button type="submit" class="search-button">搜尋</button>
            </form>
        </div>
        
        <p>請輸入關鍵字進行檢索。</p>
        
        <footer>
            <p>© 2024 Tech.VG 科技新知檢索</p>
        </footer>
    </div>
</body>
</html>