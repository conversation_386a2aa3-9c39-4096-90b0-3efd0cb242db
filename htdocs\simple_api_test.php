<?php
/**
 * 簡單的API測試 - 直接執行路由中的Closure
 */

// 設定錯誤報告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 設定內容類型
header('Content-Type: application/json; charset=utf-8');

try {
    // 直接執行API根路由的Closure內容
    echo json_encode([
        'name' => 'Tech.VG API',
        'version' => 'v1',
        'status' => 'active',
        'timestamp' => date('c'),
        'endpoints' => [
            'search' => '/api/search',
            'documents' => '/api/documents',
            'news' => '/api/news',
            'stats' => '/api/stats'
        ],
        'documentation' => [
            'interactive_docs' => '/api/docs?format=html',
            'openapi_spec' => '/api/docs/openapi',
            'code_examples' => '/api/docs/code',
            'changelog' => '/api/changelog?format=html'
        ],
        'tools' => [
            'api_tester' => '/api/test?format=html',
            'system_status' => '/api/status?format=html',
            'health_check' => '/api/health'
        ],
        'features' => [
            'rate_limiting' => true,
            'caching' => true,
            'pagination' => true,
            'search_suggestions' => true,
            'batch_operations' => true,
            'real_time_stats' => true,
            'file_upload' => true,
            'code_generation' => true
        ]
    ], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'timestamp' => date('c')
    ], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
}
?>
