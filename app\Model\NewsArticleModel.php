<?php

namespace App\Model;

/**
 * 新聞文章模型
 * 處理新聞文章相關的資料操作
 */
class NewsArticleModel extends BaseModel
{
    protected string $table = 'newsArticle';
    
    protected array $fillable = [
        'sourceId',
        'title',
        'summary',
        'content',
        'author',
        'originalUrl',
        'imageUrl',
        'publishedAt',
        'crawledAt',
        'contentHash',
        'isIndexed',
        'viewCount'
    ];
    
    protected array $casts = [
        'id' => 'int',
        'sourceId' => 'int',
        'isIndexed' => 'bool',
        'viewCount' => 'int',
        'publishedAt' => 'datetime',
        'crawledAt' => 'datetime',
        'createdAt' => 'datetime',
        'updatedAt' => 'datetime'
    ];

    /**
     * 根據新聞來源查找文章
     * 
     * @param int $sourceId
     * @param int $limit
     * @return array
     */
    public function findBySource(int $sourceId, int $limit = 50): array
    {
        return $this->findAll(['sourceId' => $sourceId], 'publishedAt DESC', $limit);
    }

    /**
     * 取得最新文章
     * 
     * @param int $limit
     * @return array
     */
    public function getLatest(int $limit = 20): array
    {
        return $this->findAll([], 'publishedAt DESC', $limit);
    }

    /**
     * 搜尋新聞文章
     * 
     * @param string $keyword
     * @param int $limit
     * @return array
     */
    public function searchNews(string $keyword, int $limit = 50): array
    {
        $sql = "
            SELECT na.*, ns.name as source_name
            FROM {$this->table} na
            JOIN newsSource ns ON na.sourceId = ns.id
            WHERE MATCH(na.title, na.content, na.summary) AGAINST(? IN NATURAL LANGUAGE MODE)
            ORDER BY MATCH(na.title, na.content, na.summary) AGAINST(? IN NATURAL LANGUAGE MODE) DESC
            LIMIT ?
        ";
        
        return $this->db->fetchAll($sql, [$keyword, $keyword, $limit]);
    }

    /**
     * 根據發布日期範圍查找文章
     * 
     * @param string $startDate
     * @param string $endDate
     * @param int $limit
     * @return array
     */
    public function findByDateRange(string $startDate, string $endDate, int $limit = 100): array
    {
        $conditions = [
            'publishedAt >=' => $startDate,
            'publishedAt <=' => $endDate
        ];
        
        return $this->findAll($conditions, 'publishedAt DESC', $limit);
    }

    /**
     * 取得熱門文章 (根據瀏覽次數)
     * 
     * @param int $limit
     * @param int $days 天數範圍
     * @return array
     */
    public function getPopular(int $limit = 10, int $days = 7): array
    {
        $startDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        
        $sql = "
            SELECT na.*, ns.name as source_name
            FROM {$this->table} na
            JOIN newsSource ns ON na.sourceId = ns.id
            WHERE na.publishedAt >= ?
            ORDER BY na.viewCount DESC, na.publishedAt DESC
            LIMIT ?
        ";
        
        return $this->db->fetchAll($sql, [$startDate, $limit]);
    }

    /**
     * 增加瀏覽次數
     * 
     * @param int $id
     * @return int
     */
    public function incrementViewCount(int $id): int
    {
        $sql = "UPDATE {$this->table} SET viewCount = viewCount + 1 WHERE id = ?";
        return $this->db->update($sql, [$id]);
    }

    /**
     * 根據內容雜湊檢查重複
     * 
     * @param string $contentHash
     * @return array|null
     */
    public function findByContentHash(string $contentHash): ?array
    {
        return $this->findFirst(['contentHash' => $contentHash]);
    }

    /**
     * 根據原始URL檢查重複
     * 
     * @param string $originalUrl
     * @return array|null
     */
    public function findByOriginalUrl(string $originalUrl): ?array
    {
        return $this->findFirst(['originalUrl' => $originalUrl]);
    }

    /**
     * 取得未建立索引的文章
     * 
     * @param int $limit
     * @return array
     */
    public function getUnindexed(int $limit = 100): array
    {
        return $this->findAll(['isIndexed' => 0], 'crawledAt ASC', $limit);
    }

    /**
     * 標記為已建立索引
     * 
     * @param int $id
     * @return int
     */
    public function markAsIndexed(int $id): int
    {
        return $this->update($id, ['isIndexed' => 1]);
    }

    /**
     * 取得新聞統計資訊
     * 
     * @return array
     */
    public function getStatistics(): array
    {
        $sql = "
            SELECT 
                COUNT(*) as total_articles,
                COUNT(CASE WHEN isIndexed = 1 THEN 1 END) as indexed_articles,
                COUNT(CASE WHEN publishedAt >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as articles_24h,
                COUNT(CASE WHEN publishedAt >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as articles_7d,
                SUM(viewCount) as total_views,
                AVG(viewCount) as avg_views
            FROM {$this->table}
        ";
        
        return $this->db->fetchOne($sql) ?: [];
    }

    /**
     * 根據來源取得統計
     * 
     * @return array
     */
    public function getStatisticsBySource(): array
    {
        $sql = "
            SELECT 
                ns.name as source_name,
                ns.id as source_id,
                COUNT(na.id) as article_count,
                SUM(na.viewCount) as total_views,
                MAX(na.publishedAt) as latest_article
            FROM newsSource ns
            LEFT JOIN {$this->table} na ON ns.id = na.sourceId
            GROUP BY ns.id, ns.name
            ORDER BY article_count DESC
        ";
        
        return $this->db->fetchAll($sql);
    }

    /**
     * 清理舊文章
     * 
     * @param int $days 保留天數
     * @return int 刪除的文章數量
     */
    public function cleanupOldArticles(int $days = 90): int
    {
        $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        
        return $this->deleteWhere(['publishedAt <' => $cutoffDate]);
    }
}