<?php

namespace App\Middleware;

/**
 * 維護模式中間件
 * 檢查網站是否處於維護模式
 */
class MaintenanceMiddleware implements MiddlewareInterface
{
    /**
     * 處理請求
     * 
     * @return void
     * @throws \Exception
     */
    public function handle(): void
    {
        // 檢查維護模式文件是否存在
        $maintenanceFile = __DIR__ . '/../../storage/maintenance.json';
        
        if (file_exists($maintenanceFile)) {
            // 讀取維護模式配置
            $maintenanceConfig = json_decode(file_get_contents($maintenanceFile), true);
            
            // 檢查是否允許當前IP訪問
            if ($this->isAllowedIp($maintenanceConfig['allowed_ips'] ?? [])) {
                return;
            }
            
            // 檢查維護模式是否已過期
            if (isset($maintenanceConfig['until']) && time() > strtotime($maintenanceConfig['until'])) {
                // 維護模式已過期，刪除維護文件
                unlink($maintenanceFile);
                return;
            }
            
            // 拋出503錯誤，進入維護模式
            throw new \Exception('Service Unavailable', 503);
        }
    }
    
    /**
     * 檢查當前IP是否在允許列表中
     * 
     * @param array $allowedIps 允許的IP列表
     * @return bool
     */
    private function isAllowedIp(array $allowedIps): bool
    {
        if (empty($allowedIps)) {
            return false;
        }
        
        $currentIp = $_SERVER['REMOTE_ADDR'] ?? '';
        
        // 檢查精確匹配
        if (in_array($currentIp, $allowedIps)) {
            return true;
        }
        
        // 檢查CIDR匹配
        foreach ($allowedIps as $allowedIp) {
            if (strpos($allowedIp, '/') !== false) {
                if ($this->ipInRange($currentIp, $allowedIp)) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    /**
     * 檢查IP是否在CIDR範圍內
     * 
     * @param string $ip 要檢查的IP
     * @param string $range CIDR範圍
     * @return bool
     */
    private function ipInRange(string $ip, string $range): bool
    {
        [$subnet, $bits] = explode('/', $range);
        
        if ($bits === null) {
            $bits = 32;
        }
        
        $ip = ip2long($ip);
        $subnet = ip2long($subnet);
        $mask = -1 << (32 - $bits);
        $subnet &= $mask;
        
        return ($ip & $mask) === $subnet;
    }
}