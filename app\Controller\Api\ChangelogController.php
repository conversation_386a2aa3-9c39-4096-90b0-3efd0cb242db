<?php

namespace App\Controller\Api;

/**
 * API 變更日誌控制器
 * 提供 API 版本變更歷史
 */
class ChangelogController extends BaseApiController
{
    /**
     * 取得變更日誌
     * 
     * GET /api/changelog
     */
    public function index(): void
    {
        $version = $_GET['version'] ?? 'all';
        $format = $_GET['format'] ?? 'json';
        
        $changelog = $this->getChangelog($version);
        
        if ($format === 'html') {
            $this->showHtmlChangelog($changelog);
            return;
        }
        
        $this->successResponse([
            'changelog' => $changelog,
            'current_version' => 'v1',
            'available_versions' => ['v1']
        ], '變更日誌取得成功');
    }

    /**
     * 取得特定版本的變更日誌
     * 
     * GET /api/v1/changelog
     */
    public function v1(): void
    {
        $changelog = $this->getChangelog('v1');
        
        $this->successResponse([
            'version' => 'v1',
            'changelog' => $changelog
        ], 'v1 變更日誌取得成功');
    }

    /**
     * 顯示 HTML 格式的變更日誌
     */
    private function showHtmlChangelog(array $changelog): void
    {
        $data = [
            'changelog' => $changelog,
            'base_url' => $this->getBaseUrl()
        ];
        
        include __DIR__ . '/../../View/api/changelog.php';
        exit;
    }

    /**
     * 取得變更日誌資料
     * 
     * @param string $version
     * @return array
     */
    private function getChangelog(string $version = 'all'): array
    {
        $allChangelog = [
            'v1' => [
                'version' => 'v1.0.0',
                'release_date' => '2024-01-15',
                'status' => 'stable',
                'changes' => [
                    'added' => [
                        '新增搜尋 API，支援文檔和新聞搜尋',
                        '新增文檔管理 API，支援上傳、更新、刪除',
                        '新增新聞 API，提供最新科技新聞',
                        '新增統計 API，提供系統使用統計',
                        '新增 API 文檔系統，支援互動式測試',
                        '新增代碼範例生成器，支援多種程式語言',
                        '新增速率限制功能',
                        '新增快取機制',
                        '新增分頁支援',
                        '新增搜尋建議功能',
                        '新增進階搜尋功能',
                        '新增批次操作支援',
                        '新增即時統計功能',
                        '新增檔案上傳功能',
                        '新增內容分析功能'
                    ],
                    'changed' => [],
                    'deprecated' => [],
                    'removed' => [],
                    'fixed' => [
                        '修復搜尋結果排序問題',
                        '修復檔案上傳大小限制',
                        '修復 API 響應格式一致性',
                        '修復錯誤處理機制'
                    ],
                    'security' => [
                        '加強 API 金鑰驗證',
                        '新增請求頻率限制',
                        '加強檔案上傳安全檢查',
                        '新增 CORS 支援'
                    ]
                ],
                'breaking_changes' => [],
                'migration_notes' => [
                    '這是第一個穩定版本，無需遷移'
                ],
                'known_issues' => [
                    '大檔案上傳可能需要較長時間',
                    '複雜搜尋查詢可能影響效能'
                ]
            ]
        ];

        if ($version === 'all') {
            return $allChangelog;
        }

        return $allChangelog[$version] ?? [];
    }

    /**
     * 取得基礎 URL
     * 
     * @return string
     */
    private function getBaseUrl(): string
    {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        return "{$protocol}://{$host}";
    }
}