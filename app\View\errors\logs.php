<?php
/**
 * 錯誤日誌查看頁面
 * 僅在開發模式或管理員權限下可訪問
 */

$pageTitle = '錯誤日誌';
$logs = $logs ?? [];
?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($pageTitle) ?> - Tech.VG</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;700&display=swap');
        
        body {
            font-family: 'Noto Sans TC', sans-serif;
        }
        
        .log-entry {
            transition: all 0.3s ease;
        }
        
        .log-entry:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .log-level-error {
            border-left: 4px solid #ef4444;
        }
        
        .log-level-warning {
            border-left: 4px solid #f59e0b;
        }
        
        .log-level-info {
            border-left: 4px solid #3b82f6;
        }
        
        .code-block {
            background: #1f2937;
            color: #f9fafb;
            border-radius: 8px;
            padding: 16px;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 導航欄 -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-semibold text-gray-900">
                        <i class="fas fa-bug mr-2 text-red-500"></i>
                        錯誤日誌查看器
                    </h1>
                </div>
                <div class="flex items-center space-x-4">
                    <button onclick="location.reload()" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <i class="fas fa-sync-alt mr-2"></i>
                        重新整理
                    </button>
                    <a href="/" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <i class="fas fa-home mr-2"></i>
                        返回首頁
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- 統計卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-circle text-2xl text-red-500"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">錯誤總數</dt>
                                <dd class="text-lg font-medium text-gray-900"><?= count(array_filter($logs, fn($log) => ($log['level'] ?? 'error') === 'error')) ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-triangle text-2xl text-yellow-500"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">警告總數</dt>
                                <dd class="text-lg font-medium text-gray-900"><?= count(array_filter($logs, fn($log) => ($log['level'] ?? 'error') === 'warning')) ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-info-circle text-2xl text-blue-500"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">資訊總數</dt>
                                <dd class="text-lg font-medium text-gray-900"><?= count(array_filter($logs, fn($log) => ($log['level'] ?? 'error') === 'info')) ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-clock text-2xl text-gray-500"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">最後更新</dt>
                                <dd class="text-lg font-medium text-gray-900"><?= !empty($logs) ? date('H:i:s', strtotime($logs[0]['timestamp'] ?? 'now')) : 'N/A' ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 日誌列表 -->
        <div class="bg-white shadow overflow-hidden sm:rounded-md">
            <div class="px-4 py-5 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">錯誤日誌記錄</h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500">最近的錯誤和警告記錄</p>
            </div>
            
            <?php if (empty($logs)): ?>
                <div class="text-center py-12">
                    <i class="fas fa-check-circle text-6xl text-green-500 mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">沒有錯誤記錄</h3>
                    <p class="text-gray-500">系統運行正常，沒有發現錯誤。</p>
                </div>
            <?php else: ?>
                <ul class="divide-y divide-gray-200">
                    <?php foreach ($logs as $index => $log): ?>
                        <?php
                        $level = $log['level'] ?? 'error';
                        $levelClass = "log-level-{$level}";
                        $levelIcon = [
                            'error' => 'fas fa-times-circle text-red-500',
                            'warning' => 'fas fa-exclamation-triangle text-yellow-500',
                            'info' => 'fas fa-info-circle text-blue-500'
                        ][$level] ?? 'fas fa-times-circle text-red-500';
                        ?>
                        <li class="log-entry <?= $levelClass ?>">
                            <div class="px-4 py-4 sm:px-6">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <i class="<?= $levelIcon ?> mr-3"></i>
                                        <p class="text-sm font-medium text-gray-900 truncate">
                                            <?= htmlspecialchars($log['message'] ?? $log['error'] ?? '未知錯誤') ?>
                                        </p>
                                    </div>
                                    <div class="ml-2 flex-shrink-0 flex">
                                        <p class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                            <?= htmlspecialchars($log['timestamp'] ?? date('Y-m-d H:i:s')) ?>
                                        </p>
                                    </div>
                                </div>
                                
                                <?php if (isset($log['context']) || isset($log['file']) || isset($log['trace'])): ?>
                                    <div class="mt-2">
                                        <button onclick="toggleDetails(<?= $index ?>)" class="text-sm text-indigo-600 hover:text-indigo-500">
                                            <i class="fas fa-chevron-down mr-1"></i>
                                            顯示詳情
                                        </button>
                                    </div>
                                    
                                    <div id="details-<?= $index ?>" class="hidden mt-4 space-y-4">
                                        <?php if (isset($log['file'])): ?>
                                            <div>
                                                <h4 class="text-sm font-medium text-gray-900">檔案位置</h4>
                                                <p class="text-sm text-gray-600"><?= htmlspecialchars($log['file']) ?><?= isset($log['line']) ? ':' . $log['line'] : '' ?></p>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <?php if (isset($log['request_uri'])): ?>
                                            <div>
                                                <h4 class="text-sm font-medium text-gray-900">請求資訊</h4>
                                                <p class="text-sm text-gray-600">
                                                    <?= htmlspecialchars($log['request_method'] ?? 'GET') ?> 
                                                    <?= htmlspecialchars($log['request_uri']) ?>
                                                </p>
                                                <?php if (isset($log['user_agent'])): ?>
                                                    <p class="text-xs text-gray-500 mt-1">User Agent: <?= htmlspecialchars($log['user_agent']) ?></p>
                                                <?php endif; ?>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <?php if (isset($log['trace'])): ?>
                                            <div>
                                                <h4 class="text-sm font-medium text-gray-900">堆疊追蹤</h4>
                                                <div class="code-block">
                                                    <?= htmlspecialchars($log['trace']) ?>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <?php if (isset($log['context']) && !empty($log['context'])): ?>
                                            <div>
                                                <h4 class="text-sm font-medium text-gray-900">上下文資料</h4>
                                                <div class="code-block">
                                                    <?= htmlspecialchars(json_encode($log['context'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) ?>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </li>
                    <?php endforeach; ?>
                </ul>
            <?php endif; ?>
        </div>
    </div>

    <script>
        function toggleDetails(index) {
            const details = document.getElementById(`details-${index}`);
            const button = details.previousElementSibling.querySelector('button');
            const icon = button.querySelector('i');
            
            if (details.classList.contains('hidden')) {
                details.classList.remove('hidden');
                icon.classList.remove('fa-chevron-down');
                icon.classList.add('fa-chevron-up');
                button.innerHTML = button.innerHTML.replace('顯示詳情', '隱藏詳情');
            } else {
                details.classList.add('hidden');
                icon.classList.remove('fa-chevron-up');
                icon.classList.add('fa-chevron-down');
                button.innerHTML = button.innerHTML.replace('隱藏詳情', '顯示詳情');
            }
        }
    </script>
</body>
</html>