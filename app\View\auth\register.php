<?php include APP_ROOT . '/app/View/layout/header.php'; ?>

<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <div class="flex justify-center">
                <div class="w-16 h-16 gradient-bg rounded-lg flex items-center justify-center">
                    <i class="fas fa-search text-white text-2xl"></i>
                </div>
            </div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                註冊新帳戶
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
                或
                <a href="/auth/login" class="font-medium text-blue-600 hover:text-blue-500">
                    登入現有帳戶
                </a>
            </p>
        </div>

        <!-- 顯示錯誤訊息 -->
        <?php if (flash('error')): ?>
            <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative alert-auto-hide">
                <span class="block sm:inline"><?= htmlspecialchars(flash('error')) ?></span>
            </div>
        <?php endif; ?>

        <!-- 顯示成功訊息 -->
        <?php if (flash('success')): ?>
            <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded relative alert-auto-hide">
                <span class="block sm:inline"><?= htmlspecialchars(flash('success')) ?></span>
            </div>
        <?php endif; ?>

        <form class="mt-8 space-y-6" action="/auth/register" method="POST">
            <?= csrf_field() ?>
            
            <div class="space-y-4">
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700">姓名</label>
                    <input id="name" 
                           name="name" 
                           type="text" 
                           autocomplete="name" 
                           required 
                           value="<?= htmlspecialchars(old('name')) ?>"
                           class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" 
                           placeholder="請輸入您的姓名">
                </div>

                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700">電子郵件地址</label>
                    <input id="email" 
                           name="email" 
                           type="email" 
                           autocomplete="email" 
                           required 
                           value="<?= htmlspecialchars(old('email')) ?>"
                           class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" 
                           placeholder="請輸入電子郵件地址">
                </div>

                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700">密碼</label>
                    <input id="password" 
                           name="password" 
                           type="password" 
                           autocomplete="new-password" 
                           required 
                           class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" 
                           placeholder="請輸入密碼（至少8個字元）">
                </div>

                <div>
                    <label for="confirm_password" class="block text-sm font-medium text-gray-700">確認密碼</label>
                    <input id="confirm_password" 
                           name="confirm_password" 
                           type="password" 
                           autocomplete="new-password" 
                           required 
                           class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" 
                           placeholder="請再次輸入密碼">
                </div>
            </div>

            <div class="flex items-center">
                <input id="agree_terms" 
                       name="agree_terms" 
                       type="checkbox" 
                       required
                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                <label for="agree_terms" class="ml-2 block text-sm text-gray-900">
                    我同意 <a href="/terms" class="text-blue-600 hover:text-blue-500" target="_blank">服務條款</a> 
                    和 <a href="/privacy" class="text-blue-600 hover:text-blue-500" target="_blank">隱私政策</a>
                </label>
            </div>

            <div>
                <button type="submit" 
                        class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white gradient-bg hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                        <i class="fas fa-user-plus text-blue-300 group-hover:text-blue-200"></i>
                    </span>
                    註冊帳戶
                </button>
            </div>

            <div class="text-center">
                <p class="text-xs text-gray-500">
                    註冊即表示您同意我們的服務條款和隱私政策
                </p>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const password = document.getElementById('password');
    const confirmPassword = document.getElementById('confirm_password');
    
    function validatePassword() {
        if (password.value !== confirmPassword.value) {
            confirmPassword.setCustomValidity('密碼不符');
        } else {
            confirmPassword.setCustomValidity('');
        }
    }
    
    password.addEventListener('change', validatePassword);
    confirmPassword.addEventListener('keyup', validatePassword);
});
</script>

<?php include APP_ROOT . '/app/View/layout/footer.php'; ?>