<?php
/**
 * API 測試工具頁面
 * 提供互動式 API 測試介面
 */

$title = 'Tech.VG API 測試工具';
$description = '互動式 API 測試工具，讓您輕鬆測試所有 API 端點';
$baseUrl = $data['base_url'] ?? '';
?>

<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($title) ?></title>
    <meta name="description" content="<?= htmlspecialchars($description) ?>">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --dark-color: #1e293b;
            --light-color: #f8fafc;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--light-color);
        }

        .test-header {
            background: linear-gradient(135deg, var(--primary-color), #3b82f6);
            color: white;
            padding: 2rem 0;
        }

        .request-panel {
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }

        .response-panel {
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            min-height: 400px;
        }

        .method-select {
            width: 120px;
        }

        .url-input {
            flex: 1;
        }

        .send-button {
            width: 120px;
        }

        .tab-content {
            padding: 1.5rem;
        }

        .header-row {
            margin-bottom: 0.5rem;
        }

        .header-row:last-child {
            margin-bottom: 0;
        }

        .response-status {
            padding: 0.5rem 1rem;
            border-radius: 0.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .status-200 { background: #dcfce7; color: #166534; }
        .status-400 { background: #fef3c7; color: #92400e; }
        .status-500 { background: #fecaca; color: #991b1b; }

        .response-time {
            font-size: 0.875rem;
            color: var(--secondary-color);
        }

        .code-editor {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.875rem;
            border: 1px solid #e2e8f0;
            border-radius: 0.25rem;
            min-height: 200px;
        }

        .preset-buttons {
            margin-bottom: 1rem;
        }

        .preset-btn {
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .history-item {
            padding: 0.75rem;
            border: 1px solid #e2e8f0;
            border-radius: 0.25rem;
            margin-bottom: 0.5rem;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .history-item:hover {
            background: var(--light-color);
        }

        .history-method {
            font-size: 0.75rem;
            font-weight: 600;
            padding: 0.125rem 0.375rem;
            border-radius: 0.125rem;
            margin-right: 0.5rem;
        }

        .method-get { background: var(--success-color); color: white; }
        .method-post { background: var(--primary-color); color: white; }
        .method-put { background: var(--warning-color); color: white; }
        .method-delete { background: var(--danger-color); color: white; }

        .loading-spinner {
            display: none;
        }

        .loading .loading-spinner {
            display: inline-block;
        }

        .loading .send-text {
            display: none;
        }
    </style>
</head>
<body>
    <!-- 標題區 -->
    <div class="test-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-flask"></i> API 測試工具</h1>
                    <p class="mb-0">測試 Tech.VG API 的所有端點，查看即時響應</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="<?= htmlspecialchars($baseUrl) ?>/api/docs?format=html" class="btn btn-light">
                        <i class="fas fa-book"></i> API 文檔
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container mt-4">
        <div class="row">
            <!-- 請求面板 -->
            <div class="col-lg-8">
                <div class="request-panel">
                    <div class="p-3 border-bottom">
                        <h5><i class="fas fa-paper-plane"></i> 請求設定</h5>
                    </div>
                    
                    <!-- 預設請求按鈕 -->
                    <div class="p-3 border-bottom">
                        <div class="preset-buttons">
                            <button class="btn btn-outline-primary preset-btn" onclick="loadPreset('search')">
                                搜尋 API
                            </button>
                            <button class="btn btn-outline-primary preset-btn" onclick="loadPreset('documents')">
                                文檔列表
                            </button>
                            <button class="btn btn-outline-primary preset-btn" onclick="loadPreset('news')">
                                新聞列表
                            </button>
                            <button class="btn btn-outline-primary preset-btn" onclick="loadPreset('stats')">
                                統計資料
                            </button>
                        </div>
                    </div>

                    <!-- URL 和方法 -->
                    <div class="p-3 border-bottom">
                        <div class="d-flex gap-2">
                            <select class="form-select method-select" id="method">
                                <option value="GET">GET</option>
                                <option value="POST">POST</option>
                                <option value="PUT">PUT</option>
                                <option value="DELETE">DELETE</option>
                            </select>
                            <input type="text" class="form-control url-input" id="url" 
                                   placeholder="輸入 API 端點 URL" 
                                   value="<?= htmlspecialchars($baseUrl) ?>/api/">
                            <button class="btn btn-primary send-button" id="sendBtn" onclick="sendRequest()">
                                <span class="loading-spinner spinner-border spinner-border-sm me-1"></span>
                                <span class="send-text">發送</span>
                            </button>
                        </div>
                    </div>

                    <!-- 標籤頁 -->
                    <ul class="nav nav-tabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" data-bs-toggle="tab" href="#params-tab">參數</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#headers-tab">標頭</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#body-tab">請求主體</a>
                        </li>
                    </ul>

                    <div class="tab-content">
                        <!-- 參數標籤 -->
                        <div class="tab-pane active" id="params-tab">
                            <div id="params-container">
                                <div class="row header-row">
                                    <div class="col-5">
                                        <input type="text" class="form-control" placeholder="參數名稱">
                                    </div>
                                    <div class="col-5">
                                        <input type="text" class="form-control" placeholder="參數值">
                                    </div>
                                    <div class="col-2">
                                        <button class="btn btn-outline-danger btn-sm" onclick="removeParamRow(this)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <button class="btn btn-outline-primary btn-sm mt-2" onclick="addParamRow()">
                                <i class="fas fa-plus"></i> 新增參數
                            </button>
                        </div>

                        <!-- 標頭標籤 -->
                        <div class="tab-pane" id="headers-tab">
                            <div id="headers-container">
                                <div class="row header-row">
                                    <div class="col-5">
                                        <input type="text" class="form-control" placeholder="標頭名稱" value="Content-Type">
                                    </div>
                                    <div class="col-5">
                                        <input type="text" class="form-control" placeholder="標頭值" value="application/json">
                                    </div>
                                    <div class="col-2">
                                        <button class="btn btn-outline-danger btn-sm" onclick="removeHeaderRow(this)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <button class="btn btn-outline-primary btn-sm mt-2" onclick="addHeaderRow()">
                                <i class="fas fa-plus"></i> 新增標頭
                            </button>
                        </div>

                        <!-- 請求主體標籤 -->
                        <div class="tab-pane" id="body-tab">
                            <textarea class="form-control code-editor" id="requestBody" 
                                      placeholder="輸入 JSON 格式的請求主體"></textarea>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 響應面板和歷史記錄 -->
            <div class="col-lg-4">
                <!-- 響應面板 -->
                <div class="response-panel mb-3">
                    <div class="p-3 border-bottom">
                        <h5><i class="fas fa-reply"></i> 響應結果</h5>
                    </div>
                    
                    <div class="p-3">
                        <div id="response-status" class="response-status" style="display: none;">
                            <span id="status-code"></span>
                            <span id="status-text"></span>
                            <div class="response-time mt-1">
                                響應時間: <span id="response-time">-</span>ms
                            </div>
                        </div>

                        <ul class="nav nav-tabs nav-tabs-sm" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" data-bs-toggle="tab" href="#response-body">響應主體</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="tab" href="#response-headers">響應標頭</a>
                            </li>
                        </ul>

                        <div class="tab-content mt-3">
                            <div class="tab-pane active" id="response-body">
                                <pre><code id="response-content" class="language-json">發送請求以查看響應...</code></pre>
                            </div>
                            <div class="tab-pane" id="response-headers">
                                <pre><code id="response-headers-content">發送請求以查看響應標頭...</code></pre>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 歷史記錄 -->
                <div class="response-panel">
                    <div class="p-3 border-bottom d-flex justify-content-between align-items-center">
                        <h6><i class="fas fa-history"></i> 請求歷史</h6>
                        <button class="btn btn-outline-danger btn-sm" onclick="clearHistory()">
                            <i class="fas fa-trash"></i> 清除
                        </button>
                    </div>
                    
                    <div class="p-3">
                        <div id="history-container">
                            <p class="text-muted text-center">尚無請求歷史</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    
    <script>
        let requestHistory = JSON.parse(localStorage.getItem('apiTestHistory') || '[]');

        // 載入預設請求
        function loadPreset(type) {
            const presets = {
                search: {
                    method: 'GET',
                    url: '<?= htmlspecialchars($baseUrl) ?>/api/search',
                    params: [
                        { name: 'q', value: '人工智慧' },
                        { name: 'type', value: 'all' },
                        { name: 'page', value: '1' }
                    ]
                },
                documents: {
                    method: 'GET',
                    url: '<?= htmlspecialchars($baseUrl) ?>/api/documents',
                    params: [
                        { name: 'page', value: '1' },
                        { name: 'per_page', value: '10' }
                    ]
                },
                news: {
                    method: 'GET',
                    url: '<?= htmlspecialchars($baseUrl) ?>/api/news',
                    params: [
                        { name: 'page', value: '1' },
                        { name: 'per_page', value: '10' }
                    ]
                },
                stats: {
                    method: 'GET',
                    url: '<?= htmlspecialchars($baseUrl) ?>/api/stats/overview',
                    params: []
                }
            };

            const preset = presets[type];
            if (preset) {
                document.getElementById('method').value = preset.method;
                document.getElementById('url').value = preset.url;
                
                // 清除現有參數
                const container = document.getElementById('params-container');
                container.innerHTML = '';
                
                // 添加預設參數
                preset.params.forEach(param => {
                    addParamRow(param.name, param.value);
                });
                
                if (preset.params.length === 0) {
                    addParamRow();
                }
            }
        }

        // 新增參數行
        function addParamRow(name = '', value = '') {
            const container = document.getElementById('params-container');
            const row = document.createElement('div');
            row.className = 'row header-row';
            row.innerHTML = `
                <div class="col-5">
                    <input type="text" class="form-control" placeholder="參數名稱" value="${name}">
                </div>
                <div class="col-5">
                    <input type="text" class="form-control" placeholder="參數值" value="${value}">
                </div>
                <div class="col-2">
                    <button class="btn btn-outline-danger btn-sm" onclick="removeParamRow(this)">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;
            container.appendChild(row);
        }

        // 移除參數行
        function removeParamRow(button) {
            button.closest('.header-row').remove();
        }

        // 新增標頭行
        function addHeaderRow(name = '', value = '') {
            const container = document.getElementById('headers-container');
            const row = document.createElement('div');
            row.className = 'row header-row';
            row.innerHTML = `
                <div class="col-5">
                    <input type="text" class="form-control" placeholder="標頭名稱" value="${name}">
                </div>
                <div class="col-5">
                    <input type="text" class="form-control" placeholder="標頭值" value="${value}">
                </div>
                <div class="col-2">
                    <button class="btn btn-outline-danger btn-sm" onclick="removeHeaderRow(this)">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;
            container.appendChild(row);
        }

        // 移除標頭行
        function removeHeaderRow(button) {
            button.closest('.header-row').remove();
        }

        // 發送請求
        async function sendRequest() {
            const method = document.getElementById('method').value;
            const url = document.getElementById('url').value;
            const sendBtn = document.getElementById('sendBtn');
            
            // 顯示載入狀態
            sendBtn.classList.add('loading');
            sendBtn.disabled = true;
            
            const startTime = Date.now();
            
            try {
                // 收集參數
                const params = new URLSearchParams();
                const paramRows = document.querySelectorAll('#params-container .header-row');
                paramRows.forEach(row => {
                    const inputs = row.querySelectorAll('input');
                    const name = inputs[0].value.trim();
                    const value = inputs[1].value.trim();
                    if (name && value) {
                        params.append(name, value);
                    }
                });

                // 收集標頭
                const headers = {};
                const headerRows = document.querySelectorAll('#headers-container .header-row');
                headerRows.forEach(row => {
                    const inputs = row.querySelectorAll('input');
                    const name = inputs[0].value.trim();
                    const value = inputs[1].value.trim();
                    if (name && value) {
                        headers[name] = value;
                    }
                });

                // 構建請求
                let requestUrl = url;
                const requestOptions = {
                    method: method,
                    headers: headers
                };

                if (method === 'GET' && params.toString()) {
                    requestUrl += (url.includes('?') ? '&' : '?') + params.toString();
                } else if (method !== 'GET') {
                    const body = document.getElementById('requestBody').value.trim();
                    if (body) {
                        requestOptions.body = body;
                    } else if (params.toString()) {
                        requestOptions.body = params.toString();
                        headers['Content-Type'] = 'application/x-www-form-urlencoded';
                    }
                }

                // 發送請求
                const response = await fetch(requestUrl, requestOptions);
                const responseTime = Date.now() - startTime;
                
                // 處理響應
                const responseText = await response.text();
                let responseData;
                
                try {
                    responseData = JSON.parse(responseText);
                } catch (e) {
                    responseData = responseText;
                }

                // 顯示響應
                displayResponse(response, responseData, responseTime);
                
                // 添加到歷史記錄
                addToHistory({
                    method: method,
                    url: requestUrl,
                    status: response.status,
                    time: responseTime,
                    timestamp: new Date().toLocaleString()
                });

            } catch (error) {
                const responseTime = Date.now() - startTime;
                displayError(error, responseTime);
            } finally {
                // 隱藏載入狀態
                sendBtn.classList.remove('loading');
                sendBtn.disabled = false;
            }
        }

        // 顯示響應
        function displayResponse(response, data, responseTime) {
            const statusDiv = document.getElementById('response-status');
            const statusCode = document.getElementById('status-code');
            const statusText = document.getElementById('status-text');
            const responseTimeSpan = document.getElementById('response-time');
            const responseContent = document.getElementById('response-content');
            const responseHeadersContent = document.getElementById('response-headers-content');

            // 顯示狀態
            statusDiv.style.display = 'block';
            statusCode.textContent = response.status;
            statusText.textContent = response.statusText;
            responseTimeSpan.textContent = responseTime;

            // 設定狀態樣式
            statusDiv.className = 'response-status';
            if (response.status >= 200 && response.status < 300) {
                statusDiv.classList.add('status-200');
            } else if (response.status >= 400 && response.status < 500) {
                statusDiv.classList.add('status-400');
            } else {
                statusDiv.classList.add('status-500');
            }

            // 顯示響應內容
            if (typeof data === 'object') {
                responseContent.textContent = JSON.stringify(data, null, 2);
                responseContent.className = 'language-json';
            } else {
                responseContent.textContent = data;
                responseContent.className = 'language-text';
            }

            // 顯示響應標頭
            const headersText = Array.from(response.headers.entries())
                .map(([key, value]) => `${key}: ${value}`)
                .join('\n');
            responseHeadersContent.textContent = headersText;

            // 高亮顯示
            Prism.highlightElement(responseContent);
        }

        // 顯示錯誤
        function displayError(error, responseTime) {
            const statusDiv = document.getElementById('response-status');
            const statusCode = document.getElementById('status-code');
            const statusText = document.getElementById('status-text');
            const responseTimeSpan = document.getElementById('response-time');
            const responseContent = document.getElementById('response-content');

            statusDiv.style.display = 'block';
            statusDiv.className = 'response-status status-500';
            statusCode.textContent = 'ERROR';
            statusText.textContent = error.message;
            responseTimeSpan.textContent = responseTime;

            responseContent.textContent = `錯誤: ${error.message}`;
            responseContent.className = 'language-text';
        }

        // 添加到歷史記錄
        function addToHistory(request) {
            requestHistory.unshift(request);
            if (requestHistory.length > 20) {
                requestHistory = requestHistory.slice(0, 20);
            }
            
            localStorage.setItem('apiTestHistory', JSON.stringify(requestHistory));
            renderHistory();
        }

        // 渲染歷史記錄
        function renderHistory() {
            const container = document.getElementById('history-container');
            
            if (requestHistory.length === 0) {
                container.innerHTML = '<p class="text-muted text-center">尚無請求歷史</p>';
                return;
            }

            container.innerHTML = requestHistory.map(request => `
                <div class="history-item" onclick="loadFromHistory('${request.url}', '${request.method}')">
                    <div>
                        <span class="history-method method-${request.method.toLowerCase()}">${request.method}</span>
                        <small class="text-muted">${request.url.replace('<?= htmlspecialchars($baseUrl) ?>', '')}</small>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mt-1">
                        <small class="text-muted">${request.timestamp}</small>
                        <span class="badge ${request.status >= 200 && request.status < 300 ? 'bg-success' : 'bg-danger'}">
                            ${request.status}
                        </span>
                    </div>
                </div>
            `).join('');
        }

        // 從歷史記錄載入
        function loadFromHistory(url, method) {
            document.getElementById('url').value = url;
            document.getElementById('method').value = method;
        }

        // 清除歷史記錄
        function clearHistory() {
            requestHistory = [];
            localStorage.removeItem('apiTestHistory');
            renderHistory();
        }

        // 頁面載入完成後的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 載入預設搜尋請求
            loadPreset('search');
            
            // 渲染歷史記錄
            renderHistory();
            
            // 高亮顯示程式碼
            Prism.highlightAll();
        });
    </script>
</body>
</html>