<?php
/**
 * 獨立的檢索頁面
 * 完全繞過框架的路由系統
 */

// 設定頭部
header('Content-Type: text/html; charset=utf-8');

// 直接輸出HTML
echo '<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>檢索 - Tech.VG</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background-color: white; padding: 20px; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        h1 { color: #333; }
        .search-form { margin-bottom: 20px; }
        .search-input { padding: 10px; width: 70%; font-size: 16px; border: 1px solid #ddd; border-radius: 4px; }
        .search-button { padding: 10px 20px; background-color: #4285f4; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; }
        .search-button:hover { background-color: #3367d6; }
        footer { margin-top: 20px; text-align: center; font-size: 12px; color: #666; }
        .header-nav { margin-bottom: 20px; }
        .header-nav a { color: #4285f4; text-decoration: none; margin-right: 15px; }
        .header-nav a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header-nav">
            <a href="/">首頁</a>
            <a href="/search.php">檢索</a>
            <a href="/documents">文檔</a>
            <a href="/news">新聞</a>
        </div>
        
        <h1>檢索系統</h1>
        
        <!-- 搜尋表單 -->
        <div class="search-form">
            <form action="/search.php" method="GET">
                <input type="text" 
                       name="q" 
                       value="' . htmlspecialchars($_GET['q'] ?? '') . '"
                       class="search-input"
                       placeholder="輸入關鍵字搜尋..."
                       autocomplete="off">
                <button type="submit" class="search-button">搜尋</button>
            </form>
        </div>
        
        <p>請輸入關鍵字進行檢索。</p>
        
        <footer>
            <p>© 2024 Tech.VG 科技新知檢索</p>
        </footer>
    </div>
</body>
</html>';