    </main>

    <!-- 頁尾 -->
    <footer class="bg-gray-900 text-white">
        <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- 關於我們 -->
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 gradient-bg rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-search text-white text-sm"></i>
                        </div>
                        <span class="text-xl font-bold">Tech.VG</span>
                    </div>
                    <p class="text-gray-300 mb-4">
                        專業的科技新知檢索平台，致力於提供最新的科技資訊、高效的文檔管理和智能檢索功能，
                        幫助用戶快速找到所需的技術資料和新聞資訊。
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fab fa-github"></i>
                        </a>
                    </div>
                </div>
                
                <!-- 快速連結 -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">快速連結</h3>
                    <ul class="space-y-2">
                        <li><a href="/" class="text-gray-300 hover:text-white transition-colors">首頁</a></li>
                        <li><a href="/search" class="text-gray-300 hover:text-white transition-colors">檢索</a></li>
                        <li><a href="/documents" class="text-gray-300 hover:text-white transition-colors">文檔管理</a></li>
                        <li><a href="/news" class="text-gray-300 hover:text-white transition-colors">科技新聞</a></li>
                        <li><a href="/documents/upload" class="text-gray-300 hover:text-white transition-colors">上傳文檔</a></li>
                        <li><a href="/search/advanced" class="text-gray-300 hover:text-white transition-colors">進階檢索</a></li>
                    </ul>
                </div>
                
                <!-- 支援 -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">支援</h3>
                    <ul class="space-y-2">
                        <li><a href="/about" class="text-gray-300 hover:text-white transition-colors">關於我們</a></li>
                        <li><a href="/contact" class="text-gray-300 hover:text-white transition-colors">聯絡我們</a></li>
                        <li><a href="/help" class="text-gray-300 hover:text-white transition-colors">使用說明</a></li>
                        <li><a href="/privacy" class="text-gray-300 hover:text-white transition-colors">隱私政策</a></li>
                        <li><a href="/terms" class="text-gray-300 hover:text-white transition-colors">服務條款</a></li>
                        <li><a href="/rss" class="text-gray-300 hover:text-white transition-colors">RSS 訂閱</a></li>
                    </ul>
                </div>
            </div>
            
            <!-- 分隔線 -->
            <div class="border-t border-gray-700 mt-8 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <div class="text-gray-400 text-sm">
                        © <?= date('Y') ?> Tech.VG. 保留所有權利。
                    </div>
                    <div class="text-gray-400 text-sm mt-2 md:mt-0">
                        最後更新：<?= date('Y-m-d H:i:s') ?>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // 行動選單切換
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenuButton = document.querySelector('.mobile-menu-button');
            const mobileMenu = document.querySelector('.mobile-menu');
            
            if (mobileMenuButton && mobileMenu) {
                mobileMenuButton.addEventListener('click', function() {
                    mobileMenu.classList.toggle('hidden');
                });
            }
            
            // 搜尋表單增強
            const searchForm = document.querySelector('form[action="/search"]');
            if (searchForm) {
                searchForm.addEventListener('submit', function(e) {
                    const input = this.querySelector('input[name="q"]');
                    if (!input.value.trim()) {
                        e.preventDefault();
                        input.focus();
                        return false;
                    }
                });
            }
            
            // 自動隱藏成功/錯誤訊息
            const alerts = document.querySelectorAll('.alert-auto-hide');
            alerts.forEach(function(alert) {
                setTimeout(function() {
                    alert.style.opacity = '0';
                    setTimeout(function() {
                        alert.remove();
                    }, 300);
                }, 5000);
            });
        });
        
        // 通用 AJAX 函數
        function ajaxRequest(url, options = {}) {
            const defaultOptions = {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            };
            
            const config = Object.assign(defaultOptions, options);
            
            return fetch(url, config)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .catch(error => {
                    console.error('AJAX request failed:', error);
                    throw error;
                });
        }
        
        // 顯示載入狀態
        function showLoading(element) {
            if (element) {
                element.classList.add('loading', 'show');
            }
        }
        
        function hideLoading(element) {
            if (element) {
                element.classList.remove('loading', 'show');
            }
        }
        
        // 顯示通知
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg alert-auto-hide ${
                type === 'success' ? 'bg-green-500' : 
                type === 'error' ? 'bg-red-500' : 
                type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500'
            } text-white`;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            // 自動移除
            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, 5000);
        }
    </script>
</body>
</html>