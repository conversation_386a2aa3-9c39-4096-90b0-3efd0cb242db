<?php include APP_ROOT . '/app/View/layout/header.php'; ?>

<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <div class="flex justify-center">
                <div class="w-16 h-16 gradient-bg rounded-lg flex items-center justify-center">
                    <i class="fas fa-search text-white text-2xl"></i>
                </div>
            </div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                重設密碼
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
                輸入您的電子郵件地址，我們將發送重設密碼的指示給您
            </p>
        </div>

        <!-- 顯示錯誤訊息 -->
        <?php if (flash('error')): ?>
            <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative alert-auto-hide">
                <span class="block sm:inline"><?= htmlspecialchars(flash('error')) ?></span>
            </div>
        <?php endif; ?>

        <!-- 顯示成功訊息 -->
        <?php if (flash('success')): ?>
            <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded relative alert-auto-hide">
                <span class="block sm:inline"><?= htmlspecialchars(flash('success')) ?></span>
            </div>
        <?php endif; ?>

        <form class="mt-8 space-y-6" action="/auth/forgot-password" method="POST">
            <?= csrf_field() ?>
            
            <div>
                <label for="email" class="block text-sm font-medium text-gray-700">電子郵件地址</label>
                <input id="email" 
                       name="email" 
                       type="email" 
                       autocomplete="email" 
                       required 
                       value="<?= htmlspecialchars(old('email')) ?>"
                       class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" 
                       placeholder="請輸入您的電子郵件地址">
            </div>

            <div>
                <button type="submit" 
                        class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white gradient-bg hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                        <i class="fas fa-envelope text-blue-300 group-hover:text-blue-200"></i>
                    </span>
                    發送重設指示
                </button>
            </div>

            <div class="text-center space-y-2">
                <p class="text-sm">
                    <a href="/auth/login" class="font-medium text-blue-600 hover:text-blue-500">
                        返回登入頁面
                    </a>
                </p>
                <p class="text-sm">
                    還沒有帳戶？
                    <a href="/auth/register" class="font-medium text-blue-600 hover:text-blue-500">
                        立即註冊
                    </a>
                </p>
            </div>
        </form>

        <div class="mt-8 p-4 bg-blue-50 rounded-lg">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-info-circle text-blue-400"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800">
                        重設密碼說明
                    </h3>
                    <div class="mt-2 text-sm text-blue-700">
                        <ul class="list-disc list-inside space-y-1">
                            <li>請確保輸入的電子郵件地址正確</li>
                            <li>如果該電子郵件地址存在於我們的系統中，您將收到重設指示</li>
                            <li>請檢查您的垃圾郵件資料夾</li>
                            <li>重設連結將在24小時後失效</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include APP_ROOT . '/app/View/layout/footer.php'; ?>