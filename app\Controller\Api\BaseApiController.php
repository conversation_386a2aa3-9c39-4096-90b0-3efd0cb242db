<?php

namespace App\Controller\Api;

use App\Controller\BaseController;

/**
 * API 基礎控制器
 * 提供所有 API 控制器的共用功能
 */
abstract class BaseApiController extends BaseController
{
    /**
     * API 版本
     */
    protected string $apiVersion = 'v1';
    
    /**
     * 每頁預設項目數
     */
    protected int $defaultPerPage = 20;
    
    /**
     * 最大每頁項目數
     */
    protected int $maxPerPage = 100;

    public function __construct()
    {
        // 設定 API 響應標頭
        $this->setApiHeaders();
        
        // 驗證 API 請求
        $this->validateApiRequest();
    }

    /**
     * 設定 API 響應標頭
     */
    protected function setApiHeaders(): void
    {
        header('Content-Type: application/json; charset=utf-8');
        header('X-API-Version: ' . $this->apiVersion);
        header('X-Powered-By: Tech.VG API');
        
        // CORS 設定
        if (isset($_SERVER['HTTP_ORIGIN'])) {
            header('Access-Control-Allow-Origin: ' . $_SERVER['HTTP_ORIGIN']);
        }
        header('Access-Control-Allow-Credentials: true');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, X-API-Key');
        
        // 處理 OPTIONS 請求
        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            http_response_code(200);
            exit;
        }
    }

    /**
     * 驗證 API 請求
     */
    protected function validateApiRequest(): void
    {
        // 檢查請求方法
        $allowedMethods = ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'];
        if (!in_array($_SERVER['REQUEST_METHOD'], $allowedMethods)) {
            $this->errorResponse('不支援的請求方法', 405);
        }

        // 檢查 Content-Type（對於 POST/PUT 請求）
        if (in_array($_SERVER['REQUEST_METHOD'], ['POST', 'PUT'])) {
            $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
            if (!empty($contentType) && strpos($contentType, 'application/json') === false && strpos($contentType, 'multipart/form-data') === false) {
                // 允許 form-data 用於檔案上傳
                if (strpos($contentType, 'application/x-www-form-urlencoded') === false) {
                    $this->errorResponse('不支援的 Content-Type', 415);
                }
            }
        }
    }

    /**
     * 成功響應
     * 
     * @param mixed $data 響應資料
     * @param string $message 響應訊息
     * @param int $statusCode HTTP 狀態碼
     * @param array $meta 元資料
     */
    protected function successResponse($data = null, string $message = 'Success', int $statusCode = 200, array $meta = []): void
    {
        $response = [
            'success' => true,
            'message' => $message,
            'data' => $data,
            'timestamp' => date('c'),
            'api_version' => $this->apiVersion
        ];

        if (!empty($meta)) {
            $response['meta'] = $meta;
        }

        $this->jsonResponse($response, $statusCode);
    }

    /**
     * 錯誤響應
     * 
     * @param string $message 錯誤訊息
     * @param int $statusCode HTTP 狀態碼
     * @param array $errors 詳細錯誤資訊
     * @param string $errorCode 錯誤代碼
     */
    protected function errorResponse(string $message, int $statusCode = 400, array $errors = [], string $errorCode = null): void
    {
        $response = [
            'success' => false,
            'message' => $message,
            'timestamp' => date('c'),
            'api_version' => $this->apiVersion
        ];

        if (!empty($errors)) {
            $response['errors'] = $errors;
        }

        if ($errorCode) {
            $response['error_code'] = $errorCode;
        }

        // 記錄錯誤
        $this->logError('API Error: ' . $message, [
            'status_code' => $statusCode,
            'errors' => $errors,
            'error_code' => $errorCode,
            'request_method' => $_SERVER['REQUEST_METHOD'] ?? '',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);

        $this->jsonResponse($response, $statusCode);
    }

    /**
     * 分頁響應
     * 
     * @param array $items 項目列表
     * @param int $total 總項目數
     * @param int $page 當前頁碼
     * @param int $perPage 每頁項目數
     * @param string $message 響應訊息
     */
    protected function paginatedResponse(array $items, int $total, int $page, int $perPage, string $message = 'Success'): void
    {
        $totalPages = ceil($total / $perPage);
        
        $meta = [
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total_items' => $total,
                'total_pages' => $totalPages,
                'has_next_page' => $page < $totalPages,
                'has_prev_page' => $page > 1
            ]
        ];

        $this->successResponse($items, $message, 200, $meta);
    }

    /**
     * 驗證請求參數
     * 
     * @param array $rules 驗證規則
     * @param array $data 要驗證的資料
     * @return array 驗證錯誤
     */
    protected function validateRequest(array $rules, array $data = null): array
    {
        if ($data === null) {
            $data = $this->getRequestData();
        }

        $errors = [];

        foreach ($rules as $field => $rule) {
            $ruleArray = is_string($rule) ? explode('|', $rule) : $rule;
            $value = $data[$field] ?? null;

            foreach ($ruleArray as $singleRule) {
                $error = $this->validateField($field, $value, $singleRule);
                if ($error) {
                    $errors[$field][] = $error;
                }
            }
        }

        return $errors;
    }

    /**
     * 驗證單一欄位
     * 
     * @param string $field 欄位名稱
     * @param mixed $value 欄位值
     * @param string $rule 驗證規則
     * @return string|null 錯誤訊息
     */
    protected function validateField(string $field, $value, string $rule): ?string
    {
        switch ($rule) {
            case 'required':
                if (empty($value) && $value !== '0') {
                    return "{$field} 為必填欄位";
                }
                break;

            case 'email':
                if (!empty($value) && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                    return "{$field} 必須是有效的電子郵件地址";
                }
                break;

            case 'numeric':
                if (!empty($value) && !is_numeric($value)) {
                    return "{$field} 必須是數字";
                }
                break;

            case 'integer':
                if (!empty($value) && !filter_var($value, FILTER_VALIDATE_INT)) {
                    return "{$field} 必須是整數";
                }
                break;

            case 'boolean':
                if (!empty($value) && !in_array($value, [true, false, 'true', 'false', '1', '0', 1, 0], true)) {
                    return "{$field} 必須是布林值";
                }
                break;

            default:
                // 處理帶參數的規則，如 min:3, max:255
                if (strpos($rule, ':') !== false) {
                    [$ruleName, $parameter] = explode(':', $rule, 2);
                    
                    switch ($ruleName) {
                        case 'min':
                            if (!empty($value) && strlen($value) < (int)$parameter) {
                                return "{$field} 最少需要 {$parameter} 個字元";
                            }
                            break;

                        case 'max':
                            if (!empty($value) && strlen($value) > (int)$parameter) {
                                return "{$field} 最多只能有 {$parameter} 個字元";
                            }
                            break;

                        case 'in':
                            $allowedValues = explode(',', $parameter);
                            if (!empty($value) && !in_array($value, $allowedValues)) {
                                return "{$field} 必須是以下值之一：" . implode(', ', $allowedValues);
                            }
                            break;
                    }
                }
                break;
        }

        return null;
    }

    /**
     * 取得請求資料
     * 
     * @return array
     */
    protected function getRequestData(): array
    {
        $method = $_SERVER['REQUEST_METHOD'];
        
        switch ($method) {
            case 'GET':
                return $_GET;
                
            case 'POST':
                $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
                if (strpos($contentType, 'application/json') !== false) {
                    $input = file_get_contents('php://input');
                    return json_decode($input, true) ?: [];
                }
                return $_POST;
                
            case 'PUT':
            case 'DELETE':
                $input = file_get_contents('php://input');
                parse_str($input, $data);
                
                // 嘗試解析 JSON
                if (empty($data)) {
                    $jsonData = json_decode($input, true);
                    if (json_last_error() === JSON_ERROR_NONE) {
                        $data = $jsonData;
                    }
                }
                
                return $data;
                
            default:
                return [];
        }
    }

    /**
     * 取得分頁參數
     * 
     * @return array
     */
    protected function getPaginationParams(): array
    {
        $page = max(1, (int)($this->getGet('page', 1)));
        $perPage = min($this->maxPerPage, max(1, (int)($this->getGet('per_page', $this->defaultPerPage))));
        
        return [
            'page' => $page,
            'per_page' => $perPage,
            'offset' => ($page - 1) * $perPage
        ];
    }

    /**
     * 取得排序參數
     * 
     * @param array $allowedFields 允許排序的欄位
     * @param string $defaultField 預設排序欄位
     * @param string $defaultDirection 預設排序方向
     * @return array
     */
    protected function getSortParams(array $allowedFields, string $defaultField = 'id', string $defaultDirection = 'desc'): array
    {
        $sortBy = $this->getGet('sort_by', $defaultField);
        $sortDirection = strtolower($this->getGet('sort_direction', $defaultDirection));
        
        // 驗證排序欄位
        if (!in_array($sortBy, $allowedFields)) {
            $sortBy = $defaultField;
        }
        
        // 驗證排序方向
        if (!in_array($sortDirection, ['asc', 'desc'])) {
            $sortDirection = $defaultDirection;
        }
        
        return [
            'sort_by' => $sortBy,
            'sort_direction' => $sortDirection
        ];
    }

    /**
     * 取得篩選參數
     * 
     * @param array $allowedFilters 允許的篩選欄位
     * @return array
     */
    protected function getFilterParams(array $allowedFilters): array
    {
        $filters = [];
        
        foreach ($allowedFilters as $filter) {
            $value = $this->getGet($filter);
            if ($value !== null && $value !== '') {
                $filters[$filter] = $value;
            }
        }
        
        return $filters;
    }

    /**
     * 檢查 API 金鑰（如果需要）
     * 
     * @return bool
     */
    protected function validateApiKey(): bool
    {
        $apiKey = $_SERVER['HTTP_X_API_KEY'] ?? $this->getGet('api_key');
        
        if (empty($apiKey)) {
            return false;
        }
        
        // 這裡應該從資料庫或配置檔案中驗證 API 金鑰
        // 暫時使用簡單的驗證
        $validApiKeys = [
            'demo_key_123456',
            'test_key_789012'
        ];
        
        return in_array($apiKey, $validApiKeys);
    }

    /**
     * 檢查速率限制
     * 
     * @param string $identifier 識別符（IP 或用戶 ID）
     * @param int $maxRequests 最大請求數
     * @param int $timeWindow 時間窗口（秒）
     * @return bool
     */
    protected function checkRateLimit(string $identifier, int $maxRequests = 100, int $timeWindow = 3600): bool
    {
        $cacheKey = "rate_limit:{$identifier}";
        $cacheFile = __DIR__ . "/../../../storage/cache/{$cacheKey}";
        
        // 確保快取目錄存在
        $cacheDir = dirname($cacheFile);
        if (!is_dir($cacheDir)) {
            mkdir($cacheDir, 0755, true);
        }
        
        $now = time();
        $requests = [];
        
        // 讀取現有請求記錄
        if (file_exists($cacheFile)) {
            $data = json_decode(file_get_contents($cacheFile), true);
            if ($data) {
                $requests = $data['requests'] ?? [];
            }
        }
        
        // 清理過期的請求記錄
        $requests = array_filter($requests, function($timestamp) use ($now, $timeWindow) {
            return ($now - $timestamp) < $timeWindow;
        });
        
        // 檢查是否超過限制
        if (count($requests) >= $maxRequests) {
            return false;
        }
        
        // 記錄當前請求
        $requests[] = $now;
        
        // 儲存更新後的記錄
        file_put_contents($cacheFile, json_encode([
            'requests' => $requests,
            'updated_at' => $now
        ]));
        
        return true;
    }

    /**
     * 取得客戶端 IP
     * 
     * @return string
     */
    protected function getClientIp(): string
    {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
}