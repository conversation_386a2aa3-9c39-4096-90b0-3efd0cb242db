<?php

namespace Tests\Unit\Controller;

use Tests\TestCase;
use App\Controller\HomeController;
use App\Service\SearchService;
use App\Service\NewsService;
use App\Service\DocumentService;

/**
 * 首頁控制器單元測試
 */
class HomeControllerTest extends TestCase
{
    private HomeController $controller;
    private $mockSearchService;
    private $mockNewsService;
    private $mockDocumentService;

    protected function setUp(): void
    {
        parent::setUp();
        
        // 建立模擬服務
        $this->mockSearchService = $this->createMock(SearchService::class);
        $this->mockNewsService = $this->createMock(NewsService::class);
        $this->mockDocumentService = $this->createMock(DocumentService::class);
        
        // 建立控制器實例
        $this->controller = new HomeController();
        
        // 注入模擬服務
        $this->injectMockServices();
    }

    /**
     * 注入模擬服務到控制器
     */
    private function injectMockServices(): void
    {
        $reflection = new \ReflectionClass($this->controller);
        
        $searchProperty = $reflection->getProperty('searchService');
        $searchProperty->setAccessible(true);
        $searchProperty->setValue($this->controller, $this->mockSearchService);
        
        $newsProperty = $reflection->getProperty('newsService');
        $newsProperty->setAccessible(true);
        $newsProperty->setValue($this->controller, $this->mockNewsService);
        
        $documentProperty = $reflection->getProperty('documentService');
        $documentProperty->setAccessible(true);
        $documentProperty->setValue($this->controller, $this->mockDocumentService);
    }

    /**
     * 測試首頁顯示
     */
    public function testIndex(): void
    {
        // 模擬最新新聞
        $mockNews = [
            [
                'id' => 1,
                'title' => 'AI 技術新突破',
                'summary' => '人工智慧在醫療領域取得重大進展',
                'category' => 'ai',
                'published_at' => '2024-01-15 10:30:00',
                'views' => 150
            ],
            [
                'id' => 2,
                'title' => '區塊鏈應用擴展',
                'summary' => '區塊鏈技術在金融業的新應用',
                'category' => 'blockchain',
                'published_at' => '2024-01-14 14:20:00',
                'views' => 89
            ]
        ];

        // 模擬熱門文檔
        $mockDocuments = [
            [
                'id' => 1,
                'title' => '機器學習基礎',
                'filename' => 'ml_basics.pdf',
                'file_type' => 'pdf',
                'downloads' => 245
            ],
            [
                'id' => 2,
                'title' => '深度學習指南',
                'filename' => 'deep_learning.md',
                'file_type' => 'md',
                'downloads' => 189
            ]
        ];

        // 設定模擬服務的期望行為
        $this->mockNewsService
            ->expects($this->once())
            ->method('getLatestNews')
            ->with(6)
            ->willReturn($mockNews);

        $this->mockDocumentService
            ->expects($this->once())
            ->method('getPopularDocuments')
            ->with(6)
            ->willReturn($mockDocuments);

        // 捕獲輸出
        ob_start();
        $this->controller->index();
        $output = ob_get_clean();

        // 驗證輸出包含預期內容
        $this->assertStringContainsString('AI 技術新突破', $output);
        $this->assertStringContainsString('機器學習基礎', $output);
    }

    /**
     * 測試關於頁面
     */
    public function testAbout(): void
    {
        ob_start();
        $this->controller->about();
        $output = ob_get_clean();

        // 驗證輸出包含關於頁面的內容
        $this->assertStringContainsString('關於', $output);
        $this->assertNotEmpty($output);
    }

    /**
     * 測試聯絡頁面
     */
    public function testContact(): void
    {
        ob_start();
        $this->controller->contact();
        $output = ob_get_clean();

        // 驗證輸出包含聯絡頁面的內容
        $this->assertStringContainsString('聯絡', $output);
        $this->assertNotEmpty($output);
    }

    /**
     * 測試處理聯絡表單提交
     */
    public function testHandleContactForm(): void
    {
        // 模擬 POST 資料
        $_POST = [
            'name' => '測試用戶',
            'email' => '<EMAIL>',
            'subject' => '測試主題',
            'message' => '這是一個測試訊息'
        ];
        $_SERVER['REQUEST_METHOD'] = 'POST';

        ob_start();
        $this->controller->contact();
        $output = ob_get_clean();

        // 驗證處理成功
        $this->assertStringContainsString('感謝您的訊息', $output);
        
        // 清理
        unset($_POST);
        $_SERVER['REQUEST_METHOD'] = 'GET';
    }

    /**
     * 測試處理無效的聯絡表單
     */
    public function testHandleInvalidContactForm(): void
    {
        // 模擬無效的 POST 資料
        $_POST = [
            'name' => '',
            'email' => 'invalid-email',
            'subject' => '',
            'message' => ''
        ];
        $_SERVER['REQUEST_METHOD'] = 'POST';

        ob_start();
        $this->controller->contact();
        $output = ob_get_clean();

        // 驗證顯示錯誤訊息
        $this->assertStringContainsString('錯誤', $output);
        
        // 清理
        unset($_POST);
        $_SERVER['REQUEST_METHOD'] = 'GET';
    }

    /**
     * 測試搜尋功能
     */
    public function testSearch(): void
    {
        $searchQuery = 'machine learning';
        $mockResults = [
            'documents' => [
                [
                    'id' => 1,
                    'title' => 'Machine Learning Basics',
                    'content' => 'Introduction to machine learning concepts',
                    'relevance' => 0.95
                ]
            ],
            'news' => [
                [
                    'id' => 1,
                    'title' => 'Machine Learning Breakthrough',
                    'summary' => 'New advances in ML algorithms',
                    'relevance' => 0.88
                ]
            ],
            'total_results' => 2
        ];

        // 設定模擬搜尋服務
        $this->mockSearchService
            ->expects($this->once())
            ->method('search')
            ->with($searchQuery, ['documents', 'news'], 1, 20)
            ->willReturn($mockResults);

        // 模擬 GET 參數
        $_GET['q'] = $searchQuery;

        ob_start();
        $this->controller->search();
        $output = ob_get_clean();

        // 驗證搜尋結果
        $this->assertStringContainsString('Machine Learning Basics', $output);
        $this->assertStringContainsString('Machine Learning Breakthrough', $output);
        
        // 清理
        unset($_GET['q']);
    }

    /**
     * 測試空搜尋查詢
     */
    public function testEmptySearch(): void
    {
        $_GET['q'] = '';

        ob_start();
        $this->controller->search();
        $output = ob_get_clean();

        // 驗證顯示空搜尋提示
        $this->assertStringContainsString('請輸入搜尋關鍵字', $output);
        
        // 清理
        unset($_GET['q']);
    }

    /**
     * 測試統計資料取得
     */
    public function testGetStatistics(): void
    {
        $mockStats = [
            'total_documents' => 150,
            'total_news' => 89,
            'total_searches' => 1250,
            'popular_categories' => ['ai', 'blockchain', 'mobile']
        ];

        // 設定模擬服務
        $this->mockDocumentService
            ->expects($this->once())
            ->method('getTotalCount')
            ->willReturn(150);

        $this->mockNewsService
            ->expects($this->once())
            ->method('getTotalCount')
            ->willReturn(89);

        $this->mockSearchService
            ->expects($this->once())
            ->method('getTotalSearches')
            ->willReturn(1250);

        // 使用反射調用私有方法
        $reflection = new \ReflectionClass($this->controller);
        $method = $reflection->getMethod('getStatistics');
        $method->setAccessible(true);
        
        $result = $method->invoke($this->controller);

        // 驗證統計資料
        $this->assertEquals(150, $result['total_documents']);
        $this->assertEquals(89, $result['total_news']);
        $this->assertEquals(1250, $result['total_searches']);
    }

    /**
     * 測試錯誤處理
     */
    public function testErrorHandling(): void
    {
        // 設定服務拋出異常
        $this->mockNewsService
            ->expects($this->once())
            ->method('getLatestNews')
            ->willThrowException(new \Exception('Database error'));

        $this->mockDocumentService
            ->expects($this->once())
            ->method('getPopularDocuments')
            ->willReturn([]);

        ob_start();
        $this->controller->index();
        $output = ob_get_clean();

        // 驗證錯誤處理
        $this->assertNotEmpty($output);
        // 應該仍然顯示頁面，但沒有新聞內容
    }

    protected function tearDown(): void
    {
        // 清理全域變數
        unset($_GET, $_POST);
        $_SERVER['REQUEST_METHOD'] = 'GET';
        
        parent::tearDown();
    }
}