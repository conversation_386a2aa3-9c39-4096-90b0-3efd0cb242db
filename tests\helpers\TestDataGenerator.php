<?php

namespace Tests\Helpers;

/**
 * 測試資料生成器
 * 用於生成測試所需的各種資料
 */
class TestDataGenerator
{
    private array $config;
    private \PDO $db;

    public function __construct(\PDO $db, array $config = [])
    {
        $this->db = $db;
        $this->config = $config;
    }

    /**
     * 生成測試文檔資料
     */
    public function generateDocuments(int $count = 10): array
    {
        $documents = [];
        $categories = $this->config['test_data']['documents']['categories'] ?? ['ai', 'blockchain', 'mobile'];
        $fileTypes = $this->config['test_data']['documents']['file_types'] ?? ['txt', 'md', 'pdf'];

        for ($i = 1; $i <= $count; $i++) {
            $category = $categories[array_rand($categories)];
            $fileType = $fileTypes[array_rand($fileTypes)];
            
            $document = [
                'title' => $this->generateDocumentTitle($category, $i),
                'filename' => "test_document_{$i}.{$fileType}",
                'file_type' => $fileType,
                'file_size' => rand(1024, 1024 * 1024), // 1KB - 1MB
                'content' => $this->generateDocumentContent($category),
                'category' => $category,
                'status' => 'active',
                'downloads' => rand(0, 1000),
                'created_at' => $this->generateRandomDate()
            ];

            $documents[] = $document;
        }

        return $documents;
    }

    /**
     * 生成測試新聞資料
     */
    public function generateNews(int $count = 20): array
    {
        $news = [];
        $categories = $this->config['test_data']['news']['categories'] ?? ['ai', 'blockchain', 'mobile'];
        $sources = $this->config['test_data']['news']['sources'] ?? ['TechCrunch', 'Wired'];

        for ($i = 1; $i <= $count; $i++) {
            $category = $categories[array_rand($categories)];
            $source = $sources[array_rand($sources)];
            
            $article = [
                'title' => $this->generateNewsTitle($category, $i),
                'summary' => $this->generateNewsSummary($category),
                'content' => $this->generateNewsContent($category),
                'url' => "https://example.com/news/{$i}",
                'category' => $category,
                'source' => $source,
                'views' => rand(0, 5000),
                'published_at' => $this->generateRandomDate(),
                'created_at' => $this->generateRandomDate()
            ];

            $news[] = $article;
        }

        return $news;
    }

    /**
     * 生成測試搜尋歷史
     */
    public function generateSearchHistory(int $count = 50): array
    {
        $searches = [];
        $queries = [
            'machine learning', 'artificial intelligence', 'blockchain technology',
            'mobile development', 'web security', 'cloud computing', 'data science',
            'neural networks', 'cryptocurrency', 'IoT devices'
        ];
        $types = ['all', 'documents', 'news'];

        for ($i = 1; $i <= $count; $i++) {
            $query = $queries[array_rand($queries)];
            $type = $types[array_rand($types)];
            
            $search = [
                'query' => $query,
                'type' => $type,
                'results_count' => rand(0, 100),
                'search_time' => round(rand(10, 500) / 1000, 3), // 0.01-0.5秒
                'created_at' => $this->generateRandomDate()
            ];

            $searches[] = $search;
        }

        return $searches;
    }

    /**
     * 生成測試用戶資料
     */
    public function generateUsers(int $count = 10): array
    {
        $users = [];
        $roles = $this->config['test_data']['users']['roles'] ?? ['admin', 'editor', 'viewer'];

        for ($i = 1; $i <= $count; $i++) {
            $role = $roles[array_rand($roles)];
            
            $user = [
                'username' => "test_user_{$i}",
                'email' => "test{$i}@example.com",
                'password' => password_hash('test_password', PASSWORD_DEFAULT),
                'role' => $role,
                'status' => 'active',
                'created_at' => $this->generateRandomDate()
            ];

            $users[] = $user;
        }

        return $users;
    }

    /**
     * 生成文檔標題
     */
    private function generateDocumentTitle(string $category, int $index): string
    {
        $titles = [
            'ai' => [
                '機器學習基礎指南',
                '深度學習實戰教程',
                '人工智慧應用案例',
                '神經網路架構設計',
                '自然語言處理技術'
            ],
            'blockchain' => [
                '區塊鏈技術原理',
                '智能合約開發指南',
                '加密貨幣交易系統',
                '分散式帳本應用',
                'DeFi 協議設計'
            ],
            'mobile' => [
                '行動應用開發框架',
                'iOS 開發最佳實踐',
                'Android 效能優化',
                '跨平台開發工具',
                '行動 UI/UX 設計'
            ],
            'web' => [
                '前端框架比較',
                '後端 API 設計',
                '網站效能優化',
                '響應式設計原則',
                '網頁安全防護'
            ],
            'security' => [
                '網路安全基礎',
                '滲透測試方法',
                '加密演算法實作',
                '安全漏洞分析',
                '資安事件應對'
            ]
        ];

        $categoryTitles = $titles[$category] ?? $titles['ai'];
        $baseTitle = $categoryTitles[array_rand($categoryTitles)];
        
        return "{$baseTitle} #{$index}";
    }

    /**
     * 生成文檔內容
     */
    private function generateDocumentContent(string $category): string
    {
        $content = [
            'ai' => '本文檔詳細介紹了人工智慧領域的核心概念和實際應用。內容涵蓋機器學習演算法、深度學習架構、以及各種 AI 技術在實際場景中的運用。',
            'blockchain' => '這份文檔深入探討區塊鏈技術的基本原理和實作細節。包括分散式帳本、共識機制、智能合約開發，以及各種區塊鏈平台的比較分析。',
            'mobile' => '本指南提供了行動應用開發的完整流程和最佳實踐。從 UI/UX 設計到後端整合，涵蓋 iOS 和 Android 平台的開發技巧和效能優化方法。',
            'web' => '這份文檔介紹了現代網頁開發的技術棧和開發流程。包括前端框架選擇、後端 API 設計、資料庫優化，以及網站部署和維護策略。',
            'security' => '本文檔詳細說明了網路安全的各個面向。從基礎的安全概念到進階的滲透測試技術，提供了完整的資安防護指南和實戰案例。'
        ];

        $baseContent = $content[$category] ?? $content['ai'];
        
        // 添加一些隨機內容使每個文檔都不同
        $additionalContent = [
            "\n\n## 技術特點\n- 高效能處理\n- 可擴展架構\n- 安全性保障",
            "\n\n## 實作範例\n```\n// 範例程式碼\nfunction example() {\n    return 'Hello World';\n}\n```",
            "\n\n## 相關資源\n- 官方文檔\n- 社群討論\n- 開源專案",
            "\n\n## 注意事項\n請確保在實作時遵循最佳實踐，並注意效能和安全性考量。"
        ];

        return $baseContent . $additionalContent[array_rand($additionalContent)];
    }

    /**
     * 生成新聞標題
     */
    private function generateNewsTitle(string $category, int $index): string
    {
        $titles = [
            'ai' => [
                'AI 技術突破：新演算法提升效能',
                '機器學習在醫療領域的最新應用',
                '深度學習模型達到新里程碑',
                '人工智慧助力自動駕駛發展',
                'AI 晶片市場迎來新變革'
            ],
            'blockchain' => [
                '區塊鏈技術推動金融創新',
                '新型共識機制提升交易效率',
                'DeFi 協議安全性獲得改善',
                '央行數位貨幣試點擴大',
                'NFT 市場出現新趨勢'
            ],
            'mobile' => [
                '5G 技術推動行動應用發展',
                '新款智慧手機發布',
                '行動支付普及率持續上升',
                'AR/VR 應用在行動裝置普及',
                '行動遊戲市場規模創新高'
            ],
            'web' => [
                '網頁技術標準更新',
                '新框架提升開發效率',
                '網站效能優化新方法',
                '瀏覽器安全性獲得提升',
                'PWA 技術應用日趨成熟'
            ],
            'security' => [
                '新型網路攻擊手法曝光',
                '資安防護技術獲得突破',
                '企業資料外洩事件分析',
                '零信任架構實作指南',
                '量子加密技術研發進展'
            ]
        ];

        $categoryTitles = $titles[$category] ?? $titles['ai'];
        $baseTitle = $categoryTitles[array_rand($categoryTitles)];
        
        return "{$baseTitle} - 第{$index}期";
    }

    /**
     * 生成新聞摘要
     */
    private function generateNewsSummary(string $category): string
    {
        $summaries = [
            'ai' => '最新研究顯示，人工智慧技術在多個領域都取得了重大突破，為未來發展奠定了堅實基礎。',
            'blockchain' => '區塊鏈技術持續演進，新的應用場景和解決方案不斷湧現，推動產業數位轉型。',
            'mobile' => '行動技術的快速發展為用戶帶來更好的體驗，同時也為開發者提供了更多創新機會。',
            'web' => '網頁技術的不斷進步使得網站開發更加高效，用戶體驗也得到了顯著提升。',
            'security' => '隨著網路威脅的不斷演變，資安技術也在持續創新，為數位世界提供更強的保護。'
        ];

        return $summaries[$category] ?? $summaries['ai'];
    }

    /**
     * 生成新聞內容
     */
    private function generateNewsContent(string $category): string
    {
        $summary = $this->generateNewsSummary($category);
        
        $additionalParagraphs = [
            "根據業界專家分析，這項技術的應用前景十分廣闊，預計將在未來幾年內得到大規模商用。",
            "相關企業已經開始投入大量資源進行研發，希望能夠在競爭激烈的市場中佔據領先地位。",
            "技術社群對此反應熱烈，認為這將為整個產業帶來革命性的變化。",
            "監管機構也在密切關注相關發展，並制定相應的政策框架來規範市場秩序。"
        ];

        $content = $summary . "\n\n";
        $content .= implode("\n\n", array_slice($additionalParagraphs, 0, rand(2, 4)));
        
        return $content;
    }

    /**
     * 生成隨機日期
     */
    private function generateRandomDate(int $daysBack = 90): string
    {
        $timestamp = time() - rand(0, $daysBack * 24 * 3600);
        return date('Y-m-d H:i:s', $timestamp);
    }

    /**
     * 插入生成的資料到資料庫
     */
    public function insertDocuments(array $documents): array
    {
        $insertedIds = [];
        
        $sql = "INSERT INTO documents (title, filename, file_type, file_size, content, status, created_at) 
                VALUES (:title, :filename, :file_type, :file_size, :content, :status, :created_at)";
        
        $stmt = $this->db->prepare($sql);
        
        foreach ($documents as $document) {
            $stmt->execute([
                ':title' => $document['title'],
                ':filename' => $document['filename'],
                ':file_type' => $document['file_type'],
                ':file_size' => $document['file_size'],
                ':content' => $document['content'],
                ':status' => $document['status'],
                ':created_at' => $document['created_at']
            ]);
            
            $insertedIds[] = $this->db->lastInsertId();
        }
        
        return $insertedIds;
    }

    /**
     * 插入新聞資料到資料庫
     */
    public function insertNews(array $news): array
    {
        $insertedIds = [];
        
        $sql = "INSERT INTO news_articles (title, summary, content, url, category, views, published_at, created_at) 
                VALUES (:title, :summary, :content, :url, :category, :views, :published_at, :created_at)";
        
        $stmt = $this->db->prepare($sql);
        
        foreach ($news as $article) {
            $stmt->execute([
                ':title' => $article['title'],
                ':summary' => $article['summary'],
                ':content' => $article['content'],
                ':url' => $article['url'],
                ':category' => $article['category'],
                ':views' => $article['views'],
                ':published_at' => $article['published_at'],
                ':created_at' => $article['created_at']
            ]);
            
            $insertedIds[] = $this->db->lastInsertId();
        }
        
        return $insertedIds;
    }

    /**
     * 插入搜尋歷史到資料庫
     */
    public function insertSearchHistory(array $searches): array
    {
        $insertedIds = [];
        
        $sql = "INSERT INTO search_history (query, type, results_count, search_time, created_at) 
                VALUES (:query, :type, :results_count, :search_time, :created_at)";
        
        $stmt = $this->db->prepare($sql);
        
        foreach ($searches as $search) {
            $stmt->execute([
                ':query' => $search['query'],
                ':type' => $search['type'],
                ':results_count' => $search['results_count'],
                ':search_time' => $search['search_time'],
                ':created_at' => $search['created_at']
            ]);
            
            $insertedIds[] = $this->db->lastInsertId();
        }
        
        return $insertedIds;
    }

    /**
     * 生成完整的測試資料集
     */
    public function generateFullTestDataset(): array
    {
        $documentCount = $this->config['test_data']['documents']['sample_count'] ?? 50;
        $newsCount = $this->config['test_data']['news']['sample_count'] ?? 100;
        $searchCount = 50;

        $documents = $this->generateDocuments($documentCount);
        $news = $this->generateNews($newsCount);
        $searches = $this->generateSearchHistory($searchCount);

        $documentIds = $this->insertDocuments($documents);
        $newsIds = $this->insertNews($news);
        $searchIds = $this->insertSearchHistory($searches);

        return [
            'documents' => [
                'count' => count($documentIds),
                'ids' => $documentIds
            ],
            'news' => [
                'count' => count($newsIds),
                'ids' => $newsIds
            ],
            'searches' => [
                'count' => count($searchIds),
                'ids' => $searchIds
            ]
        ];
    }

    /**
     * 清理所有測試資料
     */
    public function cleanupTestData(): void
    {
        $tables = ['documents', 'news_articles', 'search_history'];
        
        foreach ($tables as $table) {
            $this->db->exec("TRUNCATE TABLE {$table}");
        }
    }
}