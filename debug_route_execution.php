<?php
/**
 * 路由執行調試腳本
 */

// 錯誤報告設定
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 設定時區
date_default_timezone_set('Asia/Taipei');

// 定義應用程式根目錄
define('APP_ROOT', __DIR__);

// 載入自動載入器
require_once __DIR__ . '/htdocs/vendor/autoload.php';

// 載入環境變數
if (file_exists(APP_ROOT . '/.env')) {
    $lines = file(APP_ROOT . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            [$key, $value] = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

try {
    echo "=== 路由執行調試 ===\n";
    echo "時間: " . date('Y-m-d H:i:s') . "\n\n";
    
    // 載入配置
    $config = require APP_ROOT . '/config/app.php';
    echo "✅ 配置載入成功\n";
    
    // 載入路由
    $router = require APP_ROOT . '/config/routes.php';
    echo "✅ 路由載入成功\n";
    
    // 模擬API請求
    $testMethod = 'GET';
    $testUri = '/api/';
    
    echo "\n=== 模擬API請求 ===\n";
    echo "方法: {$testMethod}\n";
    echo "URI: {$testUri}\n\n";
    
    // 使用反射來訪問私有方法
    $reflection = new ReflectionClass($router);
    
    // 1. 測試路由匹配
    echo "1. 測試路由匹配...\n";
    $findMethod = $reflection->getMethod('findMatchingRoute');
    $findMethod->setAccessible(true);
    $matchedRoute = $findMethod->invoke($router, $testMethod, $testUri);
    
    if (!$matchedRoute) {
        echo "❌ 路由匹配失敗\n";
        exit(1);
    }
    
    echo "✅ 路由匹配成功\n";
    echo "處理器類型: " . (is_callable($matchedRoute['handler']) ? 'Closure' : 'String') . "\n";
    echo "中間件數量: " . count($matchedRoute['middleware']) . "\n";
    echo "參數數量: " . count($matchedRoute['params']) . "\n\n";
    
    // 2. 測試中間件執行
    echo "2. 測試中間件執行...\n";
    $middlewareProperty = $reflection->getProperty('middleware');
    $middlewareProperty->setAccessible(true);
    $globalMiddleware = $middlewareProperty->getValue($router);
    
    $allMiddleware = array_merge($globalMiddleware, $matchedRoute['middleware']);
    echo "全域中間件: " . count($globalMiddleware) . "\n";
    echo "路由中間件: " . count($matchedRoute['middleware']) . "\n";
    echo "總中間件: " . count($allMiddleware) . "\n";
    
    if (!empty($allMiddleware)) {
        echo "中間件列表:\n";
        foreach ($allMiddleware as $middleware) {
            echo "  - " . (is_string($middleware) ? $middleware : 'Closure') . "\n";
        }
    }
    echo "\n";
    
    // 3. 測試處理器執行
    echo "3. 測試處理器執行...\n";
    
    if (is_callable($matchedRoute['handler'])) {
        echo "執行Closure處理器...\n";
        
        // 捕獲輸出
        ob_start();
        try {
            $result = $matchedRoute['handler']();
            $output = ob_get_contents();
            ob_end_clean();
            
            echo "✅ Closure執行成功\n";
            echo "輸出長度: " . strlen($output) . " 字節\n";
            echo "返回值: " . (is_null($result) ? 'null' : gettype($result)) . "\n";
            
            if (!empty($output)) {
                echo "\n輸出內容預覽:\n";
                echo substr($output, 0, 200) . (strlen($output) > 200 ? '...' : '') . "\n";
            }
            
        } catch (Exception $e) {
            ob_end_clean();
            echo "❌ Closure執行失敗: " . $e->getMessage() . "\n";
            echo "文件: " . $e->getFile() . "\n";
            echo "行號: " . $e->getLine() . "\n";
        }
    } else {
        echo "處理器是字符串: " . $matchedRoute['handler'] . "\n";
    }
    
    echo "\n=== 完整路由分發測試 ===\n";
    
    // 4. 測試完整的路由分發
    try {
        ob_start();
        $router->dispatch($testUri, $testMethod);
        $fullOutput = ob_get_contents();
        ob_end_clean();
        
        echo "✅ 完整路由分發成功\n";
        echo "輸出長度: " . strlen($fullOutput) . " 字節\n";
        
        if (!empty($fullOutput)) {
            echo "\n完整輸出內容預覽:\n";
            echo substr($fullOutput, 0, 300) . (strlen($fullOutput) > 300 ? '...' : '') . "\n";
        }
        
    } catch (Exception $e) {
        ob_end_clean();
        echo "❌ 完整路由分發失敗: " . $e->getMessage() . "\n";
        echo "文件: " . $e->getFile() . "\n";
        echo "行號: " . $e->getLine() . "\n";
        echo "堆疊追蹤:\n" . $e->getTraceAsString() . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ 調試過程發生錯誤: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行號: " . $e->getLine() . "\n";
    echo "堆疊追蹤:\n" . $e->getTraceAsString() . "\n";
}
?>
