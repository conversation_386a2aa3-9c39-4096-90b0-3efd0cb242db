<?php

namespace App\Controller;

/**
 * 錯誤頁面控制器
 * 處理各種HTTP錯誤狀態的頁面顯示
 */
class ErrorController extends BaseController
{
    /**
     * 404 錯誤頁面
     * 
     * @return void
     */
    public function notFound(): void
    {
        http_response_code(404);
        $this->setViewData('pageTitle', '404 - 頁面不存在');
        $this->setViewData('errorCode', '404');
        $this->setViewData('errorTitle', '頁面不存在');
        $this->setViewData('errorMessage', '抱歉，您要尋找的頁面不存在或已被移除。');
        
        $this->render('errors/404');
    }
    
    /**
     * 403 錯誤頁面
     * 
     * @return void
     */
    public function forbidden(): void
    {
        http_response_code(403);
        $this->setViewData('pageTitle', '403 - 拒絕訪問');
        $this->setViewData('errorCode', '403');
        $this->setViewData('errorTitle', '拒絕訪問');
        $this->setViewData('errorMessage', '抱歉，您沒有權限訪問此資源。請確認您已登入並具有相應的權限。');
        
        $this->render('errors/403');
    }
    
    /**
     * 500 錯誤頁面
     * 
     * @return void
     */
    public function internalServerError(): void
    {
        http_response_code(500);
        $this->setViewData('pageTitle', '500 - 伺服器錯誤');
        $this->setViewData('errorCode', '500');
        $this->setViewData('errorTitle', '伺服器內部錯誤');
        $this->setViewData('errorMessage', '抱歉，伺服器發生了一些問題。我們正在努力修復中，請稍後再試。');
        
        $this->render('errors/500');
    }
    
    /**
     * 503 錯誤頁面（維護模式）
     * 
     * @return void
     */
    public function serviceUnavailable(): void
    {
        http_response_code(503);
        $this->setViewData('pageTitle', '503 - 服務暫時不可用');
        $this->setViewData('errorCode', '503');
        $this->setViewData('errorTitle', '服務暫時不可用');
        $this->setViewData('errorMessage', '網站正在進行維護升級，預計很快就會恢復正常。感謝您的耐心等待。');
        
        $this->render('errors/503');
    }
    
    /**
     * 通用錯誤頁面處理
     * 
     * @param int $statusCode HTTP狀態碼
     * @param string|null $message 自定義錯誤訊息
     * @return void
     */
    public function handleError(int $statusCode, ?string $message = null): void
    {
        http_response_code($statusCode);
        
        switch ($statusCode) {
            case 403:
                $this->forbidden();
                break;
            case 404:
                $this->notFound();
                break;
            case 500:
                $this->internalServerError();
                break;
            case 503:
                $this->serviceUnavailable();
                break;
            default:
                // 對於其他錯誤代碼，使用通用錯誤頁面
                $this->setViewData('pageTitle', "{$statusCode} - 錯誤");
                $this->setViewData('errorCode', (string)$statusCode);
                $this->setViewData('errorTitle', '發生錯誤');
                $this->setViewData('errorMessage', $message ?: '抱歉，發生了一個錯誤。請稍後再試。');
                
                // 根據錯誤類型選擇適當的模板
                if ($statusCode >= 500) {
                    $this->render('errors/500');
                } elseif ($statusCode >= 400) {
                    $this->render('errors/404');
                } else {
                    $this->render('errors/500');
                }
                break;
        }
    }
    
    /**
     * API錯誤響應
     * 
     * @param int $statusCode HTTP狀態碼
     * @param string $message 錯誤訊息
     * @param array $details 額外的錯誤詳情
     * @return void
     */
    public function apiError(int $statusCode, string $message, array $details = []): void
    {
        $response = [
            'error' => true,
            'status_code' => $statusCode,
            'message' => $message,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        if (!empty($details)) {
            $response['details'] = $details;
        }
        
        $this->jsonResponse($response, $statusCode);
    }
    
    /**
     * 記錄錯誤並顯示錯誤頁面
     * 
     * @param \Exception $exception 異常對象
     * @return void
     */
    public function handleException(\Exception $exception): void
    {
        // 記錄錯誤日誌
        $this->logError($exception->getMessage(), [
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString(),
            'request_uri' => $_SERVER['REQUEST_URI'] ?? '',
            'request_method' => $_SERVER['REQUEST_METHOD'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'ip' => $_SERVER['REMOTE_ADDR'] ?? ''
        ]);
        
        // 根據異常代碼顯示相應的錯誤頁面
        $statusCode = $exception->getCode() ?: 500;
        $this->handleError($statusCode, $exception->getMessage());
    }
    
    /**
     * 檢查是否為AJAX請求
     * 
     * @return bool
     */
    private function isAjaxRequest(): bool
    {
        return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
    
    /**
     * 檢查是否為API請求
     * 
     * @return bool
     */
    private function isApiRequest(): bool
    {
        $requestUri = $_SERVER['REQUEST_URI'] ?? '';
        return strpos($requestUri, '/api/') === 0 || 
               (isset($_SERVER['HTTP_ACCEPT']) && strpos($_SERVER['HTTP_ACCEPT'], 'application/json') !== false);
    }
    
    /**
     * 顯示錯誤日誌頁面
     * 僅在開發模式或管理員權限下可訪問
     * 
     * @return void
     */
    public function logs(): void
    {
        // 檢查權限（這裡簡化處理，實際應用中應該檢查用戶權限）
        $config = require __DIR__ . '/../../config/app.php';
        if (!$config['debug']) {
            $this->forbidden();
            return;
        }
        
        $logs = $this->getErrorLogs();
        
        $this->setViewData('pageTitle', '錯誤日誌');
        $this->setViewData('logs', $logs);
        
        $this->render('errors/logs');
    }
    
    /**
     * 讀取錯誤日誌
     * 
     * @param int $limit 限制返回的日誌數量
     * @return array
     */
    private function getErrorLogs(int $limit = 100): array
    {
        $logFile = __DIR__ . '/../../storage/logs/error.log';
        
        if (!file_exists($logFile)) {
            return [];
        }
        
        $lines = file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        $logs = [];
        
        // 從最新的日誌開始讀取
        $lines = array_reverse($lines);
        
        foreach ($lines as $line) {
            if (count($logs) >= $limit) {
                break;
            }
            
            $logData = json_decode($line, true);
            if ($logData) {
                // 確定日誌級別
                if (!isset($logData['level'])) {
                    if (strpos(strtolower($logData['message'] ?? ''), 'warning') !== false) {
                        $logData['level'] = 'warning';
                    } elseif (strpos(strtolower($logData['message'] ?? ''), 'info') !== false) {
                        $logData['level'] = 'info';
                    } else {
                        $logData['level'] = 'error';
                    }
                }
                
                $logs[] = $logData;
            }
        }
        
        return $logs;
    }
    
    /**
     * 清空錯誤日誌
     * 
     * @return void
     */
    public function clearLogs(): void
    {
        // 檢查權限
        $config = require __DIR__ . '/../../config/app.php';
        if (!$config['debug']) {
            $this->apiError(403, '權限不足');
            return;
        }
        
        $logFile = __DIR__ . '/../../storage/logs/error.log';
        
        if (file_exists($logFile)) {
            if (file_put_contents($logFile, '') !== false) {
                $this->jsonResponse(['success' => true, 'message' => '日誌已清空']);
            } else {
                $this->apiError(500, '清空日誌失敗');
            }
        } else {
            $this->jsonResponse(['success' => true, 'message' => '日誌文件不存在']);
        }
    }
}