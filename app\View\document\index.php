<?php
/**
 * 文檔列表頁面
 */

$documents = $documents ?? [];
$pagination = $pagination ?? [];
$currentPage = $currentPage ?? 1;
$status = $status ?? '';
$fileType = $fileType ?? '';
$error = $error ?? '';

include __DIR__ . '/../layout/header.php';
?>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- 頁面標題 -->
    <div class="flex justify-between items-center mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 mb-2">文檔管理</h1>
            <p class="text-gray-600">管理和檢索您的技術文檔</p>
        </div>
        <a href="/documents/upload" 
           class="inline-flex items-center px-4 py-2 btn-primary text-white rounded-md hover:bg-blue-700 transition-colors">
            <i class="fas fa-upload mr-2"></i>上傳文檔
        </a>
    </div>

    <!-- 篩選器 -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <form method="GET" action="/documents" class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <!-- 狀態篩選 -->
            <div>
                <label for="status" class="block text-sm font-medium text-gray-700 mb-2">狀態</label>
                <select name="status" id="status" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">所有狀態</option>
                    <option value="active" <?= $status === 'active' ? 'selected' : '' ?>>已啟用</option>
                    <option value="inactive" <?= $status === 'inactive' ? 'selected' : '' ?>>已停用</option>
                    <option value="processing" <?= $status === 'processing' ? 'selected' : '' ?>>處理中</option>
                </select>
            </div>

            <!-- 檔案類型篩選 -->
            <div>
                <label for="file_type" class="block text-sm font-medium text-gray-700 mb-2">檔案類型</label>
                <select name="file_type" id="file_type" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">所有類型</option>
                    <option value="txt" <?= $fileType === 'txt' ? 'selected' : '' ?>>文字檔 (.txt)</option>
                    <option value="md" <?= $fileType === 'md' ? 'selected' : '' ?>>Markdown (.md)</option>
                    <option value="markdown" <?= $fileType === 'markdown' ? 'selected' : '' ?>>Markdown (.markdown)</option>
                </select>
            </div>

            <!-- 搜尋按鈕 -->
            <div class="flex items-end">
                <button type="submit" class="w-full btn-primary text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
                    <i class="fas fa-filter mr-2"></i>篩選
                </button>
            </div>
        </form>
    </div>

    <?php if ($error): ?>
        <div class="bg-red-50 border border-red-200 rounded-md p-4 mb-8">
            <div class="flex">
                <i class="fas fa-exclamation-circle text-red-400 mr-3 mt-1"></i>
                <div class="text-red-700"><?= htmlspecialchars($error) ?></div>
            </div>
        </div>
    <?php endif; ?>

    <!-- 文檔列表 -->
    <?php if (empty($documents)): ?>
        <div class="text-center py-12">
            <i class="fas fa-file-alt text-gray-400 text-6xl mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">暫無文檔</h3>
            <p class="text-gray-500 mb-6">您還沒有上傳任何文檔，立即開始上傳您的第一個文檔吧！</p>
            <a href="/documents/upload" 
               class="inline-flex items-center px-4 py-2 btn-primary text-white rounded-md hover:bg-blue-700 transition-colors">
                <i class="fas fa-upload mr-2"></i>上傳文檔
            </a>
        </div>
    <?php else: ?>
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <!-- 表格標題 -->
            <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
                <div class="grid grid-cols-12 gap-4 text-sm font-medium text-gray-700">
                    <div class="col-span-4">文檔名稱</div>
                    <div class="col-span-2">檔案類型</div>
                    <div class="col-span-2">大小</div>
                    <div class="col-span-2">狀態</div>
                    <div class="col-span-2">操作</div>
                </div>
            </div>

            <!-- 文檔列表 -->
            <div class="divide-y divide-gray-200">
                <?php foreach ($documents as $document): ?>
                    <div class="px-6 py-4 hover:bg-gray-50 transition-colors">
                        <div class="grid grid-cols-12 gap-4 items-center">
                            <!-- 文檔名稱 -->
                            <div class="col-span-4">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-file-alt text-blue-600"></i>
                                    </div>
                                    <div>
                                        <h3 class="text-sm font-medium text-gray-900">
                                            <a href="/documents/<?= $document['id'] ?>" class="hover:text-blue-600 transition-colors">
                                                <?= htmlspecialchars($document['title'] ?? $document['filename'] ?? '') ?>
                                            </a>
                                        </h3>
                                        <p class="text-xs text-gray-500">
                                            上傳於 <?= date('Y-m-d H:i', strtotime($document['created_at'] ?? 'now')) ?>
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <!-- 檔案類型 -->
                            <div class="col-span-2">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    <?= strtoupper($document['file_type'] ?? 'unknown') ?>
                                </span>
                            </div>

                            <!-- 檔案大小 -->
                            <div class="col-span-2 text-sm text-gray-600">
                                <?= $this->formatBytes($document['file_size'] ?? 0) ?>
                            </div>

                            <!-- 狀態 -->
                            <div class="col-span-2">
                                <?php
                                $status = $document['status'] ?? 'unknown';
                                $statusClass = [
                                    'active' => 'bg-green-100 text-green-800',
                                    'inactive' => 'bg-red-100 text-red-800',
                                    'processing' => 'bg-yellow-100 text-yellow-800'
                                ][$status] ?? 'bg-gray-100 text-gray-800';
                                
                                $statusText = [
                                    'active' => '已啟用',
                                    'inactive' => '已停用',
                                    'processing' => '處理中'
                                ][$status] ?? '未知';
                                ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $statusClass ?>">
                                    <?= $statusText ?>
                                </span>
                            </div>

                            <!-- 操作 -->
                            <div class="col-span-2">
                                <div class="flex items-center space-x-2">
                                    <a href="/documents/<?= $document['id'] ?>" 
                                       class="text-blue-600 hover:text-blue-800 transition-colors" 
                                       title="檢視">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    
                                    <a href="/documents/<?= $document['id'] ?>/edit" 
                                       class="text-green-600 hover:text-green-800 transition-colors" 
                                       title="編輯">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    
                                    <a href="/documents/<?= $document['id'] ?>/download" 
                                       class="text-purple-600 hover:text-purple-800 transition-colors" 
                                       title="下載">
                                        <i class="fas fa-download"></i>
                                    </a>
                                    
                                    <button onclick="deleteDocument(<?= $document['id'] ?>)" 
                                            class="text-red-600 hover:text-red-800 transition-colors" 
                                            title="刪除">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- 分頁 -->
        <?php if (!empty($pagination)): ?>
            <div class="mt-8 flex justify-center">
                <nav class="flex items-center space-x-2">
                    <?php if ($pagination['current_page'] > 1): ?>
                        <a href="?page=<?= $pagination['current_page'] - 1 ?>&status=<?= urlencode($status) ?>&file_type=<?= urlencode($fileType) ?>" 
                           class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                            上一頁
                        </a>
                    <?php endif; ?>

                    <span class="px-3 py-2 text-sm font-medium text-white bg-blue-600 border border-blue-600 rounded-md">
                        第 <?= $pagination['current_page'] ?> 頁，共 <?= $pagination['total_pages'] ?> 頁
                    </span>

                    <?php if ($pagination['current_page'] < $pagination['total_pages']): ?>
                        <a href="?page=<?= $pagination['current_page'] + 1 ?>&status=<?= urlencode($status) ?>&file_type=<?= urlencode($fileType) ?>" 
                           class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                            下一頁
                        </a>
                    <?php endif; ?>
                </nav>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</div>

<!-- 刪除確認對話框 -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <i class="fas fa-exclamation-triangle text-red-600"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mt-4">確認刪除</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500">
                    您確定要刪除此文檔嗎？此操作無法復原。
                </p>
            </div>
            <div class="items-center px-4 py-3">
                <button id="confirmDelete" 
                        class="px-4 py-2 bg-red-500 text-white text-base font-medium rounded-md w-24 mr-2 hover:bg-red-600 transition-colors">
                    刪除
                </button>
                <button onclick="closeDeleteModal()" 
                        class="px-4 py-2 bg-gray-300 text-gray-800 text-base font-medium rounded-md w-24 hover:bg-gray-400 transition-colors">
                    取消
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    let documentToDelete = null;

    function deleteDocument(documentId) {
        documentToDelete = documentId;
        document.getElementById('deleteModal').classList.remove('hidden');
    }

    function closeDeleteModal() {
        document.getElementById('deleteModal').classList.add('hidden');
        documentToDelete = null;
    }

    document.getElementById('confirmDelete').addEventListener('click', function() {
        if (documentToDelete) {
            // 發送刪除請求
            ajaxRequest(`/documents/${documentToDelete}`, {
                method: 'DELETE'
            })
            .then(response => {
                if (response.success) {
                    showNotification('文檔已成功刪除', 'success');
                    // 重新載入頁面
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    showNotification(response.error || '刪除失敗', 'error');
                }
            })
            .catch(error => {
                showNotification('刪除失敗，請稍後再試', 'error');
            })
            .finally(() => {
                closeDeleteModal();
            });
        }
    });

    // 點擊模態框外部關閉
    document.getElementById('deleteModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeDeleteModal();
        }
    });
</script>

<?php include __DIR__ . '/../layout/footer.php'; ?>