<?php
/**
 * 403 錯誤頁面
 * 當用戶沒有權限訪問資源時顯示
 */

$pageTitle = '403 - 拒絕訪問';
$errorCode = '403';
$errorTitle = '拒絕訪問';
$errorMessage = '抱歉，您沒有權限訪問此資源。請確認您已登入並具有相應的權限。';
?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($pageTitle) ?> - Tech.VG</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;700&display=swap');
        
        body {
            font-family: 'Noto Sans TC', sans-serif;
        }
        
        .error-animation {
            animation: bounce 2s ease-in-out infinite;
        }
        
        @keyframes bounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-15px); }
        }
        
        .btn-hover {
            transition: all 0.3s ease;
        }
        
        .btn-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center">
    <div class="max-w-2xl mx-auto px-4 text-center">
        <!-- 錯誤圖示 -->
        <div class="error-animation mb-8">
            <div class="inline-flex items-center justify-center w-32 h-32 bg-gradient-to-br from-yellow-500 to-red-600 rounded-full shadow-2xl">
                <i class="fas fa-lock text-white text-5xl"></i>
            </div>
        </div>
        
        <!-- 錯誤代碼 -->
        <h1 class="text-8xl font-bold text-gray-800 mb-4"><?= htmlspecialchars($errorCode) ?></h1>
        
        <!-- 錯誤標題 -->
        <h2 class="text-3xl font-semibold text-gray-700 mb-6"><?= htmlspecialchars($errorTitle) ?></h2>
        
        <!-- 錯誤訊息 -->
        <p class="text-lg text-gray-600 mb-8 leading-relaxed">
            <?= htmlspecialchars($errorMessage) ?>
        </p>
        
        <!-- 建議操作 -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h3 class="text-xl font-semibold text-gray-800 mb-4">可能的解決方案：</h3>
            <ul class="text-left text-gray-600 space-y-2">
                <li class="flex items-center">
                    <i class="fas fa-sign-in-alt text-blue-500 mr-3"></i>
                    登入您的帳戶
                </li>
                <li class="flex items-center">
                    <i class="fas fa-user-check text-blue-500 mr-3"></i>
                    確認您的權限等級
                </li>
                <li class="flex items-center">
                    <i class="fas fa-envelope text-blue-500 mr-3"></i>
                    聯繫管理員申請權限
                </li>
                <li class="flex items-center">
                    <i class="fas fa-home text-blue-500 mr-3"></i>
                    返回首頁瀏覽公開內容
                </li>
            </ul>
        </div>
        
        <!-- 操作按鈕 -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="/auth/login" class="btn-hover inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors">
                <i class="fas fa-sign-in-alt mr-2"></i>
                登入
            </a>
            
            <a href="/" class="btn-hover inline-flex items-center px-6 py-3 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 transition-colors">
                <i class="fas fa-home mr-2"></i>
                返回首頁
            </a>
            
            <a href="/contact" class="btn-hover inline-flex items-center px-6 py-3 bg-orange-600 text-white font-medium rounded-lg hover:bg-orange-700 transition-colors">
                <i class="fas fa-envelope mr-2"></i>
                聯繫管理員
            </a>
        </div>
        
        <!-- 返回按鈕 -->
        <div class="mt-8">
            <button onclick="history.back()" class="text-gray-500 hover:text-gray-700 transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>
                返回上一頁
            </button>
        </div>
    </div>
    
    <!-- 背景裝飾 -->
    <div class="fixed inset-0 -z-10 overflow-hidden">
        <div class="absolute -top-40 -right-32 w-80 h-80 bg-yellow-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse"></div>
        <div class="absolute -bottom-40 -left-32 w-80 h-80 bg-red-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse"></div>
    </div>
</body>
</html>