<?php

/**
 * Tech.VG API 使用範例腳本
 * 展示如何使用各種 API 端點
 */

// 確保只能從命令行執行
if (php_sapi_name() !== 'cli') {
    die('此腳本只能從命令行執行');
}

class ApiExamples
{
    private string $baseUrl;
    private ?string $apiKey;

    public function __construct(string $baseUrl = 'http://localhost:8000', ?string $apiKey = null)
    {
        $this->baseUrl = rtrim($baseUrl, '/');
        $this->apiKey = $apiKey;
    }

    /**
     * 執行所有範例
     */
    public function runAllExamples(): void
    {
        echo "Tech.VG API 使用範例\n";
        echo "==================\n\n";

        $this->testApiHealth();
        $this->testSearch();
        $this->testDocuments();
        $this->testNews();
        $this->testStats();
        $this->testCache();

        echo "\n所有範例執行完成！\n";
    }

    /**
     * 測試 API 健康狀態
     */
    public function testApiHealth(): void
    {
        echo "1. 測試 API 健康狀態\n";
        echo "-------------------\n";

        // Ping 測試
        $response = $this->makeRequest('GET', '/api/test/ping');
        if ($response) {
            echo "✓ Ping 測試成功\n";
            echo "  響應時間: {$response['data']['response_time_ms']}ms\n";
            echo "  PHP 版本: {$response['data']['server_info']['php_version']}\n";
        }

        // 健康檢查
        $response = $this->makeRequest('GET', '/api/health');
        if ($response) {
            echo "✓ 健康檢查成功\n";
            echo "  狀態: {$response['status']}\n";
        }

        echo "\n";
    }

    /**
     * 測試搜尋 API
     */
    public function testSearch(): void
    {
        echo "2. 測試搜尋 API\n";
        echo "---------------\n";

        // 基本搜尋
        $response = $this->makeRequest('GET', '/api/search', ['q' => 'technology']);
        if ($response) {
            echo "✓ 基本搜尋成功\n";
            echo "  搜尋時間: {$response['data']['search_time']}秒\n";
            echo "  結果數量: " . count($response['data']['results']) . "\n";
        }

        // 搜尋建議
        $response = $this->makeRequest('GET', '/api/search/suggestions', ['q' => 'tech']);
        if ($response) {
            echo "✓ 搜尋建議成功\n";
            echo "  建議數量: " . count($response['data']['suggestions']) . "\n";
        }

        // 熱門搜尋
        $response = $this->makeRequest('GET', '/api/search/popular');
        if ($response) {
            echo "✓ 熱門搜尋成功\n";
            echo "  熱門搜尋數量: " . count($response['data']['popular_searches']) . "\n";
        }

        echo "\n";
    }

    /**
     * 測試文檔 API
     */
    public function testDocuments(): void
    {
        echo "3. 測試文檔 API\n";
        echo "---------------\n";

        // 取得文檔列表
        $response = $this->makeRequest('GET', '/api/documents', ['per_page' => 5]);
        if ($response) {
            echo "✓ 文檔列表取得成功\n";
            echo "  文檔數量: " . count($response['data']) . "\n";
            if (isset($response['meta']['pagination'])) {
                echo "  總頁數: {$response['meta']['pagination']['total_pages']}\n";
            }
        }

        // 取得文檔統計
        $response = $this->makeRequest('GET', '/api/documents/stats');
        if ($response) {
            echo "✓ 文檔統計取得成功\n";
            echo "  總文檔數: {$response['data']['total']}\n";
            echo "  已索引文檔: {$response['data']['indexed']}\n";
        }

        echo "\n";
    }

    /**
     * 測試新聞 API
     */
    public function testNews(): void
    {
        echo "4. 測試新聞 API\n";
        echo "---------------\n";

        // 取得最新新聞
        $response = $this->makeRequest('GET', '/api/news/latest', ['limit' => 5]);
        if ($response) {
            echo "✓ 最新新聞取得成功\n";
            echo "  新聞數量: " . count($response['data']['news']) . "\n";
        }

        // 取得熱門新聞
        $response = $this->makeRequest('GET', '/api/news/popular', ['limit' => 5]);
        if ($response) {
            echo "✓ 熱門新聞取得成功\n";
            echo "  新聞數量: " . count($response['data']['news']) . "\n";
        }

        // 取得新聞分類
        $response = $this->makeRequest('GET', '/api/news/categories');
        if ($response) {
            echo "✓ 新聞分類取得成功\n";
            echo "  分類數量: " . count($response['data']) . "\n";
        }

        echo "\n";
    }

    /**
     * 測試統計 API
     */
    public function testStats(): void
    {
        echo "5. 測試統計 API\n";
        echo "---------------\n";

        // 取得系統總覽
        $response = $this->makeRequest('GET', '/api/stats/overview');
        if ($response) {
            echo "✓ 系統總覽取得成功\n";
            echo "  總文檔數: {$response['data']['summary']['total_documents']}\n";
            echo "  總新聞數: {$response['data']['summary']['total_news']}\n";
            echo "  總搜尋數: {$response['data']['summary']['total_searches']}\n";
        }

        // 取得即時統計
        $response = $this->makeRequest('GET', '/api/stats/realtime');
        if ($response) {
            echo "✓ 即時統計取得成功\n";
            echo "  最近1小時搜尋: {$response['data']['realtime_stats']['searches_last_hour']}\n";
        }

        echo "\n";
    }

    /**
     * 測試快取 API
     */
    public function testCache(): void
    {
        echo "6. 測試快取 API\n";
        echo "---------------\n";

        // 取得快取狀態
        $response = $this->makeRequest('GET', '/api/cache/status');
        if ($response) {
            echo "✓ 快取狀態取得成功\n";
            echo "  快取項目數: {$response['data']['statistics']['total_items']}\n";
            echo "  快取大小: {$response['data']['statistics']['total_size']}\n";
            echo "  命中率: {$response['data']['statistics']['hit_rate']}\n";
        }

        // 取得快取項目
        $response = $this->makeRequest('GET', '/api/cache/items', ['limit' => 5]);
        if ($response) {
            echo "✓ 快取項目取得成功\n";
            echo "  項目數量: " . count($response['data']['items']) . "\n";
        }

        echo "\n";
    }

    /**
     * 發送 HTTP 請求
     * 
     * @param string $method
     * @param string $endpoint
     * @param array $params
     * @param array $data
     * @return array|null
     */
    private function makeRequest(string $method, string $endpoint, array $params = [], array $data = []): ?array
    {
        $url = $this->baseUrl . $endpoint;
        
        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        }

        $context = [
            'http' => [
                'method' => $method,
                'header' => [
                    'Content-Type: application/json',
                    'Accept: application/json'
                ],
                'timeout' => 30
            ]
        ];

        if ($this->apiKey) {
            $context['http']['header'][] = 'X-API-Key: ' . $this->apiKey;
        }

        if (!empty($data)) {
            $context['http']['content'] = json_encode($data);
        }

        $context = stream_context_create($context);
        
        try {
            $response = file_get_contents($url, false, $context);
            
            if ($response === false) {
                echo "✗ 請求失敗: {$method} {$endpoint}\n";
                return null;
            }

            $decoded = json_decode($response, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                echo "✗ JSON 解析失敗: {$method} {$endpoint}\n";
                return null;
            }

            if (!$decoded['success']) {
                echo "✗ API 錯誤: {$decoded['message']}\n";
                return null;
            }

            return $decoded;

        } catch (Exception $e) {
            echo "✗ 請求異常: {$e->getMessage()}\n";
            return null;
        }
    }

    /**
     * 展示進階用法範例
     */
    public function showAdvancedExamples(): void
    {
        echo "\n進階用法範例\n";
        echo "============\n\n";

        echo "1. 進階搜尋範例：\n";
        echo "POST /api/search/advanced\n";
        echo "{\n";
        echo "  \"query\": \"machine learning\",\n";
        echo "  \"type\": \"document\",\n";
        echo "  \"date_from\": \"2024-01-01\",\n";
        echo "  \"sort_by\": \"relevance\"\n";
        echo "}\n\n";

        echo "2. 文檔上傳範例：\n";
        echo "POST /api/documents\n";
        echo "Content-Type: multipart/form-data\n";
        echo "document=@/path/to/file.txt\n";
        echo "title=My Document\n";
        echo "description=A sample document\n\n";

        echo "3. 批量操作範例：\n";
        echo "POST /api/documents/batch\n";
        echo "{\n";
        echo "  \"action\": \"update_status\",\n";
        echo "  \"document_ids\": [1, 2, 3],\n";
        echo "  \"data\": {\"status\": \"active\"}\n";
        echo "}\n\n";

        echo "4. 快取預熱範例：\n";
        echo "POST /api/cache/warmup\n";
        echo "{\n";
        echo "  \"endpoints\": [\n";
        echo "    \"/api/news/latest\",\n";
        echo "    \"/api/stats/overview\"\n";
        echo "  ]\n";
        echo "}\n\n";
    }
}

// 主程式
if ($argc > 1) {
    $command = $argv[1];
    $baseUrl = $argv[2] ?? 'http://localhost:8000';
    $apiKey = $argv[3] ?? null;
    
    $examples = new ApiExamples($baseUrl, $apiKey);
    
    switch ($command) {
        case 'all':
            $examples->runAllExamples();
            break;
            
        case 'health':
            $examples->testApiHealth();
            break;
            
        case 'search':
            $examples->testSearch();
            break;
            
        case 'documents':
            $examples->testDocuments();
            break;
            
        case 'news':
            $examples->testNews();
            break;
            
        case 'stats':
            $examples->testStats();
            break;
            
        case 'cache':
            $examples->testCache();
            break;
            
        case 'advanced':
            $examples->showAdvancedExamples();
            break;
            
        default:
            echo "用法: php api_examples.php [command] [base_url] [api_key]\n\n";
            echo "可用命令:\n";
            echo "  all       - 執行所有範例\n";
            echo "  health    - 測試 API 健康狀態\n";
            echo "  search    - 測試搜尋 API\n";
            echo "  documents - 測試文檔 API\n";
            echo "  news      - 測試新聞 API\n";
            echo "  stats     - 測試統計 API\n";
            echo "  cache     - 測試快取 API\n";
            echo "  advanced  - 顯示進階用法範例\n\n";
            echo "範例:\n";
            echo "  php api_examples.php all\n";
            echo "  php api_examples.php search http://localhost:8000\n";
            echo "  php api_examples.php documents http://localhost:8000 your_api_key\n";
            break;
    }
} else {
    $examples = new ApiExamples();
    $examples->runAllExamples();
}