<?php
/**
 * 500 錯誤頁面
 * 當伺服器發生內部錯誤時顯示
 */

$pageTitle = '500 - 伺服器錯誤';
$errorCode = '500';
$errorTitle = '伺服器內部錯誤';
$errorMessage = '抱歉，伺服器發生了一些問題。我們正在努力修復中，請稍後再試。';
?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($pageTitle) ?> - Tech.VG</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;700&display=swap');
        
        body {
            font-family: 'Noto Sans TC', sans-serif;
        }
        
        .error-animation {
            animation: shake 2s ease-in-out infinite;
        }
        
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
        
        .pulse-animation {
            animation: pulse 2s ease-in-out infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .btn-hover {
            transition: all 0.3s ease;
        }
        
        .btn-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center">
    <div class="max-w-2xl mx-auto px-4 text-center">
        <!-- 錯誤圖示 -->
        <div class="error-animation mb-8">
            <div class="inline-flex items-center justify-center w-32 h-32 bg-gradient-to-br from-red-500 to-orange-600 rounded-full shadow-2xl">
                <i class="fas fa-exclamation-triangle text-white text-5xl"></i>
            </div>
        </div>
        
        <!-- 錯誤代碼 -->
        <h1 class="text-8xl font-bold text-gray-800 mb-4"><?= htmlspecialchars($errorCode) ?></h1>
        
        <!-- 錯誤標題 -->
        <h2 class="text-3xl font-semibold text-gray-700 mb-6"><?= htmlspecialchars($errorTitle) ?></h2>
        
        <!-- 錯誤訊息 -->
        <p class="text-lg text-gray-600 mb-8 leading-relaxed">
            <?= htmlspecialchars($errorMessage) ?>
        </p>
        
        <!-- 狀態指示器 -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h3 class="text-xl font-semibold text-gray-800 mb-4">系統狀態</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="flex items-center justify-center p-4 bg-red-50 rounded-lg">
                    <div class="pulse-animation w-3 h-3 bg-red-500 rounded-full mr-3"></div>
                    <span class="text-red-700 font-medium">伺服器異常</span>
                </div>
                <div class="flex items-center justify-center p-4 bg-yellow-50 rounded-lg">
                    <div class="pulse-animation w-3 h-3 bg-yellow-500 rounded-full mr-3"></div>
                    <span class="text-yellow-700 font-medium">正在修復</span>
                </div>
                <div class="flex items-center justify-center p-4 bg-green-50 rounded-lg">
                    <div class="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                    <span class="text-green-700 font-medium">資料庫正常</span>
                </div>
            </div>
        </div>
        
        <!-- 建議操作 -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h3 class="text-xl font-semibold text-gray-800 mb-4">建議您：</h3>
            <ul class="text-left text-gray-600 space-y-2">
                <li class="flex items-center">
                    <i class="fas fa-clock text-blue-500 mr-3"></i>
                    稍等幾分鐘後重新整理頁面
                </li>
                <li class="flex items-center">
                    <i class="fas fa-home text-blue-500 mr-3"></i>
                    返回首頁嘗試其他功能
                </li>
                <li class="flex items-center">
                    <i class="fas fa-envelope text-blue-500 mr-3"></i>
                    如問題持續，請聯繫技術支援
                </li>
                <li class="flex items-center">
                    <i class="fas fa-bookmark text-blue-500 mr-3"></i>
                    將此頁面加入書籤，稍後再試
                </li>
            </ul>
        </div>
        
        <!-- 操作按鈕 -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <button onclick="location.reload()" class="btn-hover inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors">
                <i class="fas fa-sync-alt mr-2"></i>
                重新整理
            </button>
            
            <a href="/" class="btn-hover inline-flex items-center px-6 py-3 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 transition-colors">
                <i class="fas fa-home mr-2"></i>
                返回首頁
            </a>
            
            <a href="/contact" class="btn-hover inline-flex items-center px-6 py-3 bg-orange-600 text-white font-medium rounded-lg hover:bg-orange-700 transition-colors">
                <i class="fas fa-life-ring mr-2"></i>
                技術支援
            </a>
        </div>
        
        <!-- 錯誤ID (用於技術支援) -->
        <div class="mt-8 p-4 bg-gray-100 rounded-lg">
            <p class="text-sm text-gray-500">
                錯誤ID: <span class="font-mono"><?= uniqid('ERR_') ?></span>
                <br>
                時間: <?= date('Y-m-d H:i:s') ?>
            </p>
        </div>
        
        <!-- 返回按鈕 -->
        <div class="mt-6">
            <button onclick="history.back()" class="text-gray-500 hover:text-gray-700 transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>
                返回上一頁
            </button>
        </div>
    </div>
    
    <!-- 背景裝飾 -->
    <div class="fixed inset-0 -z-10 overflow-hidden">
        <div class="absolute -top-40 -right-32 w-80 h-80 bg-red-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse"></div>
        <div class="absolute -bottom-40 -left-32 w-80 h-80 bg-orange-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse"></div>
    </div>
</body>
</html>