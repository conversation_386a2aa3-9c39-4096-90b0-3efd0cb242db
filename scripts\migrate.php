#!/usr/bin/env php
<?php

/**
 * 資料庫遷移腳本
 * 執行資料庫結構建立和初始化
 */

// 設定錯誤報告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 設定時區
date_default_timezone_set('Asia/Taipei');

// 定義應用程式根目錄
define('APP_ROOT', dirname(__DIR__));

// 載入自動載入器
require_once APP_ROOT . '/htdocs/vendor/autoload.php';

// 載入環境變數
if (file_exists(APP_ROOT . '/.env')) {
    $lines = file(APP_ROOT . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            [$key, $value] = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

use App\Database\Database;

try {
    echo "開始資料庫遷移...\n";
    echo "執行時間: " . date('Y-m-d H:i:s') . "\n";
    echo str_repeat('-', 50) . "\n";

    $db = Database::getInstance();
    
    // 讀取schema.sql文件
    $schemaFile = APP_ROOT . '/database/schema.sql';
    
    if (!file_exists($schemaFile)) {
        throw new Exception("Schema文件不存在: {$schemaFile}");
    }
    
    $sql = file_get_contents($schemaFile);
    
    if (!$sql) {
        throw new Exception("無法讀取Schema文件");
    }
    
    echo "讀取Schema文件成功\n";
    
    // 分割SQL語句
    $statements = array_filter(
        array_map('trim', explode(';', $sql)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
        }
    );
    
    echo "發現 " . count($statements) . " 個SQL語句\n";
    
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($statements as $statement) {
        try {
            $db->query($statement);
            $successCount++;
            
            // 顯示執行的語句類型
            if (preg_match('/^\s*(CREATE|DROP|INSERT|ALTER)\s+(\w+)/i', $statement, $matches)) {
                echo "✓ 執行 {$matches[1]} {$matches[2]}\n";
            }
            
        } catch (Exception $e) {
            $errorCount++;
            echo "✗ SQL執行失敗: " . $e->getMessage() . "\n";
            echo "語句: " . substr($statement, 0, 100) . "...\n";
        }
    }
    
    echo str_repeat('-', 50) . "\n";
    echo "遷移完成\n";
    echo "成功: {$successCount} 個語句\n";
    echo "失敗: {$errorCount} 個語句\n";
    
    if ($errorCount > 0) {
        echo "⚠️  部分語句執行失敗，請檢查錯誤訊息\n";
        exit(1);
    } else {
        echo "✓ 所有語句執行成功\n";
    }
    
    // 檢查資料表是否建立成功
    echo "\n檢查資料表...\n";
    $tables = $db->fetchAll("SHOW TABLES");
    
    echo "已建立的資料表:\n";
    foreach ($tables as $table) {
        $tableName = array_values($table)[0];
        echo "  - {$tableName}\n";
    }
    
    echo "\n資料庫遷移完成！\n";
    
} catch (Exception $e) {
    echo "✗ 遷移失敗: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行號: " . $e->getLine() . "\n";
    exit(1);
}