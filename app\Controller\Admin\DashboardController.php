<?php

namespace App\Controller\Admin;

use App\Controller\BaseController;
use App\Service\NewsService;
use App\Service\DocumentService;

/**
 * 管理後台儀表板控制器
 */
class DashboardController extends BaseController
{
    private NewsService $newsService;
    private DocumentService $documentService;

    public function __construct()
    {
        $this->newsService = new NewsService();
        $this->documentService = new DocumentService();
    }

    /**
     * 儀表板首頁
     */
    public function index(): void
    {
        try {
            // 取得統計資料
            $stats = $this->getSystemStats();
            
            $this->setViewData('pageTitle', '管理後台 - Tech.VG');
            $this->setViewData('currentPage', 'admin-dashboard');
            $this->setViewData('stats', $stats);
            
            $this->render('admin/dashboard');
            
        } catch (\Exception $e) {
            $this->logError('Dashboard error: ' . $e->getMessage());
            
            // 顯示簡化的儀表板
            $this->setViewData('pageTitle', '管理後台 - Tech.VG');
            $this->setViewData('currentPage', 'admin-dashboard');
            $this->setViewData('stats', $this->getBasicStats());
            $this->setViewData('error', '部分統計資料無法載入');
            
            $this->render('admin/dashboard');
        }
    }

    /**
     * 取得系統統計資料
     */
    private function getSystemStats(): array
    {
        $stats = [];

        // 新聞統計
        try {
            $newsStats = $this->newsService->getStats();
            $stats['news'] = [
                'total' => $newsStats['total'] ?? 0,
                'today' => $newsStats['today'] ?? 0,
                'this_week' => $newsStats['this_week'] ?? 0,
                'sources' => $newsStats['sources'] ?? 0
            ];
        } catch (\Exception $e) {
            $stats['news'] = ['total' => 0, 'today' => 0, 'this_week' => 0, 'sources' => 0];
        }

        // 文檔統計
        try {
            $docStats = $this->documentService->getStats();
            $stats['documents'] = [
                'total' => $docStats['total'] ?? 0,
                'today' => $docStats['today'] ?? 0,
                'this_week' => $docStats['this_week'] ?? 0,
                'total_size' => $docStats['total_size'] ?? 0
            ];
        } catch (\Exception $e) {
            $stats['documents'] = ['total' => 0, 'today' => 0, 'this_week' => 0, 'total_size' => 0];
        }

        // 系統統計
        $stats['system'] = [
            'php_version' => PHP_VERSION,
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true),
            'uptime' => $this->getSystemUptime(),
            'disk_usage' => $this->getDiskUsage()
        ];

        // 用戶統計（簡化版本）
        $stats['users'] = [
            'total' => 1, // 目前只有管理員
            'active_today' => 1,
            'active_this_week' => 1
        ];

        return $stats;
    }

    /**
     * 取得基本統計資料
     */
    private function getBasicStats(): array
    {
        return [
            'news' => ['total' => 0, 'today' => 0, 'this_week' => 0, 'sources' => 0],
            'documents' => ['total' => 0, 'today' => 0, 'this_week' => 0, 'total_size' => 0],
            'users' => ['total' => 1, 'active_today' => 1, 'active_this_week' => 1],
            'system' => [
                'php_version' => PHP_VERSION,
                'memory_usage' => memory_get_usage(true),
                'memory_peak' => memory_get_peak_usage(true),
                'uptime' => 'N/A',
                'disk_usage' => 'N/A'
            ]
        ];
    }

    /**
     * 取得系統運行時間
     */
    private function getSystemUptime(): string
    {
        if (function_exists('sys_getloadavg') && is_readable('/proc/uptime')) {
            $uptime = file_get_contents('/proc/uptime');
            $uptime = floatval($uptime);
            
            $days = floor($uptime / 86400);
            $hours = floor(($uptime % 86400) / 3600);
            $minutes = floor(($uptime % 3600) / 60);
            
            return "{$days}天 {$hours}小時 {$minutes}分鐘";
        }
        
        return 'N/A';
    }

    /**
     * 取得磁碟使用情況
     */
    private function getDiskUsage(): array
    {
        $path = APP_ROOT;
        
        if (function_exists('disk_total_space') && function_exists('disk_free_space')) {
            $total = disk_total_space($path);
            $free = disk_free_space($path);
            $used = $total - $free;
            
            return [
                'total' => $total,
                'used' => $used,
                'free' => $free,
                'percentage' => round(($used / $total) * 100, 2)
            ];
        }
        
        return [
            'total' => 0,
            'used' => 0,
            'free' => 0,
            'percentage' => 0
        ];
    }
}