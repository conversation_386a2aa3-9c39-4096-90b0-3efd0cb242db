# ChangeLog

All notable changes are documented in this file using the [Keep a CHANGELOG](http://keepachangelog.com/) principles.

## [2.0.4] - 2020-10-26

### Fixed

* `<PERSON><PERSON><PERSON><PERSON><PERSON>\Template\Exception` now correctly extends `\Throwable`

## [2.0.3] - 2020-09-28

### Changed

* Changed PHP version constraint in `composer.json` from `^7.3 || ^8.0` to `>=7.3`

## [2.0.2] - 2020-06-26

### Added

* This component is now supported on PHP 8

## [2.0.1] - 2020-06-15

### Changed

* Tests etc. are now ignored for archive exports

## [2.0.0] - 2020-02-07

### Changed

* The `Text_Template` class was renamed to `<PERSON><PERSON><PERSON><PERSON><PERSON>\Template\Template`

### Removed

* Removed support for PHP 5.3, PHP 5.4, PHP 5.5, PHP 5.6, PHP 7.0, PHP 7.1, and PHP 7.2 

[2.0.4]: https://github.com/sebastian<PERSON>mann/php-text-template/compare/2.0.3...2.0.4
[2.0.3]: https://github.com/sebastian<PERSON>mann/php-text-template/compare/2.0.2...2.0.3
[2.0.2]: https://github.com/sebastianbergmann/php-text-template/compare/2.0.1...2.0.2
[2.0.1]: https://github.com/sebastianbergmann/php-text-template/compare/2.0.0...2.0.1
[2.0.0]: https://github.com/sebastianbergmann/php-text-template/compare/1.2.1...2.0.0
