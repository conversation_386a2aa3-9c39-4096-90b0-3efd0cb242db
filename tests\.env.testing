# 測試環境配置
APP_ENV=testing
APP_DEBUG=true

# 測試資料庫配置
DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=techvg_test
DB_USERNAME=test_user
DB_PASSWORD=test_password

# 測試 API 配置
API_BASE_URL=http://localhost:8000
API_TEST_KEY=test_key_123456

# 快取配置
CACHE_DRIVER=file
CACHE_PREFIX=techvg_test

# 會話配置
SESSION_DRIVER=file
SESSION_LIFETIME=120

# 日誌配置
LOG_CHANNEL=single
LOG_LEVEL=debug

# 檔案上傳配置
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=txt,md,markdown

# 搜尋配置
SEARCH_INDEX_PATH=tests/data/search_index
SEARCH_MAX_RESULTS=100

# 測試配置
TEST_TIMEOUT=30
TEST_MAX_MEMORY=128M
TEST_CONCURRENT_USERS=10