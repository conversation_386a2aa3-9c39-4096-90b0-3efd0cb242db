<?php

namespace App\Service;

use Exception;

/**
 * 排程服務
 * 處理定時任務和背景作業
 */
class ScheduleService extends BaseService
{
    private NewsService $newsService;
    private IndexService $indexService;
    private DocumentService $documentService;
    
    public function __construct()
    {
        parent::__construct();
        $this->newsService = new NewsService();
        $this->indexService = new IndexService();
        $this->documentService = new DocumentService();
    }

    /**
     * 執行所有排程任務
     * 
     * @return array
     */
    public function runScheduledTasks(): array
    {
        $results = [
            'total_tasks' => 0,
            'successful_tasks' => 0,
            'failed_tasks' => 0,
            'task_results' => []
        ];

        $tasks = [
            'news_crawl' => '新聞抓取',
            'index_documents' => '文檔索引',
            'cleanup_old_news' => '清理舊新聞',
            'optimize_index' => '優化索引'
        ];

        foreach ($tasks as $taskMethod => $taskName) {
            $results['total_tasks']++;
            
            try {
                $taskResult = $this->$taskMethod();
                
                if ($taskResult['success']) {
                    $results['successful_tasks']++;
                } else {
                    $results['failed_tasks']++;
                }
                
                $results['task_results'][$taskName] = $taskResult;
                
            } catch (Exception $e) {
                $results['failed_tasks']++;
                $results['task_results'][$taskName] = [
                    'success' => false,
                    'error' => $e->getMessage()
                ];
                
                $this->log('error', "排程任務失敗: {$taskName}", ['error' => $e->getMessage()]);
            }
        }

        $this->log('info', '排程任務執行完成', $results);

        return $this->formatResponse(true, $results, '排程任務執行完成');
    }

    /**
     * 新聞抓取任務
     * 
     * @return array
     */
    public function newsCrawl(): array
    {
        try {
            $this->log('info', '開始執行新聞抓取任務');
            
            $result = $this->newsService->crawlNews();
            
            if ($result['success']) {
                $this->log('info', '新聞抓取任務完成', $result['data']);
            }
            
            return $result;

        } catch (Exception $e) {
            return $this->handleException($e, '新聞抓取任務');
        }
    }

    /**
     * 文檔索引任務
     * 
     * @return array
     */
    public function indexDocuments(): array
    {
        try {
            $this->log('info', '開始執行文檔索引任務');
            
            $result = $this->indexService->batchIndex();
            
            if ($result['success']) {
                $this->log('info', '文檔索引任務完成', $result['data']);
            }
            
            return $result;

        } catch (Exception $e) {
            return $this->handleException($e, '文檔索引任務');
        }
    }

    /**
     * 清理舊新聞任務
     * 
     * @return array
     */
    public function cleanupOldNews(): array
    {
        try {
            $this->log('info', '開始執行舊新聞清理任務');
            
            $retentionDays = $this->config['news']['retention_days'] ?? 90;
            $result = $this->newsService->cleanupOldNews($retentionDays);
            
            if ($result['success']) {
                $this->log('info', '舊新聞清理任務完成', $result['data']);
            }
            
            return $result;

        } catch (Exception $e) {
            return $this->handleException($e, '舊新聞清理任務');
        }
    }

    /**
     * 優化索引任務
     * 
     * @return array
     */
    public function optimizeIndex(): array
    {
        try {
            $this->log('info', '開始執行索引優化任務');
            
            $result = $this->indexService->optimizeIndex();
            
            if ($result['success']) {
                $this->log('info', '索引優化任務完成', $result['data']);
            }
            
            return $result;

        } catch (Exception $e) {
            return $this->handleException($e, '索引優化任務');
        }
    }

    /**
     * 檢查任務執行條件
     * 
     * @param string $taskName
     * @return bool
     */
    public function shouldRunTask(string $taskName): bool
    {
        try {
            $lastRun = $this->getLastTaskRun($taskName);
            
            if (!$lastRun) {
                return true; // 從未執行過
            }

            $intervals = [
                'news_crawl' => 3600,      // 1小時
                'index_documents' => 1800,  // 30分鐘
                'cleanup_old_news' => 86400, // 1天
                'optimize_index' => 604800   // 1週
            ];

            $interval = $intervals[$taskName] ?? 3600;
            $nextRun = strtotime($lastRun) + $interval;

            return time() >= $nextRun;

        } catch (Exception $e) {
            $this->log('error', '檢查任務執行條件失敗', ['task' => $taskName, 'error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * 記錄任務執行
     * 
     * @param string $taskName
     * @param bool $success
     * @param array $result
     */
    public function recordTaskRun(string $taskName, bool $success, array $result = []): void
    {
        try {
            $sql = "
                INSERT INTO taskLog (taskName, status, result, executedAt)
                VALUES (?, ?, ?, NOW())
                ON DUPLICATE KEY UPDATE
                status = VALUES(status),
                result = VALUES(result),
                executedAt = VALUES(executedAt)
            ";

            $this->db->query($sql, [
                $taskName,
                $success ? 'success' : 'failed',
                json_encode($result, JSON_UNESCAPED_UNICODE)
            ]);

        } catch (Exception $e) {
            $this->log('error', '記錄任務執行失敗', ['task' => $taskName, 'error' => $e->getMessage()]);
        }
    }

    /**
     * 取得最後任務執行時間
     * 
     * @param string $taskName
     * @return string|null
     */
    private function getLastTaskRun(string $taskName): ?string
    {
        try {
            $sql = "SELECT executedAt FROM taskLog WHERE taskName = ? ORDER BY executedAt DESC LIMIT 1";
            $result = $this->db->fetchOne($sql, [$taskName]);
            
            return $result['executedAt'] ?? null;

        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * 取得任務執行歷史
     * 
     * @param string|null $taskName
     * @param int $limit
     * @return array
     */
    public function getTaskHistory(?string $taskName = null, int $limit = 50): array
    {
        try {
            $sql = "SELECT * FROM taskLog";
            $params = [];
            
            if ($taskName) {
                $sql .= " WHERE taskName = ?";
                $params[] = $taskName;
            }
            
            $sql .= " ORDER BY executedAt DESC LIMIT ?";
            $params[] = $limit;

            $history = $this->db->fetchAll($sql, $params);

            return $this->formatResponse(true, $history);

        } catch (Exception $e) {
            return $this->handleException($e, '取得任務執行歷史');
        }
    }
}