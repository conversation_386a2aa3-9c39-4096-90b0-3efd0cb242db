<?php

namespace Tests\Integration;

use Tests\TestCase;

/**
 * 控制器整合測試
 * 測試控制器與服務層的整合
 */
class ControllerIntegrationTest extends TestCase
{
    private string $baseUrl;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->baseUrl = $this->testConfig['app']['base_url'] ?? 'http://localhost:8000';
        
        // 建立測試資料
        $this->createIntegrationTestData();
    }

    /**
     * 建立整合測試資料
     */
    private function createIntegrationTestData(): void
    {
        // 建立測試文檔
        $this->createTestDocument([
            'title' => '整合測試文檔 1',
            'content' => '這是整合測試的文檔內容，包含機器學習相關資訊。',
            'file_type' => 'txt',
            'category' => 'ai'
        ]);

        $this->createTestDocument([
            'title' => '整合測試文檔 2',
            'content' => '這是關於區塊鏈技術的文檔，詳細介紹了分散式帳本。',
            'file_type' => 'md',
            'category' => 'blockchain'
        ]);

        // 建立測試新聞
        $this->createTestNews([
            'title' => '整合測試新聞 1',
            'summary' => 'AI 技術在醫療領域的最新應用',
            'content' => '詳細內容...',
            'category' => 'ai',
            'source' => 'TechNews'
        ]);

        $this->createTestNews([
            'title' => '整合測試新聞 2',
            'summary' => '區塊鏈在金融業的創新應用',
            'content' => '詳細內容...',
            'category' => 'blockchain',
            'source' => 'FinTech Today'
        ]);
    }

    /**
     * 測試首頁控制器整合
     */
    public function testHomeControllerIntegration(): void
    {
        $response = $this->makeRequest('GET', '/');

        // 驗證回應
        $this->assertEquals(200, $response['status']);
        $this->assertStringContainsString('整合測試新聞', $response['body']);
        $this->assertStringContainsString('整合測試文檔', $response['body']);
    }

    /**
     * 測試文檔控制器整合
     */
    public function testDocumentControllerIntegration(): void
    {
        // 測試文檔列表
        $response = $this->makeRequest('GET', '/documents');
        
        $this->assertEquals(200, $response['status']);
        $this->assertStringContainsString('整合測試文檔', $response['body']);

        // 測試文檔詳情
        $documentId = $this->getTestDocumentId();
        $response = $this->makeRequest('GET', "/documents/{$documentId}");
        
        $this->assertEquals(200, $response['status']);
        $this->assertStringContainsString('機器學習', $response['body']);
    }

    /**
     * 測試新聞控制器整合
     */
    public function testNewsControllerIntegration(): void
    {
        // 測試新聞列表
        $response = $this->makeRequest('GET', '/news');
        
        $this->assertEquals(200, $response['status']);
        $this->assertStringContainsString('整合測試新聞', $response['body']);

        // 測試新聞詳情
        $newsId = $this->getTestNewsId();
        $response = $this->makeRequest('GET', "/news/{$newsId}");
        
        $this->assertEquals(200, $response['status']);
        $this->assertStringContainsString('AI 技術', $response['body']);
    }

    /**
     * 測試搜尋控制器整合
     */
    public function testSearchControllerIntegration(): void
    {
        // 測試搜尋頁面
        $response = $this->makeRequest('GET', '/search');
        
        $this->assertEquals(200, $response['status']);
        $this->assertStringContainsString('搜尋', $response['body']);

        // 測試搜尋功能
        $response = $this->makeRequest('GET', '/search?q=機器學習');
        
        $this->assertEquals(200, $response['status']);
        $this->assertStringContainsString('整合測試文檔 1', $response['body']);
    }

    /**
     * 測試 API 控制器整合
     */
    public function testApiControllerIntegration(): void
    {
        // 測試文檔 API
        $response = $this->makeApiRequest('GET', '/api/documents');
        
        $this->assertEquals(200, $response['status']);
        $data = json_decode($response['body'], true);
        $this->assertTrue($data['success']);
        $this->assertArrayHasKey('data', $data);

        // 測試新聞 API
        $response = $this->makeApiRequest('GET', '/api/news');
        
        $this->assertEquals(200, $response['status']);
        $data = json_decode($response['body'], true);
        $this->assertTrue($data['success']);
        $this->assertArrayHasKey('data', $data);

        // 測試搜尋 API
        $response = $this->makeApiRequest('GET', '/api/search?q=區塊鏈');
        
        $this->assertEquals(200, $response['status']);
        $data = json_decode($response['body'], true);
        $this->assertTrue($data['success']);
        $this->assertGreaterThan(0, count($data['data']['results']));
    }

    /**
     * 測試錯誤處理整合
     */
    public function testErrorHandlingIntegration(): void
    {
        // 測試 404 錯誤
        $response = $this->makeRequest('GET', '/nonexistent-page');
        
        $this->assertEquals(404, $response['status']);
        $this->assertStringContainsString('404', $response['body']);

        // 測試 API 404 錯誤
        $response = $this->makeApiRequest('GET', '/api/nonexistent');
        
        $this->assertEquals(404, $response['status']);
        $data = json_decode($response['body'], true);
        $this->assertFalse($data['success']);
        $this->assertEquals(404, $data['error']['code']);
    }

    /**
     * 測試表單提交整合
     */
    public function testFormSubmissionIntegration(): void
    {
        // 測試聯絡表單提交
        $formData = [
            'name' => '測試用戶',
            'email' => '<EMAIL>',
            'subject' => '整合測試',
            'message' => '這是整合測試的訊息'
        ];

        $response = $this->makeRequest('POST', '/contact', $formData);
        
        $this->assertEquals(200, $response['status']);
        $this->assertStringContainsString('感謝', $response['body']);
    }

    /**
     * 測試檔案上傳整合
     */
    public function testFileUploadIntegration(): void
    {
        // 建立測試檔案
        $testFile = $this->createTestFile('integration_test.txt', '整合測試檔案內容');

        $uploadData = [
            'title' => '整合測試上傳檔案',
            'category' => 'test',
            'file' => $testFile
        ];

        $response = $this->makeRequest('POST', '/documents/upload', $uploadData);
        
        // 驗證上傳成功
        $this->assertEquals(200, $response['status']);
        $this->assertStringContainsString('上傳成功', $response['body']);

        // 清理測試檔案
        if (file_exists($testFile)) {
            unlink($testFile);
        }
    }

    /**
     * 測試分頁功能整合
     */
    public function testPaginationIntegration(): void
    {
        // 建立更多測試資料
        for ($i = 3; $i <= 25; $i++) {
            $this->createTestDocument([
                'title' => "分頁測試文檔 {$i}",
                'content' => "分頁測試內容 {$i}",
                'file_type' => 'txt'
            ]);
        }

        // 測試第一頁
        $response = $this->makeApiRequest('GET', '/api/documents?page=1&limit=10');
        
        $this->assertEquals(200, $response['status']);
        $data = json_decode($response['body'], true);
        $this->assertEquals(10, count($data['data']['items']));
        $this->assertArrayHasKey('pagination', $data['data']);

        // 測試第二頁
        $response = $this->makeApiRequest('GET', '/api/documents?page=2&limit=10');
        
        $this->assertEquals(200, $response['status']);
        $data = json_decode($response['body'], true);
        $this->assertGreaterThan(0, count($data['data']['items']));
    }

    /**
     * 測試快取整合
     */
    public function testCacheIntegration(): void
    {
        // 第一次請求（應該建立快取）
        $start1 = microtime(true);
        $response1 = $this->makeApiRequest('GET', '/api/documents');
        $time1 = microtime(true) - $start1;

        $this->assertEquals(200, $response1['status']);

        // 第二次請求（應該使用快取）
        $start2 = microtime(true);
        $response2 = $this->makeApiRequest('GET', '/api/documents');
        $time2 = microtime(true) - $start2;

        $this->assertEquals(200, $response2['status']);
        
        // 快取的請求應該更快
        $this->assertLessThan($time1, $time2);
        
        // 回應內容應該相同
        $this->assertEquals($response1['body'], $response2['body']);
    }

    /**
     * 測試搜尋索引整合
     */
    public function testSearchIndexIntegration(): void
    {
        // 建立新文檔
        $documentId = $this->createTestDocument([
            'title' => '搜尋索引測試文檔',
            'content' => '這是用於測試搜尋索引的特殊內容，包含關鍵字：量子計算',
            'file_type' => 'txt'
        ]);

        // 等待索引更新
        sleep(1);

        // 搜尋新建立的文檔
        $response = $this->makeApiRequest('GET', '/api/search?q=量子計算');
        
        $this->assertEquals(200, $response['status']);
        $data = json_decode($response['body'], true);
        $this->assertTrue($data['success']);
        
        // 驗證搜尋結果包含新文檔
        $found = false;
        foreach ($data['data']['results'] as $result) {
            if ($result['id'] == $documentId) {
                $found = true;
                break;
            }
        }
        $this->assertTrue($found, '新建立的文檔應該出現在搜尋結果中');
    }

    /**
     * 測試統計資料整合
     */
    public function testStatisticsIntegration(): void
    {
        // 取得統計資料
        $response = $this->makeApiRequest('GET', '/api/stats/overview');
        
        $this->assertEquals(200, $response['status']);
        $data = json_decode($response['body'], true);
        $this->assertTrue($data['success']);
        
        $stats = $data['data'];
        $this->assertArrayHasKey('total_documents', $stats);
        $this->assertArrayHasKey('total_news', $stats);
        $this->assertGreaterThan(0, $stats['total_documents']);
        $this->assertGreaterThan(0, $stats['total_news']);
    }

    /**
     * 測試多語言支援整合
     */
    public function testMultiLanguageIntegration(): void
    {
        // 測試中文介面
        $response = $this->makeRequest('GET', '/', [], ['Accept-Language' => 'zh-TW']);
        
        $this->assertEquals(200, $response['status']);
        $this->assertStringContainsString('文檔', $response['body']);

        // 測試英文介面
        $response = $this->makeRequest('GET', '/', [], ['Accept-Language' => 'en-US']);
        
        $this->assertEquals(200, $response['status']);
        // 如果有英文支援，應該包含英文內容
    }

    /**
     * 測試 API 版本控制整合
     */
    public function testApiVersioningIntegration(): void
    {
        // 測試 v1 API
        $response = $this->makeApiRequest('GET', '/api/v1/documents');
        
        $this->assertEquals(200, $response['status']);
        $this->assertStringContainsString('v1', $response['headers']['X-API-Version'] ?? '');

        // 測試預設 API（應該是最新版本）
        $response = $this->makeApiRequest('GET', '/api/documents');
        
        $this->assertEquals(200, $response['status']);
    }

    /**
     * 發送 HTTP 請求
     */
    private function makeRequest(string $method, string $path, array $data = [], array $headers = []): array
    {
        $url = $this->baseUrl . $path;
        
        $context = [
            'http' => [
                'method' => $method,
                'header' => $this->buildHeaders($headers),
                'content' => $method === 'POST' ? http_build_query($data) : null,
                'ignore_errors' => true
            ]
        ];

        if ($method === 'GET' && !empty($data)) {
            $url .= '?' . http_build_query($data);
        }

        $response = file_get_contents($url, false, stream_context_create($context));
        
        return [
            'status' => $this->getHttpResponseCode($http_response_header ?? []),
            'headers' => $this->parseHeaders($http_response_header ?? []),
            'body' => $response ?: ''
        ];
    }

    /**
     * 發送 API 請求
     */
    private function makeApiRequest(string $method, string $path, array $data = []): array
    {
        $headers = [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json'
        ];

        if ($method === 'POST' || $method === 'PUT') {
            $data = json_encode($data);
        }

        return $this->makeRequest($method, $path, $data, $headers);
    }

    /**
     * 建構 HTTP 標頭
     */
    private function buildHeaders(array $headers): string
    {
        $headerStrings = [];
        foreach ($headers as $key => $value) {
            $headerStrings[] = "{$key}: {$value}";
        }
        return implode("\r\n", $headerStrings);
    }

    /**
     * 取得 HTTP 回應狀態碼
     */
    private function getHttpResponseCode(array $headers): int
    {
        if (empty($headers)) {
            return 200;
        }

        $statusLine = $headers[0];
        if (preg_match('/HTTP\/\d\.\d\s+(\d+)/', $statusLine, $matches)) {
            return (int)$matches[1];
        }

        return 200;
    }

    /**
     * 解析 HTTP 標頭
     */
    private function parseHeaders(array $headers): array
    {
        $parsed = [];
        foreach ($headers as $header) {
            if (strpos($header, ':') !== false) {
                [$key, $value] = explode(':', $header, 2);
                $parsed[trim($key)] = trim($value);
            }
        }
        return $parsed;
    }

    /**
     * 取得測試文檔 ID
     */
    private function getTestDocumentId(): int
    {
        // 這裡應該從資料庫查詢測試文檔的 ID
        // 為了簡化，返回固定值
        return 1;
    }

    /**
     * 取得測試新聞 ID
     */
    private function getTestNewsId(): int
    {
        // 這裡應該從資料庫查詢測試新聞的 ID
        // 為了簡化，返回固定值
        return 1;
    }

    /**
     * 建立測試檔案
     */
    private function createTestFile(string $filename, string $content): string
    {
        $filepath = $this->testDataDir . '/' . $filename;
        file_put_contents($filepath, $content);
        return $filepath;
    }

    protected function tearDown(): void
    {
        // 清理測試資料
        $this->cleanupTestData();
        
        parent::tearDown();
    }
}