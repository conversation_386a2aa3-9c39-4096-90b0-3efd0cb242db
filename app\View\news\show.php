<?php
/**
 * 新聞詳情頁面
 */

$news = $news ?? [];
$error = $error ?? '';

include __DIR__ . '/../layout/header.php';
?>

<?php if ($error): ?>
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="bg-red-50 border border-red-200 rounded-md p-4">
            <div class="flex">
                <i class="fas fa-exclamation-circle text-red-400 mr-3 mt-1"></i>
                <div class="text-red-700"><?= htmlspecialchars($error) ?></div>
            </div>
        </div>
    </div>
<?php else: ?>
    <article class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 返回按鈕 -->
        <div class="mb-6">
            <a href="/news" class="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>返回新聞列表
            </a>
        </div>

        <!-- 新聞標題 -->
        <header class="mb-8">
            <h1 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                <?= htmlspecialchars($news['title'] ?? '') ?>
            </h1>

            <!-- 新聞元資訊 -->
            <div class="flex flex-wrap items-center text-gray-600 text-sm space-x-4 mb-6">
                <div class="flex items-center">
                    <i class="fas fa-clock mr-2"></i>
                    <?= date('Y年m月d日 H:i', strtotime($news['published_at'] ?? 'now')) ?>
                </div>

                <?php if (!empty($news['source_name'])): ?>
                    <div class="flex items-center">
                        <i class="fas fa-globe mr-2"></i>
                        <?= htmlspecialchars($news['source_name']) ?>
                    </div>
                <?php endif; ?>

                <?php if (!empty($news['author'])): ?>
                    <div class="flex items-center">
                        <i class="fas fa-user mr-2"></i>
                        <?= htmlspecialchars($news['author']) ?>
                    </div>
                <?php endif; ?>

                <div class="flex items-center">
                    <i class="fas fa-eye mr-2"></i>
                    <?= number_format($news['views'] ?? 0) ?> 次瀏覽
                </div>

                <?php if (!empty($news['category'])): ?>
                    <span class="px-3 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">
                        <?= htmlspecialchars($news['category']) ?>
                    </span>
                <?php endif; ?>
            </div>

            <!-- 分享按鈕 -->
            <div class="flex items-center space-x-3 mb-6">
                <span class="text-gray-600 text-sm">分享：</span>
                <button onclick="shareToFacebook()" class="text-blue-600 hover:text-blue-800 transition-colors">
                    <i class="fab fa-facebook-f"></i>
                </button>
                <button onclick="shareToTwitter()" class="text-blue-400 hover:text-blue-600 transition-colors">
                    <i class="fab fa-twitter"></i>
                </button>
                <button onclick="shareToLinkedIn()" class="text-blue-700 hover:text-blue-900 transition-colors">
                    <i class="fab fa-linkedin-in"></i>
                </button>
                <button onclick="copyLink()" class="text-gray-600 hover:text-gray-800 transition-colors">
                    <i class="fas fa-link"></i>
                </button>
            </div>
        </header>

        <!-- 新聞圖片 -->
        <?php if (!empty($news['image_url'])): ?>
            <div class="mb-8">
                <img src="<?= htmlspecialchars($news['image_url']) ?>" 
                     alt="<?= htmlspecialchars($news['title'] ?? '') ?>"
                     class="w-full h-64 md:h-96 object-cover rounded-lg shadow-lg">
            </div>
        <?php endif; ?>

        <!-- 新聞摘要 -->
        <?php if (!empty($news['summary'])): ?>
            <div class="bg-blue-50 border-l-4 border-blue-400 p-6 mb-8">
                <h2 class="text-lg font-semibold text-gray-900 mb-2">摘要</h2>
                <p class="text-gray-700 leading-relaxed">
                    <?= nl2br(htmlspecialchars($news['summary'])) ?>
                </p>
            </div>
        <?php endif; ?>

        <!-- 新聞內容 -->
        <div class="prose prose-lg max-w-none mb-8">
            <?php if (!empty($news['content'])): ?>
                <div class="text-gray-800 leading-relaxed">
                    <?= nl2br(htmlspecialchars($news['content'])) ?>
                </div>
            <?php else: ?>
                <div class="text-center py-8 text-gray-500">
                    <i class="fas fa-file-alt text-4xl mb-4"></i>
                    <p>新聞內容暫時無法顯示</p>
                </div>
            <?php endif; ?>
        </div>

        <!-- 原文連結 -->
        <?php if (!empty($news['url'])): ?>
            <div class="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-8">
                <h3 class="text-lg font-semibold text-gray-900 mb-3">原文連結</h3>
                <a href="<?= htmlspecialchars($news['url']) ?>" 
                   target="_blank" 
                   rel="noopener noreferrer"
                   class="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors">
                    <i class="fas fa-external-link-alt mr-2"></i>
                    查看原始文章
                </a>
            </div>
        <?php endif; ?>

        <!-- 標籤 -->
        <?php if (!empty($news['tags'])): ?>
            <div class="mb-8">
                <h3 class="text-lg font-semibold text-gray-900 mb-3">相關標籤</h3>
                <div class="flex flex-wrap gap-2">
                    <?php foreach (explode(',', $news['tags']) as $tag): ?>
                        <span class="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full">
                            #<?= htmlspecialchars(trim($tag)) ?>
                        </span>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>

        <!-- 相關操作 -->
        <div class="border-t border-gray-200 pt-8">
            <div class="flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0">
                <div class="flex items-center space-x-4">
                    <button onclick="window.print()" 
                            class="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 bg-white rounded-md hover:bg-gray-50 transition-colors">
                        <i class="fas fa-print mr-2"></i>列印
                    </button>
                    
                    <button onclick="addToBookmarks()" 
                            class="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 bg-white rounded-md hover:bg-gray-50 transition-colors">
                        <i class="fas fa-bookmark mr-2"></i>收藏
                    </button>
                </div>

                <div class="flex items-center space-x-4">
                    <a href="/news" 
                       class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                        <i class="fas fa-list mr-2"></i>更多新聞
                    </a>
                </div>
            </div>
        </div>
    </article>
<?php endif; ?>

<script>
    function shareToFacebook() {
        const url = encodeURIComponent(window.location.href);
        const title = encodeURIComponent(document.title);
        window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}`, '_blank', 'width=600,height=400');
    }

    function shareToTwitter() {
        const url = encodeURIComponent(window.location.href);
        const title = encodeURIComponent(document.title);
        window.open(`https://twitter.com/intent/tweet?url=${url}&text=${title}`, '_blank', 'width=600,height=400');
    }

    function shareToLinkedIn() {
        const url = encodeURIComponent(window.location.href);
        const title = encodeURIComponent(document.title);
        window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${url}`, '_blank', 'width=600,height=400');
    }

    function copyLink() {
        navigator.clipboard.writeText(window.location.href).then(() => {
            showNotification('連結已複製到剪貼簿', 'success');
        }).catch(() => {
            showNotification('複製失敗，請手動複製連結', 'error');
        });
    }

    function addToBookmarks() {
        if (window.sidebar && window.sidebar.addPanel) {
            // Firefox
            window.sidebar.addPanel(document.title, window.location.href, '');
        } else if (window.external && ('AddFavorite' in window.external)) {
            // Internet Explorer
            window.external.AddFavorite(window.location.href, document.title);
        } else if (window.opera && window.print) {
            // Opera
            const elem = document.createElement('a');
            elem.setAttribute('href', window.location.href);
            elem.setAttribute('title', document.title);
            elem.setAttribute('rel', 'sidebar');
            elem.click();
        } else {
            // Other browsers
            showNotification('請使用 Ctrl+D (Windows) 或 Cmd+D (Mac) 將此頁面加入書籤', 'info');
        }
    }
</script>

<?php include __DIR__ . '/../layout/footer.php'; ?>