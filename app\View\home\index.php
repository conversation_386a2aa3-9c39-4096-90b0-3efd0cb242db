<?php
/**
 * 首頁視圖
 * 顯示網站首頁內容
 */

// 設定預設值
$latestNews = $latestNews ?? [];
$popularSearches = $popularSearches ?? [];
$popularNews = $popularNews ?? [];
$statistics = $statistics ?? [];
$csrfToken = $csrfToken ?? '';

include __DIR__ . '/../layout/header.php';
?>

<!-- 英雄區塊 -->
<section class="gradient-bg py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-4xl md:text-6xl font-bold text-white mb-6">
            探索科技新知
        </h1>
        <p class="text-xl md:text-2xl text-blue-100 mb-8 max-w-3xl mx-auto">
            專業的科技資訊檢索平台，提供最新科技新聞、技術文檔管理和智能檢索功能
        </p>
        
        <!-- 主搜尋欄 -->
        <div class="max-w-2xl mx-auto">
            <form action="/search" method="GET" class="relative">
                <div class="flex">
                    <input type="text" 
                           name="q" 
                           class="flex-1 px-6 py-4 text-lg border-0 rounded-l-lg focus:ring-2 focus:ring-blue-300 focus:outline-none" 
                           placeholder="搜尋科技新聞、技術文檔..."
                           autocomplete="off">
                    <button type="submit" 
                            class="px-8 py-4 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-r-lg transition-colors">
                        <i class="fas fa-search mr-2"></i>搜尋
                    </button>
                </div>
            </form>
            
            <!-- 快速檢索建議 -->
            <?php if (!empty($popularSearches)): ?>
                <div class="mt-4 text-blue-100">
                    <span class="text-sm">熱門搜尋：</span>
                    <?php foreach (array_slice($popularSearches, 0, 5) as $search): ?>
                        <a href="/search?q=<?= urlencode($search['query']) ?>" 
                           class="inline-block mx-1 px-3 py-1 bg-blue-500 bg-opacity-30 rounded-full text-sm hover:bg-opacity-50 transition-colors">
                            <?= htmlspecialchars($search['query']) ?>
                        </a>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section>

<!-- 統計資訊 -->
<?php if (!empty($statistics)): ?>
<section class="py-12 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div class="text-center">
                <div class="text-3xl font-bold text-blue-600 mb-2">
                    <?= number_format($statistics['totalDocuments'] ?? 0) ?>
                </div>
                <div class="text-gray-600">文檔總數</div>
            </div>
            <div class="text-center">
                <div class="text-3xl font-bold text-purple-600 mb-2">
                    <?= number_format($statistics['totalNews'] ?? 0) ?>
                </div>
                <div class="text-gray-600">新聞文章</div>
            </div>
            <div class="text-center">
                <div class="text-3xl font-bold text-green-600 mb-2">
                    <?= number_format($statistics['totalSearches'] ?? 0) ?>
                </div>
                <div class="text-gray-600">檢索次數</div>
            </div>
            <div class="text-center">
                <div class="text-3xl font-bold text-orange-600 mb-2">
                    <?= number_format($statistics['totalWords'] ?? 0) ?>
                </div>
                <div class="text-gray-600">索引詞彙</div>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- 最新科技新聞 -->
<?php if (!empty($latestNews)): ?>
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center mb-8">
            <h2 class="text-3xl font-bold text-gray-900">最新科技新聞</h2>
            <a href="/news" class="text-blue-600 hover:text-blue-800 font-medium">
                查看更多 <i class="fas fa-arrow-right ml-1"></i>
            </a>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php foreach (array_slice($latestNews, 0, 6) as $news): ?>
                <article class="card-hover bg-white rounded-lg shadow-md overflow-hidden">
                    <?php if (!empty($news['image_url'])): ?>
                        <img src="<?= htmlspecialchars($news['image_url']) ?>" 
                             alt="<?= htmlspecialchars($news['title']) ?>"
                             class="w-full h-48 object-cover">
                    <?php else: ?>
                        <div class="w-full h-48 bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                            <i class="fas fa-newspaper text-white text-4xl"></i>
                        </div>
                    <?php endif; ?>
                    
                    <div class="p-6">
                        <div class="flex items-center text-sm text-gray-500 mb-2">
                            <i class="fas fa-clock mr-1"></i>
                            <?= date('Y-m-d H:i', strtotime($news['published_at'] ?? 'now')) ?>
                            <?php if (!empty($news['source_name'])): ?>
                                <span class="mx-2">•</span>
                                <span><?= htmlspecialchars($news['source_name']) ?></span>
                            <?php endif; ?>
                        </div>
                        
                        <h3 class="text-lg font-semibold text-gray-900 mb-3 line-clamp-2">
                            <a href="/news/<?= $news['id'] ?>" class="hover:text-blue-600 transition-colors">
                                <?= htmlspecialchars($news['title']) ?>
                            </a>
                        </h3>
                        
                        <?php if (!empty($news['summary'])): ?>
                            <p class="text-gray-600 text-sm line-clamp-3 mb-4">
                                <?= htmlspecialchars($news['summary']) ?>
                            </p>
                        <?php endif; ?>
                        
                        <div class="flex justify-between items-center">
                            <a href="/news/<?= $news['id'] ?>" 
                               class="text-blue-600 hover:text-blue-800 font-medium text-sm">
                                閱讀更多
                            </a>
                            <?php if (!empty($news['category'])): ?>
                                <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                                    <?= htmlspecialchars($news['category']) ?>
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>
                </article>
            <?php endforeach; ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- 功能特色 -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">平台特色</h2>
            <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                提供全方位的科技資訊服務，讓您輕鬆掌握最新科技動態
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <!-- 智能檢索 -->
            <div class="text-center p-6">
                <div class="w-16 h-16 gradient-bg rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-search text-white text-2xl"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-3">智能檢索</h3>
                <p class="text-gray-600">
                    先進的全文檢索技術，支援模糊搜尋、關鍵字高亮和相關性排序，快速找到您需要的資訊。
                </p>
                <a href="/search" class="inline-block mt-4 text-blue-600 hover:text-blue-800 font-medium">
                    立即體驗 <i class="fas fa-arrow-right ml-1"></i>
                </a>
            </div>
            
            <!-- 文檔管理 -->
            <div class="text-center p-6">
                <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-teal-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-file-alt text-white text-2xl"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-3">文檔管理</h3>
                <p class="text-gray-600">
                    支援多種格式的技術文檔上傳、管理和分享，自動建立索引，方便檢索和管理。
                </p>
                <a href="/documents" class="inline-block mt-4 text-green-600 hover:text-green-800 font-medium">
                    管理文檔 <i class="fas fa-arrow-right ml-1"></i>
                </a>
            </div>
            
            <!-- 即時新聞 -->
            <div class="text-center p-6">
                <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-newspaper text-white text-2xl"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-3">即時新聞</h3>
                <p class="text-gray-600">
                    聚合多個科技媒體的最新資訊，提供分類瀏覽和個人化推薦，掌握科技脈動。
                </p>
                <a href="/news" class="inline-block mt-4 text-orange-600 hover:text-orange-800 font-medium">
                    瀏覽新聞 <i class="fas fa-arrow-right ml-1"></i>
                </a>
            </div>
        </div>
    </div>
</section>

<!-- 熱門新聞 -->
<?php if (!empty($popularNews)): ?>
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center mb-8">
            <h2 class="text-3xl font-bold text-gray-900">熱門新聞</h2>
            <a href="/news" class="text-blue-600 hover:text-blue-800 font-medium">
                查看更多 <i class="fas fa-arrow-right ml-1"></i>
            </a>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <?php foreach (array_slice($popularNews, 0, 4) as $index => $news): ?>
                <article class="flex bg-white rounded-lg shadow-md overflow-hidden card-hover">
                    <div class="flex-shrink-0 w-24 h-24 bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                        <span class="text-white text-2xl font-bold"><?= $index + 1 ?></span>
                    </div>
                    <div class="flex-1 p-4">
                        <h3 class="font-semibold text-gray-900 mb-2 line-clamp-2">
                            <a href="/news/<?= $news['id'] ?>" class="hover:text-blue-600 transition-colors">
                                <?= htmlspecialchars($news['title']) ?>
                            </a>
                        </h3>
                        <div class="flex items-center text-sm text-gray-500">
                            <i class="fas fa-eye mr-1"></i>
                            <?= number_format($news['views'] ?? 0) ?> 次瀏覽
                            <span class="mx-2">•</span>
                            <?= date('m-d', strtotime($news['published_at'] ?? 'now')) ?>
                        </div>
                    </div>
                </article>
            <?php endforeach; ?>
        </div>
    </div>
</section>
<?php endif; ?>

<style>
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
    
    .line-clamp-3 {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
</style>

<?php include __DIR__ . '/../layout/footer.php'; ?>