-- Tech.VG 科技新知檢索網站資料庫結構
-- 建立時間: 2024
-- 資料庫編碼: utf8mb4_unicode_ci

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 文檔管理相關資料表
-- ----------------------------

-- 文檔基本資訊表
DROP TABLE IF EXISTS `document`;
CREATE TABLE `document` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '文檔唯一識別碼',
  `title` varchar(255) NOT NULL COMMENT '文檔標題',
  `fileName` varchar(255) NOT NULL COMMENT '原始檔案名稱',
  `fileType` enum('txt','markdown') NOT NULL COMMENT '檔案類型',
  `fileSize` bigint(20) NOT NULL COMMENT '檔案大小(位元組)',
  `filePath` varchar(500) NOT NULL COMMENT '檔案儲存路徑',
  `uploadedBy` int(11) DEFAULT NULL COMMENT '上傳者用戶ID',
  `status` enum('processing','indexed','failed') DEFAULT 'processing' COMMENT '處理狀態',
  `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '建立時間',
  `updatedAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新時間',
  PRIMARY KEY (`id`),
  KEY `idx_document_status` (`status`),
  KEY `idx_document_created` (`createdAt`),
  KEY `idx_document_uploaded_by` (`uploadedBy`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文檔基本資訊表，儲存上傳文檔的元資料';

-- 文檔內容表
DROP TABLE IF EXISTS `documentContent`;
CREATE TABLE `documentContent` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '內容唯一識別碼',
  `documentId` int(11) NOT NULL COMMENT '關聯文檔ID',
  `content` longtext NOT NULL COMMENT '文檔內容',
  `contentHash` varchar(64) NOT NULL COMMENT '內容雜湊值',
  `wordCount` int(11) DEFAULT 0 COMMENT '字數統計',
  `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '建立時間',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_document_content` (`documentId`),
  KEY `idx_content_hash` (`contentHash`),
  CONSTRAINT `fk_document_content_document` FOREIGN KEY (`documentId`) REFERENCES `document` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文檔內容表，儲存文檔的完整內容';

-- 文檔索引表
DROP TABLE IF EXISTS `documentIndex`;
CREATE TABLE `documentIndex` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '索引唯一識別碼',
  `documentId` int(11) NOT NULL COMMENT '關聯文檔ID',
  `chunkIndex` int(11) NOT NULL COMMENT '文檔分塊索引',
  `chunkContent` text NOT NULL COMMENT '分塊內容',
  `keywords` text DEFAULT NULL COMMENT '提取的關鍵字',
  `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '建立時間',
  PRIMARY KEY (`id`),
  KEY `idx_document_index_document` (`documentId`),
  FULLTEXT KEY `ft_document_index_content` (`chunkContent`),
  FULLTEXT KEY `ft_document_index_keywords` (`keywords`),
  CONSTRAINT `fk_document_index_document` FOREIGN KEY (`documentId`) REFERENCES `document` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文檔索引表，儲存文檔的全文檢索索引資訊';

-- ----------------------------
-- 檢索相關資料表
-- ----------------------------

-- 檢索歷史表
DROP TABLE IF EXISTS `searchHistory`;
CREATE TABLE `searchHistory` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '檢索歷史唯一識別碼',
  `query` varchar(500) NOT NULL COMMENT '檢索查詢內容',
  `queryType` enum('keyword','phrase') DEFAULT 'keyword' COMMENT '查詢類型',
  `userId` int(11) DEFAULT NULL COMMENT '用戶ID',
  `ipAddress` varchar(45) DEFAULT NULL COMMENT 'IP位址',
  `userAgent` varchar(500) DEFAULT NULL COMMENT '用戶代理',
  `resultCount` int(11) DEFAULT 0 COMMENT '檢索結果數量',
  `responseTime` decimal(8,3) DEFAULT NULL COMMENT '響應時間(秒)',
  `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '檢索時間',
  PRIMARY KEY (`id`),
  KEY `idx_search_history_user` (`userId`),
  KEY `idx_search_history_created` (`createdAt`),
  KEY `idx_search_history_query` (`query`(100))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='檢索歷史表，記錄用戶的檢索行為';

-- 檢索結果表
DROP TABLE IF EXISTS `searchResult`;
CREATE TABLE `searchResult` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '檢索結果唯一識別碼',
  `searchHistoryId` int(11) NOT NULL COMMENT '關聯檢索歷史ID',
  `documentId` int(11) DEFAULT NULL COMMENT '關聯文檔ID',
  `newsArticleId` int(11) DEFAULT NULL COMMENT '關聯新聞文章ID',
  `relevanceScore` decimal(5,4) DEFAULT NULL COMMENT '相關性分數',
  `rank` int(11) NOT NULL COMMENT '結果排名',
  `snippet` text DEFAULT NULL COMMENT '結果摘要',
  `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '建立時間',
  PRIMARY KEY (`id`),
  KEY `idx_search_result_history` (`searchHistoryId`),
  KEY `idx_search_result_document` (`documentId`),
  KEY `idx_search_result_news` (`newsArticleId`),
  KEY `idx_search_result_score` (`relevanceScore`),
  CONSTRAINT `fk_search_result_history` FOREIGN KEY (`searchHistoryId`) REFERENCES `searchHistory` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_search_result_document` FOREIGN KEY (`documentId`) REFERENCES `document` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='檢索結果表，儲存檢索返回的結果詳情';

-- ----------------------------
-- 新聞抓取相關資料表
-- ----------------------------

-- 新聞來源表
DROP TABLE IF EXISTS `newsSource`;
CREATE TABLE `newsSource` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '新聞來源唯一識別碼',
  `name` varchar(100) NOT NULL COMMENT '新聞來源名稱',
  `url` varchar(500) NOT NULL COMMENT '新聞來源網址',
  `rssUrl` varchar(500) DEFAULT NULL COMMENT 'RSS訂閱網址',
  `category` varchar(50) DEFAULT 'tech' COMMENT '新聞分類',
  `language` varchar(10) DEFAULT 'zh-TW' COMMENT '語言代碼',
  `isActive` tinyint(1) DEFAULT 1 COMMENT '是否啟用',
  `crawlInterval` int(11) DEFAULT 3600 COMMENT '抓取間隔(秒)',
  `lastCrawlAt` timestamp NULL DEFAULT NULL COMMENT '最後抓取時間',
  `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '建立時間',
  `updatedAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新時間',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_news_source_url` (`url`),
  KEY `idx_news_source_active` (`isActive`),
  KEY `idx_news_source_category` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='新聞來源表，管理科技新聞的抓取來源';

-- 新聞文章表
DROP TABLE IF EXISTS `newsArticle`;
CREATE TABLE `newsArticle` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '新聞文章唯一識別碼',
  `sourceId` int(11) NOT NULL COMMENT '關聯新聞來源ID',
  `title` varchar(255) NOT NULL COMMENT '新聞標題',
  `summary` text DEFAULT NULL COMMENT '新聞摘要',
  `content` longtext DEFAULT NULL COMMENT '新聞內容',
  `author` varchar(100) DEFAULT NULL COMMENT '作者',
  `originalUrl` varchar(500) NOT NULL COMMENT '原始新聞網址',
  `imageUrl` varchar(500) DEFAULT NULL COMMENT '新聞圖片網址',
  `publishedAt` timestamp NULL DEFAULT NULL COMMENT '發布時間',
  `crawledAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '抓取時間',
  `contentHash` varchar(64) NOT NULL COMMENT '內容雜湊值',
  `isIndexed` tinyint(1) DEFAULT 0 COMMENT '是否已建立索引',
  `viewCount` int(11) DEFAULT 0 COMMENT '瀏覽次數',
  `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '建立時間',
  `updatedAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新時間',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_news_article_url` (`originalUrl`),
  KEY `idx_news_article_source` (`sourceId`),
  KEY `idx_news_article_published` (`publishedAt`),
  KEY `idx_news_article_indexed` (`isIndexed`),
  KEY `idx_news_article_hash` (`contentHash`),
  FULLTEXT KEY `ft_news_article_title` (`title`),
  FULLTEXT KEY `ft_news_article_content` (`content`),
  FULLTEXT KEY `ft_news_article_summary` (`summary`),
  CONSTRAINT `fk_news_article_source` FOREIGN KEY (`sourceId`) REFERENCES `newsSource` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='新聞文章表，儲存抓取的科技新聞內容';

-- ----------------------------
-- 系統管理相關資料表
-- ----------------------------

-- 用戶表(簡化版)
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '用戶唯一識別碼',
  `username` varchar(50) NOT NULL COMMENT '用戶名稱',
  `email` varchar(100) NOT NULL COMMENT '電子郵件',
  `passwordHash` varchar(255) NOT NULL COMMENT '密碼雜湊值',
  `role` enum('admin','user') DEFAULT 'user' COMMENT '用戶角色',
  `isActive` tinyint(1) DEFAULT 1 COMMENT '是否啟用',
  `lastLoginAt` timestamp NULL DEFAULT NULL COMMENT '最後登入時間',
  `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '建立時間',
  `updatedAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新時間',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_username` (`username`),
  UNIQUE KEY `uk_user_email` (`email`),
  KEY `idx_user_role` (`role`),
  KEY `idx_user_active` (`isActive`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用戶表，管理系統用戶資訊';

-- 系統配置表
DROP TABLE IF EXISTS `systemConfig`;
CREATE TABLE `systemConfig` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置唯一識別碼',
  `configKey` varchar(100) NOT NULL COMMENT '配置鍵名',
  `configValue` text DEFAULT NULL COMMENT '配置值',
  `description` varchar(255) DEFAULT NULL COMMENT '配置說明',
  `category` varchar(50) DEFAULT 'general' COMMENT '配置分類',
  `isEditable` tinyint(1) DEFAULT 1 COMMENT '是否可編輯',
  `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '建立時間',
  `updatedAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新時間',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_system_config_key` (`configKey`),
  KEY `idx_system_config_category` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系統配置表，儲存系統運行參數';

-- ----------------------------
-- 初始資料插入
-- ----------------------------

-- 插入預設新聞來源
INSERT INTO `newsSource` (`name`, `url`, `rssUrl`, `category`, `language`) VALUES
('TechCrunch', 'https://techcrunch.com', 'https://techcrunch.com/feed/', 'tech', 'en'),
('科技新報', 'https://technews.tw', 'https://technews.tw/feed/', 'tech', 'zh-TW'),
('iThome', 'https://www.ithome.com.tw', 'https://www.ithome.com.tw/rss', 'tech', 'zh-TW'),
('數位時代', 'https://www.bnext.com.tw', 'https://www.bnext.com.tw/rss', 'tech', 'zh-TW');

-- 插入預設系統配置
INSERT INTO `systemConfig` (`configKey`, `configValue`, `description`, `category`) VALUES
('site_name', 'Tech.VG 科技新知檢索', '網站名稱', 'general'),
('max_upload_size', '10485760', '最大上傳檔案大小(位元組)', 'upload'),
('allowed_file_types', 'txt,md,markdown', '允許上傳的檔案類型', 'upload'),
('search_results_per_page', '20', '每頁檢索結果數量', 'search'),
('fulltext_min_word_len', '2', 'MySQL全文檢索最小字長', 'search'),
('news_crawl_interval', '3600', '新聞抓取間隔(秒)', 'news');

-- 插入預設管理員用戶
INSERT INTO `user` (`username`, `email`, `passwordHash`, `role`) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin');

SET FOREIGN_KEY_CHECKS = 1;