<?php

namespace Tests\Integration\Api;

use Tests\TestCase;

/**
 * API 整合測試
 * 測試完整的 API 工作流程
 */
class ApiIntegrationTest extends TestCase
{
    private string $baseUrl;
    private string $apiKey;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->baseUrl = $this->testConfig['api']['base_url'];
        $this->apiKey = $this->testConfig['api']['test_api_key'];
        
        // 建立測試資料
        $this->createTestData();
    }

    /**
     * 建立測試資料
     */
    private function createTestData(): void
    {
        // 建立測試文檔
        $this->createTestDocument([
            'title' => 'Machine Learning Basics',
            'content' => 'This document covers the fundamentals of machine learning, including supervised and unsupervised learning algorithms.',
            'file_type' => 'txt'
        ]);

        $this->createTestDocument([
            'title' => 'Deep Learning Guide',
            'content' => 'A comprehensive guide to deep learning, neural networks, and artificial intelligence applications.',
            'file_type' => 'md'
        ]);

        // 建立測試新聞
        $this->createTestNews([
            'title' => 'AI Breakthrough in Healthcare',
            'summary' => 'New AI system shows promising results in medical diagnosis.',
            'category' => 'ai',
            'views' => 150
        ]);

        $this->createTestNews([
            'title' => 'Blockchain Technology Advances',
            'summary' => 'Latest developments in blockchain and cryptocurrency.',
            'category' => 'blockchain',
            'views' => 200
        ]);
    }

    /**
     * 測試完整的搜尋工作流程
     */
    public function testCompleteSearchWorkflow(): void
    {
        // 1. 基本搜尋
        $searchResponse = $this->makeHttpRequest('GET', '/api/search', ['q' => 'machine learning']);
        $this->assertApiSuccess($searchResponse, 'Basic search should succeed');
        
        $searchData = $searchResponse['data']['data'];
        $this->assertArrayHasKey('results', $searchData);
        $this->assertGreaterThan(0, count($searchData['results']));

        // 2. 取得搜尋建議
        $suggestionsResponse = $this->makeHttpRequest('GET', '/api/search/suggestions', ['q' => 'machine']);
        $this->assertApiSuccess($suggestionsResponse, 'Search suggestions should succeed');
        
        $suggestions = $suggestionsResponse['data']['data']['suggestions'];
        $this->assertIsArray($suggestions);

        // 3. 進階搜尋
        $advancedSearchData = [
            'query' => 'learning',
            'type' => 'document',
            'sort_by' => 'relevance'
        ];
        
        $advancedResponse = $this->makeHttpRequest('POST', '/api/search/advanced', $advancedSearchData);
        $this->assertApiSuccess($advancedResponse, 'Advanced search should succeed');

        // 4. 檢查搜尋歷史
        $historyResponse = $this->makeHttpRequest('GET', '/api/search/history');
        $this->assertApiSuccess($historyResponse, 'Search history should be accessible');
    }

    /**
     * 測試完整的文檔管理工作流程
     */
    public function testCompleteDocumentWorkflow(): void
    {
        // 1. 取得文檔列表
        $listResponse = $this->makeHttpRequest('GET', '/api/documents');
        $this->assertApiSuccess($listResponse, 'Document list should be accessible');
        
        $documents = $listResponse['data']['data'];
        $this->assertIsArray($documents);
        $this->assertGreaterThan(0, count($documents));

        $documentId = $documents[0]['id'];

        // 2. 取得單一文檔詳情
        $detailResponse = $this->makeHttpRequest('GET', "/api/documents/{$documentId}", ['include_content' => 'true']);
        $this->assertApiSuccess($detailResponse, 'Document detail should be accessible');
        
        $document = $detailResponse['data']['data'];
        $this->assertEquals($documentId, $document['id']);
        $this->assertArrayHasKey('content', $document);

        // 3. 更新文檔
        $updateData = [
            'title' => 'Updated Document Title',
            'description' => 'Updated description'
        ];
        
        $updateResponse = $this->makeHttpRequest(
            'PUT', 
            "/api/documents/{$documentId}", 
            $updateData,
            ['X-API-Key: ' . $this->apiKey]
        );
        $this->assertApiSuccess($updateResponse, 'Document update should succeed');

        // 4. 驗證更新
        $verifyResponse = $this->makeHttpRequest('GET', "/api/documents/{$documentId}");
        $this->assertApiSuccess($verifyResponse, 'Updated document should be accessible');
        
        $updatedDocument = $verifyResponse['data']['data'];
        $this->assertEquals('Updated Document Title', $updatedDocument['title']);

        // 5. 重建索引
        $reindexResponse = $this->makeHttpRequest(
            'POST', 
            "/api/documents/{$documentId}/reindex",
            [],
            ['X-API-Key: ' . $this->apiKey]
        );
        $this->assertApiSuccess($reindexResponse, 'Document reindex should succeed');
    }

    /**
     * 測試文檔上傳工作流程
     */
    public function testDocumentUploadWorkflow(): void
    {
        // 建立測試檔案
        $testContent = "This is a test document for upload testing.\nIt contains multiple lines of text.";
        $testFile = $this->createTestFile('upload_test.txt', $testContent);

        // 準備上傳資料
        $uploadData = [
            'title' => 'Uploaded Test Document',
            'description' => 'This document was uploaded via API test'
        ];

        // 模擬檔案上傳（這裡需要實際的 HTTP 客戶端來處理 multipart/form-data）
        $uploadResponse = $this->simulateFileUpload('/api/documents', $testFile, $uploadData);
        
        if ($uploadResponse['success']) {
            $this->assertApiSuccess($uploadResponse, 'Document upload should succeed');
            
            $uploadedDocument = $uploadResponse['data']['data'];
            $this->assertArrayHasKey('id', $uploadedDocument);
            $this->assertEquals('Uploaded Test Document', $uploadedDocument['title']);

            // 驗證檔案可以被檢索
            $searchResponse = $this->makeHttpRequest('GET', '/api/search', ['q' => 'upload testing']);
            $this->assertApiSuccess($searchResponse, 'Uploaded document should be searchable');
        } else {
            $this->markTestSkipped('File upload simulation not available');
        }
    }

    /**
     * 測試新聞 API 工作流程
     */
    public function testNewsWorkflow(): void
    {
        // 1. 取得新聞列表
        $newsResponse = $this->makeHttpRequest('GET', '/api/news');
        $this->assertApiSuccess($newsResponse, 'News list should be accessible');
        
        $newsList = $newsResponse['data']['data'];
        $this->assertIsArray($newsList);

        if (count($newsList) > 0) {
            $newsId = $newsList[0]['id'];

            // 2. 取得新聞詳情
            $detailResponse = $this->makeHttpRequest('GET', "/api/news/{$newsId}");
            $this->assertApiSuccess($detailResponse, 'News detail should be accessible');
            
            $newsDetail = $detailResponse['data']['data'];
            $this->assertEquals($newsId, $newsDetail['id']);
        }

        // 3. 取得最新新聞
        $latestResponse = $this->makeHttpRequest('GET', '/api/news/latest', ['limit' => 5]);
        $this->assertApiSuccess($latestResponse, 'Latest news should be accessible');

        // 4. 取得熱門新聞
        $popularResponse = $this->makeHttpRequest('GET', '/api/news/popular', ['limit' => 5]);
        $this->assertApiSuccess($popularResponse, 'Popular news should be accessible');

        // 5. 取得新聞分類
        $categoriesResponse = $this->makeHttpRequest('GET', '/api/news/categories');
        $this->assertApiSuccess($categoriesResponse, 'News categories should be accessible');
    }

    /**
     * 測試統計 API 工作流程
     */
    public function testStatsWorkflow(): void
    {
        // 1. 取得系統總覽
        $overviewResponse = $this->makeHttpRequest('GET', '/api/stats/overview');
        $this->assertApiSuccess($overviewResponse, 'Stats overview should be accessible');
        
        $overview = $overviewResponse['data']['data'];
        $this->assertArrayHasKeys(['summary', 'system_health'], $overview);

        // 2. 取得文檔統計
        $docStatsResponse = $this->makeHttpRequest('GET', '/api/stats/documents');
        $this->assertApiSuccess($docStatsResponse, 'Document stats should be accessible');

        // 3. 取得新聞統計
        $newsStatsResponse = $this->makeHttpRequest('GET', '/api/stats/news');
        $this->assertApiSuccess($newsStatsResponse, 'News stats should be accessible');

        // 4. 取得搜尋統計
        $searchStatsResponse = $this->makeHttpRequest('GET', '/api/stats/search');
        $this->assertApiSuccess($searchStatsResponse, 'Search stats should be accessible');

        // 5. 取得即時統計
        $realtimeResponse = $this->makeHttpRequest('GET', '/api/stats/realtime');
        $this->assertApiSuccess($realtimeResponse, 'Realtime stats should be accessible');
    }

    /**
     * 測試快取系統工作流程
     */
    public function testCacheWorkflow(): void
    {
        // 1. 檢查快取狀態
        $statusResponse = $this->makeHttpRequest('GET', '/api/cache/status');
        $this->assertApiSuccess($statusResponse, 'Cache status should be accessible');

        // 2. 取得快取項目
        $itemsResponse = $this->makeHttpRequest('GET', '/api/cache/items', ['limit' => 10]);
        $this->assertApiSuccess($itemsResponse, 'Cache items should be accessible');

        // 3. 快取預熱
        $warmupData = [
            'endpoints' => [
                '/api/stats/overview',
                '/api/news/latest'
            ]
        ];
        
        $warmupResponse = $this->makeHttpRequest(
            'POST', 
            '/api/cache/warmup', 
            $warmupData,
            ['X-API-Key: ' . $this->apiKey]
        );
        $this->assertApiSuccess($warmupResponse, 'Cache warmup should succeed');

        // 4. 清除特定快取
        $clearData = [
            'type' => 'search',
            'pattern' => 'search_*'
        ];
        
        $clearResponse = $this->makeHttpRequest(
            'DELETE', 
            '/api/cache', 
            $clearData,
            ['X-API-Key: ' . $this->apiKey]
        );
        $this->assertApiSuccess($clearResponse, 'Cache clear should succeed');
    }

    /**
     * 測試 API 認證和授權
     */
    public function testAuthenticationAndAuthorization(): void
    {
        // 1. 測試無需認證的端點
        $publicResponse = $this->makeHttpRequest('GET', '/api/search', ['q' => 'test']);
        $this->assertApiSuccess($publicResponse, 'Public endpoint should be accessible without API key');

        // 2. 測試需要認證的端點（無 API 金鑰）
        $protectedResponse = $this->makeHttpRequest('POST', '/api/documents/batch', [
            'action' => 'update_status',
            'document_ids' => [1],
            'data' => ['status' => 'active']
        ]);
        $this->assertApiError($protectedResponse, 401, 'Protected endpoint should require API key');

        // 3. 測試無效的 API 金鑰
        $invalidKeyResponse = $this->makeHttpRequest(
            'POST', 
            '/api/documents/batch',
            ['action' => 'update_status', 'document_ids' => [1]],
            ['X-API-Key: invalid_key']
        );
        $this->assertApiError($invalidKeyResponse, 401, 'Invalid API key should be rejected');

        // 4. 測試有效的 API 金鑰
        $validKeyResponse = $this->makeHttpRequest(
            'GET', 
            '/api/documents/stats',
            [],
            ['X-API-Key: ' . $this->apiKey]
        );
        $this->assertApiSuccess($validKeyResponse, 'Valid API key should be accepted');
    }

    /**
     * 測試速率限制
     */
    public function testRateLimiting(): void
    {
        $endpoint = '/api/search';
        $params = ['q' => 'rate limit test'];
        
        $successCount = 0;
        $rateLimitHit = false;

        // 發送大量請求來觸發速率限制
        for ($i = 0; $i < 250; $i++) {
            $response = $this->makeHttpRequest('GET', $endpoint, $params);
            
            if ($response['success'] && $response['data']['success']) {
                $successCount++;
            } else {
                // 檢查是否是速率限制錯誤
                if (isset($response['data']['error_code']) && 
                    $response['data']['error_code'] === 'RATE_LIMIT_EXCEEDED') {
                    $rateLimitHit = true;
                    break;
                }
            }
            
            // 短暫延遲避免過快請求
            usleep(10000); // 10ms
        }

        $this->assertGreaterThan(0, $successCount, 'Some requests should succeed before rate limit');
        $this->assertTrue($rateLimitHit, 'Rate limit should be triggered');
    }

    /**
     * 測試錯誤處理
     */
    public function testErrorHandling(): void
    {
        // 1. 測試 404 錯誤
        $notFoundResponse = $this->makeHttpRequest('GET', '/api/documents/99999');
        $this->assertApiError($notFoundResponse, 404, 'Non-existent resource should return 404');

        // 2. 測試參數驗證錯誤
        $validationResponse = $this->makeHttpRequest('GET', '/api/search', ['q' => '']);
        $this->assertApiError($validationResponse, 400, 'Invalid parameters should return 400');

        // 3. 測試不支援的方法
        $methodResponse = $this->makeHttpRequest('PATCH', '/api/documents/1');
        $this->assertApiError($methodResponse, 405, 'Unsupported method should return 405');
    }

    /**
     * 測試資料一致性
     */
    public function testDataConsistency(): void
    {
        // 1. 建立文檔並驗證可以搜尋到
        $documentId = $this->createTestDocument([
            'title' => 'Consistency Test Document',
            'content' => 'This document is for testing data consistency across APIs.'
        ]);

        // 等待索引建立
        sleep(1);

        // 2. 搜尋新建立的文檔
        $searchResponse = $this->makeHttpRequest('GET', '/api/search', ['q' => 'consistency test']);
        $this->assertApiSuccess($searchResponse, 'Search should find newly created document');
        
        $results = $searchResponse['data']['data']['results'];
        $found = false;
        foreach ($results as $result) {
            if ($result['id'] == $documentId && $result['type'] === 'document') {
                $found = true;
                break;
            }
        }
        $this->assertTrue($found, 'Newly created document should be found in search results');

        // 3. 更新文檔並驗證搜尋結果更新
        $updateResponse = $this->makeHttpRequest(
            'PUT',
            "/api/documents/{$documentId}",
            ['title' => 'Updated Consistency Test'],
            ['X-API-Key: ' . $this->apiKey]
        );
        $this->assertApiSuccess($updateResponse, 'Document update should succeed');

        // 4. 驗證統計資料一致性
        $statsResponse = $this->makeHttpRequest('GET', '/api/stats/documents');
        $this->assertApiSuccess($statsResponse, 'Document stats should be accessible');
        
        $stats = $statsResponse['data']['data'];
        $this->assertArrayHasKey('total', $stats);
        $this->assertGreaterThan(0, $stats['total']);
    }

    /**
     * 模擬檔案上傳
     * 
     * @param string $endpoint
     * @param string $filePath
     * @param array $data
     * @return array
     */
    private function simulateFileUpload(string $endpoint, string $filePath, array $data): array
    {
        // 這裡應該實現實際的檔案上傳邏輯
        // 由於測試環境限制，暫時返回模擬結果
        return [
            'success' => false,
            'message' => 'File upload simulation not implemented'
        ];
    }

    /**
     * 測試 API 版本控制
     */
    public function testApiVersioning(): void
    {
        // 測試 API 版本標頭
        $response = $this->makeHttpRequest('GET', '/api/search', ['q' => 'version test']);
        
        if ($response['success']) {
            $headers = $response['http_response_header'];
            $versionHeader = null;
            
            foreach ($headers as $header) {
                if (strpos($header, 'X-API-Version:') !== false) {
                    $versionHeader = $header;
                    break;
                }
            }
            
            $this->assertNotNull($versionHeader, 'API version header should be present');
            $this->assertStringContains('v1', $versionHeader, 'API version should be v1');
        }
    }

    /**
     * 測試 CORS 支援
     */
    public function testCorsSupport(): void
    {
        // 發送 OPTIONS 請求測試 CORS 預檢
        $corsResponse = $this->makeHttpRequest('OPTIONS', '/api/search');
        
        if ($corsResponse['success']) {
            $headers = $corsResponse['http_response_header'];
            $corsHeaders = [];
            
            foreach ($headers as $header) {
                if (strpos($header, 'Access-Control-') !== false) {
                    $corsHeaders[] = $header;
                }
            }
            
            $this->assertNotEmpty($corsHeaders, 'CORS headers should be present');
        }
    }

    protected function tearDown(): void
    {
        // 清理測試資料
        $this->cleanupTestData();
        
        parent::tearDown();
    }
}