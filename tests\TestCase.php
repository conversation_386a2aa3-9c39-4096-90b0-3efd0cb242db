<?php

namespace Tests;

use PHPUnit\Framework\TestCase as PHPUnitTestCase;
use Tests\Helpers\TestDataGenerator;

/**
 * 測試基礎類別
 * 提供所有測試的共用功能
 */
abstract class TestCase extends PHPUnitTestCase
{
    /**
     * 測試資料庫連線
     */
    protected $testDb;
    
    /**
     * 測試配置
     */
    protected array $testConfig;
    
    /**
     * 測試資料目錄
     */
    protected string $testDataDir;
    
    /**
     * 測試資料生成器
     */
    protected TestDataGenerator $dataGenerator;

    /**
     * 設定測試環境
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        // 設定測試資料目錄
        $this->testDataDir = __DIR__ . '/data';
        
        // 載入測試配置
        $this->loadTestConfig();
        
        // 設定測試資料庫
        $this->setupTestDatabase();
        
        // 清理測試環境
        $this->cleanupTestEnvironment();
    }

    /**
     * 清理測試環境
     */
    protected function tearDown(): void
    {
        // 清理測試資料
        $this->cleanupTestData();
        
        // 關閉資料庫連線
        if ($this->testDb) {
            $this->testDb = null;
        }
        
        parent::tearDown();
    }

    /**
     * 載入測試配置
     */
    protected function loadTestConfig(): void
    {
        $configFile = __DIR__ . '/config/test_config.php';
        
        if (file_exists($configFile)) {
            $this->testConfig = require $configFile;
        } else {
            // 預設配置
            $this->testConfig = [
                'database' => [
                    'host' => 'localhost',
                    'dbname' => 'techvg_test',
                    'username' => 'test_user',
                    'password' => 'test_password',
                    'charset' => 'utf8mb4'
                ],
                'api' => [
                    'base_url' => 'http://localhost:8000',
                    'test_api_key' => 'test_key_123456'
                ],
                'storage' => [
                    'test_uploads' => __DIR__ . '/uploads',
                    'test_cache' => __DIR__ . '/cache'
                ]
            ];
        }
    }

    /**
     * 設定測試資料庫
     */
    protected function setupTestDatabase(): void
    {
        try {
            $dsn = sprintf(
                'mysql:host=%s;dbname=%s;charset=%s',
                $this->testConfig['database']['host'],
                $this->testConfig['database']['dbname'],
                $this->testConfig['database']['charset']
            );

            $this->testDb = new \PDO(
                $dsn,
                $this->testConfig['database']['username'],
                $this->testConfig['database']['password'],
                [
                    \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                    \PDO::ATTR_DEFAULT_FETCH_MODE => \PDO::FETCH_ASSOC
                ]
            );

            // 建立測試資料表
            $this->createTestTables();
            
            // 初始化測試資料生成器
            $this->dataGenerator = new TestDataGenerator($this->testDb, $this->testConfig);

        } catch (\PDOException $e) {
            $this->markTestSkipped('無法連接測試資料庫: ' . $e->getMessage());
        }
    }

    /**
     * 建立測試資料表
     */
    protected function createTestTables(): void
    {
        $tables = [
            'documents' => "
                CREATE TABLE IF NOT EXISTS documents (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    title VARCHAR(255) NOT NULL,
                    filename VARCHAR(255) NOT NULL,
                    file_type VARCHAR(10) NOT NULL,
                    file_size INT NOT NULL,
                    content TEXT,
                    status ENUM('active', 'inactive', 'processing') DEFAULT 'active',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            ",
            'news_articles' => "
                CREATE TABLE IF NOT EXISTS news_articles (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    title VARCHAR(500) NOT NULL,
                    summary TEXT,
                    content TEXT,
                    url VARCHAR(1000),
                    category VARCHAR(100),
                    source_id INT,
                    views INT DEFAULT 0,
                    published_at TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            ",
            'search_history' => "
                CREATE TABLE IF NOT EXISTS search_history (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    query VARCHAR(500) NOT NULL,
                    type VARCHAR(50) DEFAULT 'all',
                    results_count INT DEFAULT 0,
                    search_time DECIMAL(8,3) DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            "
        ];

        foreach ($tables as $tableName => $sql) {
            $this->testDb->exec($sql);
        }
    }

    /**
     * 清理測試環境
     */
    protected function cleanupTestEnvironment(): void
    {
        // 清理測試上傳目錄
        $uploadDir = $this->testConfig['storage']['test_uploads'];
        if (is_dir($uploadDir)) {
            $this->removeDirectory($uploadDir);
        }
        mkdir($uploadDir, 0755, true);

        // 清理測試快取目錄
        $cacheDir = $this->testConfig['storage']['test_cache'];
        if (is_dir($cacheDir)) {
            $this->removeDirectory($cacheDir);
        }
        mkdir($cacheDir, 0755, true);
    }

    /**
     * 清理測試資料
     */
    protected function cleanupTestData(): void
    {
        if ($this->testDb) {
            $tables = ['documents', 'news_articles', 'search_history'];
            foreach ($tables as $table) {
                $this->testDb->exec("TRUNCATE TABLE {$table}");
            }
        }
    }

    /**
     * 建立測試文檔
     * 
     * @param array $data
     * @return int
     */
    protected function createTestDocument(array $data = []): int
    {
        $defaultData = [
            'title' => 'Test Document',
            'filename' => 'test.txt',
            'file_type' => 'txt',
            'file_size' => 1024,
            'content' => 'This is a test document content.',
            'status' => 'active'
        ];

        $data = array_merge($defaultData, $data);

        $sql = "INSERT INTO documents (title, filename, file_type, file_size, content, status) 
                VALUES (:title, :filename, :file_type, :file_size, :content, :status)";
        
        $stmt = $this->testDb->prepare($sql);
        $stmt->execute($data);
        
        return $this->testDb->lastInsertId();
    }

    /**
     * 建立測試新聞
     * 
     * @param array $data
     * @return int
     */
    protected function createTestNews(array $data = []): int
    {
        $defaultData = [
            'title' => 'Test News Article',
            'summary' => 'This is a test news summary.',
            'content' => 'This is a test news content.',
            'url' => 'https://example.com/news/1',
            'category' => 'technology',
            'source_id' => 1,
            'views' => 0,
            'published_at' => date('Y-m-d H:i:s')
        ];

        $data = array_merge($defaultData, $data);

        $sql = "INSERT INTO news_articles (title, summary, content, url, category, source_id, views, published_at) 
                VALUES (:title, :summary, :content, :url, :category, :source_id, :views, :published_at)";
        
        $stmt = $this->testDb->prepare($sql);
        $stmt->execute($data);
        
        return $this->testDb->lastInsertId();
    }

    /**
     * 建立測試檔案
     * 
     * @param string $filename
     * @param string $content
     * @return string
     */
    protected function createTestFile(string $filename, string $content = 'Test file content'): string
    {
        $filePath = $this->testConfig['storage']['test_uploads'] . '/' . $filename;
        file_put_contents($filePath, $content);
        return $filePath;
    }

    /**
     * 發送 HTTP 請求
     * 
     * @param string $method
     * @param string $endpoint
     * @param array $data
     * @param array $headers
     * @return array
     */
    protected function makeHttpRequest(string $method, string $endpoint, array $data = [], array $headers = []): array
    {
        $url = $this->testConfig['api']['base_url'] . $endpoint;
        
        $defaultHeaders = [
            'Content-Type: application/json',
            'Accept: application/json'
        ];
        
        $headers = array_merge($defaultHeaders, $headers);
        
        $context = [
            'http' => [
                'method' => $method,
                'header' => implode("\r\n", $headers),
                'timeout' => 30
            ]
        ];

        if (!empty($data) && in_array($method, ['POST', 'PUT'])) {
            $context['http']['content'] = json_encode($data);
        }

        $context = stream_context_create($context);
        
        $response = @file_get_contents($url, false, $context);
        
        if ($response === false) {
            return [
                'success' => false,
                'error' => 'HTTP request failed',
                'http_response_header' => $http_response_header ?? []
            ];
        }

        $decoded = json_decode($response, true);
        
        return [
            'success' => true,
            'data' => $decoded,
            'http_response_header' => $http_response_header ?? []
        ];
    }

    /**
     * 斷言 API 響應成功
     * 
     * @param array $response
     * @param string $message
     */
    protected function assertApiSuccess(array $response, string $message = ''): void
    {
        $this->assertTrue($response['success'], 'HTTP request should succeed');
        $this->assertArrayHasKey('data', $response, 'Response should have data');
        $this->assertTrue($response['data']['success'] ?? false, $message ?: 'API response should be successful');
    }

    /**
     * 斷言 API 響應失敗
     * 
     * @param array $response
     * @param int $expectedStatusCode
     * @param string $message
     */
    protected function assertApiError(array $response, int $expectedStatusCode = 400, string $message = ''): void
    {
        $this->assertTrue($response['success'], 'HTTP request should succeed');
        $this->assertArrayHasKey('data', $response, 'Response should have data');
        $this->assertFalse($response['data']['success'] ?? true, $message ?: 'API response should indicate error');
    }

    /**
     * 斷言陣列包含鍵值
     * 
     * @param array $expectedKeys
     * @param array $array
     * @param string $message
     */
    protected function assertArrayHasKeys(array $expectedKeys, array $array, string $message = ''): void
    {
        foreach ($expectedKeys as $key) {
            $this->assertArrayHasKey($key, $array, $message ?: "Array should have key: {$key}");
        }
    }

    /**
     * 移除目錄及其內容
     * 
     * @param string $dir
     */
    protected function removeDirectory(string $dir): void
    {
        if (!is_dir($dir)) {
            return;
        }

        $files = array_diff(scandir($dir), ['.', '..']);
        
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            if (is_dir($path)) {
                $this->removeDirectory($path);
            } else {
                unlink($path);
            }
        }
        
        rmdir($dir);
    }

    /**
     * 取得測試資料
     * 
     * @param string $filename
     * @return mixed
     */
    protected function getTestData(string $filename)
    {
        $filePath = $this->testDataDir . '/' . $filename;
        
        if (!file_exists($filePath)) {
            throw new \Exception("Test data file not found: {$filePath}");
        }

        $extension = pathinfo($filePath, PATHINFO_EXTENSION);
        
        switch ($extension) {
            case 'json':
                return json_decode(file_get_contents($filePath), true);
            case 'php':
                return require $filePath;
            default:
                return file_get_contents($filePath);
        }
    }

    /**
     * 模擬時間
     * 
     * @param string $time
     */
    protected function mockTime(string $time): void
    {
        // 這裡可以實現時間模擬邏輯
        // 暫時使用簡單的方法
        $_SERVER['MOCK_TIME'] = strtotime($time);
    }

    /**
     * 重置時間模擬
     */
    protected function resetTime(): void
    {
        unset($_SERVER['MOCK_TIME']);
    }
}