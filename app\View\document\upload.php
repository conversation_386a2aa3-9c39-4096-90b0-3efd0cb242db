<?php
/**
 * 文檔上傳頁面
 */

$csrfToken = $csrfToken ?? '';
$maxFileSize = $maxFileSize ?? '10MB';
$allowedTypes = $allowedTypes ?? ['txt', 'md', 'markdown'];
$error = $error ?? '';
$success = $success ?? '';
$formData = $formData ?? [];
$documentId = $documentId ?? null;

include __DIR__ . '/../layout/header.php';
?>

<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- 返回按鈕 -->
    <div class="mb-6">
        <a href="/documents" class="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors">
            <i class="fas fa-arrow-left mr-2"></i>返回文檔列表
        </a>
    </div>

    <!-- 頁面標題 -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">上傳文檔</h1>
        <p class="text-gray-600">上傳您的技術文檔，系統將自動建立索引以便檢索</p>
    </div>

    <!-- 成功訊息 -->
    <?php if ($success): ?>
        <div class="bg-green-50 border border-green-200 rounded-md p-4 mb-8">
            <div class="flex">
                <i class="fas fa-check-circle text-green-400 mr-3 mt-1"></i>
                <div>
                    <div class="text-green-700 font-medium"><?= htmlspecialchars($success) ?></div>
                    <?php if ($documentId): ?>
                        <div class="mt-2">
                            <a href="/documents/<?= $documentId ?>" 
                               class="text-green-600 hover:text-green-800 font-medium">
                                查看文檔 <i class="fas fa-arrow-right ml-1"></i>
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- 錯誤訊息 -->
    <?php if ($error): ?>
        <div class="bg-red-50 border border-red-200 rounded-md p-4 mb-8">
            <div class="flex">
                <i class="fas fa-exclamation-circle text-red-400 mr-3 mt-1"></i>
                <div class="text-red-700"><?= htmlspecialchars($error) ?></div>
            </div>
        </div>
    <?php endif; ?>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- 上傳表單 -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow-md p-6">
                <form action="/documents/upload" method="POST" enctype="multipart/form-data" id="uploadForm">
                    <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($csrfToken) ?>">

                    <!-- 文檔標題 -->
                    <div class="mb-6">
                        <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                            文檔標題 <span class="text-gray-400">(選填)</span>
                        </label>
                        <input type="text" 
                               name="title" 
                               id="title" 
                               value="<?= htmlspecialchars($formData['title'] ?? '') ?>"
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="如果不填寫，將使用檔案名稱作為標題">
                    </div>

                    <!-- 檔案上傳 -->
                    <div class="mb-6">
                        <label for="document" class="block text-sm font-medium text-gray-700 mb-2">
                            選擇文檔 <span class="text-red-500">*</span>
                        </label>
                        
                        <!-- 拖拽上傳區域 -->
                        <div id="dropZone" class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors cursor-pointer">
                            <div id="dropZoneContent">
                                <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                                <p class="text-lg font-medium text-gray-700 mb-2">拖拽檔案到此處或點擊選擇</p>
                                <p class="text-sm text-gray-500">
                                    支援格式：<?= implode(', ', array_map('strtoupper', $allowedTypes)) ?>
                                    <br>
                                    最大檔案大小：<?= htmlspecialchars($maxFileSize) ?>
                                </p>
                            </div>
                            
                            <!-- 檔案資訊顯示 -->
                            <div id="fileInfo" class="hidden">
                                <i class="fas fa-file-alt text-4xl text-blue-500 mb-4"></i>
                                <p id="fileName" class="text-lg font-medium text-gray-700 mb-2"></p>
                                <p id="fileSize" class="text-sm text-gray-500"></p>
                                <button type="button" onclick="clearFile()" class="mt-2 text-red-600 hover:text-red-800 text-sm">
                                    <i class="fas fa-times mr-1"></i>移除檔案
                                </button>
                            </div>
                        </div>
                        
                        <input type="file" 
                               name="document" 
                               id="document" 
                               accept=".txt,.md,.markdown"
                               class="hidden"
                               required>
                    </div>

                    <!-- 上傳進度 -->
                    <div id="uploadProgress" class="hidden mb-6">
                        <div class="flex justify-between text-sm text-gray-600 mb-2">
                            <span>上傳進度</span>
                            <span id="progressPercent">0%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div id="progressBar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                        </div>
                    </div>

                    <!-- 提交按鈕 -->
                    <div class="flex justify-end space-x-4">
                        <a href="/documents" 
                           class="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors">
                            取消
                        </a>
                        <button type="submit" 
                                id="submitBtn"
                                class="px-6 py-2 btn-primary text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                            <span id="submitText">
                                <i class="fas fa-upload mr-2"></i>上傳文檔
                            </span>
                            <span id="uploadingText" class="hidden">
                                <i class="fas fa-spinner fa-spin mr-2"></i>上傳中...
                            </span>
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 側邊欄資訊 -->
        <div class="lg:col-span-1">
            <!-- 上傳須知 -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-info-circle text-blue-500 mr-2"></i>上傳須知
                </h3>
                <ul class="space-y-3 text-sm text-gray-600">
                    <li class="flex items-start">
                        <i class="fas fa-check text-green-500 mr-2 mt-1 flex-shrink-0"></i>
                        支援的檔案格式：TXT、Markdown
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check text-green-500 mr-2 mt-1 flex-shrink-0"></i>
                        最大檔案大小：<?= htmlspecialchars($maxFileSize) ?>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check text-green-500 mr-2 mt-1 flex-shrink-0"></i>
                        系統將自動建立全文索引
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check text-green-500 mr-2 mt-1 flex-shrink-0"></i>
                        支援中文和英文內容檢索
                    </li>
                </ul>
            </div>

            <!-- 最近上傳 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-clock text-gray-500 mr-2"></i>最近上傳
                </h3>
                <div class="space-y-3">
                    <div class="text-center text-gray-500 text-sm py-4">
                        <i class="fas fa-file-alt text-2xl mb-2"></i>
                        <p>暫無最近上傳的文檔</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    const dropZone = document.getElementById('dropZone');
    const fileInput = document.getElementById('document');
    const dropZoneContent = document.getElementById('dropZoneContent');
    const fileInfo = document.getElementById('fileInfo');
    const fileName = document.getElementById('fileName');
    const fileSize = document.getElementById('fileSize');
    const uploadForm = document.getElementById('uploadForm');
    const submitBtn = document.getElementById('submitBtn');
    const submitText = document.getElementById('submitText');
    const uploadingText = document.getElementById('uploadingText');
    const uploadProgress = document.getElementById('uploadProgress');
    const progressBar = document.getElementById('progressBar');
    const progressPercent = document.getElementById('progressPercent');

    // 拖拽事件
    dropZone.addEventListener('click', () => fileInput.click());
    
    dropZone.addEventListener('dragover', (e) => {
        e.preventDefault();
        dropZone.classList.add('border-blue-400', 'bg-blue-50');
    });

    dropZone.addEventListener('dragleave', (e) => {
        e.preventDefault();
        dropZone.classList.remove('border-blue-400', 'bg-blue-50');
    });

    dropZone.addEventListener('drop', (e) => {
        e.preventDefault();
        dropZone.classList.remove('border-blue-400', 'bg-blue-50');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            fileInput.files = files;
            handleFileSelect(files[0]);
        }
    });

    // 檔案選擇事件
    fileInput.addEventListener('change', (e) => {
        if (e.target.files.length > 0) {
            handleFileSelect(e.target.files[0]);
        }
    });

    // 處理檔案選擇
    function handleFileSelect(file) {
        // 檢查檔案類型
        const allowedTypes = ['txt', 'md', 'markdown'];
        const fileExtension = file.name.split('.').pop().toLowerCase();
        
        if (!allowedTypes.includes(fileExtension)) {
            showNotification('不支援的檔案類型，請選擇 TXT 或 Markdown 檔案', 'error');
            return;
        }

        // 檢查檔案大小 (10MB)
        const maxSize = 10 * 1024 * 1024;
        if (file.size > maxSize) {
            showNotification('檔案大小超過限制，請選擇小於 10MB 的檔案', 'error');
            return;
        }

        // 顯示檔案資訊
        fileName.textContent = file.name;
        fileSize.textContent = formatBytes(file.size);
        
        dropZoneContent.classList.add('hidden');
        fileInfo.classList.remove('hidden');

        // 自動填入標題
        const titleInput = document.getElementById('title');
        if (!titleInput.value) {
            const nameWithoutExtension = file.name.replace(/\.[^/.]+$/, '');
            titleInput.value = nameWithoutExtension;
        }
    }

    // 清除檔案
    function clearFile() {
        fileInput.value = '';
        dropZoneContent.classList.remove('hidden');
        fileInfo.classList.add('hidden');
    }

    // 表單提交
    uploadForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (!fileInput.files.length) {
            showNotification('請選擇要上傳的檔案', 'error');
            return;
        }

        // 顯示上傳狀態
        submitBtn.disabled = true;
        submitText.classList.add('hidden');
        uploadingText.classList.remove('hidden');
        uploadProgress.classList.remove('hidden');

        // 使用 FormData 上傳
        const formData = new FormData(this);
        
        const xhr = new XMLHttpRequest();
        
        // 上傳進度
        xhr.upload.addEventListener('progress', function(e) {
            if (e.lengthComputable) {
                const percentComplete = (e.loaded / e.total) * 100;
                progressBar.style.width = percentComplete + '%';
                progressPercent.textContent = Math.round(percentComplete) + '%';
            }
        });

        // 上傳完成
        xhr.addEventListener('load', function() {
            if (xhr.status >= 200 && xhr.status < 300) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (response.success && response.redirectUrl) {
                        showNotification(response.message || '文檔上傳成功！', 'success');
                        setTimeout(() => {
                            window.location.href = response.redirectUrl;
                        }, 1000);
                    } else {
                        showNotification(response.error || '上傳失敗，請稍後再試', 'error');
                        resetUploadState();
                    }
                } catch (e) {
                    showNotification('回應解析失敗', 'error');
                    resetUploadState();
                }
            } else {
                try {
                    const response = JSON.parse(xhr.responseText);
                    showNotification(response.error || '上傳失敗，請稍後再試', 'error');
                } catch (e) {
                    showNotification('上傳失敗，請稍後再試', 'error');
                }
                resetUploadState();
            }
        });

        // 上傳錯誤
        xhr.addEventListener('error', function() {
            showNotification('上傳失敗，請檢查網路連線', 'error');
            resetUploadState();
        });

        xhr.open('POST', '/documents/upload');
        xhr.send(formData);
    });

    // 重置上傳狀態
    function resetUploadState() {
        submitBtn.disabled = false;
        submitText.classList.remove('hidden');
        uploadingText.classList.add('hidden');
        uploadProgress.classList.add('hidden');
        progressBar.style.width = '0%';
        progressPercent.textContent = '0%';
    }

    // 格式化檔案大小
    function formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
</script>

<?php include __DIR__ . '/../layout/footer.php'; ?>