<!DOCTYPE html>
<html lang='zh-TW'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Tech.VG 測試報告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .suite { margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .failure { background-color: #f8d7da; border-color: #f5c6cb; }
        .output { background: #f8f9fa; padding: 10px; border-radius: 3px; font-family: monospace; white-space: pre-wrap; }
        .summary { background: #e9ecef; padding: 15px; border-radius: 5px; margin-top: 20px; }
    </style>
</head>
<body>
    <div class='header'>
        <h1>Tech.VG 測試報告</h1>
        <p>生成時間: 2025-06-30 10:48:20</p>
    </div>
    <div class='suite failure'>
        <h2>unit - ✗ 失敗</h2>
        <p>執行時間: 0.28 秒</p>
        <details>
            <summary>詳細輸出</summary>
            <div class='output'>PHPUnit 9.6.23 by Sebastian Bergmann and contributors.

Usage:
  phpunit [options] UnitTest.php
  phpunit [options] &lt;directory&gt;

Code Coverage Options:

  --coverage-clover &lt;file&gt;    Generate code coverage report in Clover XML format
  --coverage-cobertura &lt;file&gt; Generate code coverage report in Cobertura XML format
  --coverage-crap4j &lt;file&gt;    Generate code coverage report in Crap4J XML format
  --coverage-html &lt;dir&gt;       Generate code coverage report in HTML format
  --coverage-php &lt;file&gt;       Export PHP_CodeCoverage object to file
  --coverage-text=&lt;file&gt;      Generate code coverage report in text format [default: standard output]
  --coverage-xml &lt;dir&gt;        Generate code coverage report in PHPUnit XML format
  --coverage-cache &lt;dir&gt;      Cache static analysis results
  --warm-coverage-cache       Warm static analysis cache
  --coverage-filter &lt;dir&gt;     Include &lt;dir&gt; in code coverage analysis
  --path-coverage             Perform path coverage analysis
  --disable-coverage-ignore   Disable annotations for ignoring code coverage
  --no-coverage               Ignore code coverage configuration

Logging Options:

  --log-junit &lt;file&gt;          Log test execution in JUnit XML format to file
  --log-teamcity &lt;file&gt;       Log test execution in TeamCity format to file
  --testdox-html &lt;file&gt;       Write agile documentation in HTML format to file
  --testdox-text &lt;file&gt;       Write agile documentation in Text format to file
  --testdox-xml &lt;file&gt;        Write agile documentation in XML format to file
  --reverse-list              Print defects in reverse order
  --no-logging                Ignore logging configuration

Test Selection Options:

  --list-suites               List available test suites
  --testsuite &lt;name&gt;          Filter which testsuite to run
  --list-groups               List available test groups
  --group &lt;name&gt;              Only runs tests from the specified group(s)
  --exclude-group &lt;name&gt;      Exclude tests from the specified group(s)
  --covers &lt;name&gt;             Only runs tests annotated with &quot;@covers &lt;name&gt;&quot;
  --uses &lt;name&gt;               Only runs tests annotated with &quot;@uses &lt;name&gt;&quot;
  --list-tests                List available tests
  --list-tests-xml &lt;file&gt;     List available tests in XML format
  --filter &lt;pattern&gt;          Filter which tests to run
  --test-suffix &lt;suffixes&gt;    Only search for test in files with specified suffix(es). Default: Test.php,.phpt

Test Execution Options:

  --dont-report-useless-tests Do not report tests that do not test anything
  --strict-coverage           Be strict about @covers annotation usage
  --strict-global-state       Be strict about changes to global state
  --disallow-test-output      Be strict about output during tests
  --disallow-resource-usage   Be strict about resource usage during small tests
  --enforce-time-limit        Enforce time limit based on test size
  --default-time-limit &lt;sec&gt;  Timeout in seconds for tests without @small, @medium or @large
  --disallow-todo-tests       Disallow @todo-annotated tests

  --process-isolation         Run each test in a separate PHP process
  --globals-backup            Backup and restore $GLOBALS for each test
  --static-backup             Backup and restore static attributes for each test

  --colors &lt;flag&gt;             Use colors in output (&quot;never&quot;, &quot;auto&quot; or &quot;always&quot;)
  --columns &lt;n&gt;               Number of columns to use for progress output
  --columns max               Use maximum number of columns for progress output
  --stderr                    Write to STDERR instead of STDOUT
  --stop-on-defect            Stop execution upon first not-passed test
  --stop-on-error             Stop execution upon first error
  --stop-on-failure           Stop execution upon first error or failure
  --stop-on-warning           Stop execution upon first warning
  --stop-on-risky             Stop execution upon first risky test
  --stop-on-skipped           Stop execution upon first skipped test
  --stop-on-incomplete        Stop execution upon first incomplete test
  --fail-on-incomplete        Treat incomplete tests as failures
  --fail-on-risky             Treat risky tests as failures
  --fail-on-skipped           Treat skipped tests as failures
  --fail-on-warning           Treat tests with warnings as failures
  -v|--verbose                Output more verbose information
  --debug                     Display debugging information

  --repeat &lt;times&gt;            Runs the test(s) repeatedly
  --teamcity                  Report test execution progress in TeamCity format
  --testdox                   Report test execution progress in TestDox format
  --testdox-group             Only include tests from the specified group(s)
  --testdox-exclude-group     Exclude tests from the specified group(s)
  --no-interaction            Disable TestDox progress animation
  --printer &lt;printer&gt;         TestListener implementation to use

  --order-by &lt;order&gt;          Run tests in order: default|defects|duration|no-depends|random|reverse|size
  --random-order-seed &lt;N&gt;     Use a specific random seed &lt;N&gt; for random order
  --cache-result              Write test results to cache file
  --do-not-cache-result       Do not write test results to cache file

Configuration Options:

  --prepend &lt;file&gt;            A PHP script that is included as early as possible
  --bootstrap &lt;file&gt;          A PHP script that is included before the tests run
  -c|--configuration &lt;file&gt;   Read configuration from XML file
  --no-configuration          Ignore default configuration file (phpunit.xml)
  --extensions &lt;extensions&gt;   A comma separated list of PHPUnit extensions to load
  --no-extensions             Do not load PHPUnit extensions
  --include-path &lt;path(s)&gt;    Prepend PHP&#039;s include_path with given path(s)
  -d &lt;key[=value]&gt;            Sets a php.ini value
  --cache-result-file &lt;file&gt;  Specify result cache path and filename
  --generate-configuration    Generate configuration file with suggested settings
  --migrate-configuration     Migrate configuration file to current format

Miscellaneous Options:

  -h|--help                   Prints this usage information
  --version                   Prints the version and exits
  --atleast-version &lt;min&gt;     Checks that version is greater than min and exits
  --check-version             Checks whether PHPUnit is the latest version and exits
</div>
        </details>
    </div>
    <div class='suite failure'>
        <h2>integration - ✗ 失敗</h2>
        <p>執行時間: 0.26 秒</p>
        <details>
            <summary>詳細輸出</summary>
            <div class='output'>PHPUnit 9.6.23 by Sebastian Bergmann and contributors.

Usage:
  phpunit [options] UnitTest.php
  phpunit [options] &lt;directory&gt;

Code Coverage Options:

  --coverage-clover &lt;file&gt;    Generate code coverage report in Clover XML format
  --coverage-cobertura &lt;file&gt; Generate code coverage report in Cobertura XML format
  --coverage-crap4j &lt;file&gt;    Generate code coverage report in Crap4J XML format
  --coverage-html &lt;dir&gt;       Generate code coverage report in HTML format
  --coverage-php &lt;file&gt;       Export PHP_CodeCoverage object to file
  --coverage-text=&lt;file&gt;      Generate code coverage report in text format [default: standard output]
  --coverage-xml &lt;dir&gt;        Generate code coverage report in PHPUnit XML format
  --coverage-cache &lt;dir&gt;      Cache static analysis results
  --warm-coverage-cache       Warm static analysis cache
  --coverage-filter &lt;dir&gt;     Include &lt;dir&gt; in code coverage analysis
  --path-coverage             Perform path coverage analysis
  --disable-coverage-ignore   Disable annotations for ignoring code coverage
  --no-coverage               Ignore code coverage configuration

Logging Options:

  --log-junit &lt;file&gt;          Log test execution in JUnit XML format to file
  --log-teamcity &lt;file&gt;       Log test execution in TeamCity format to file
  --testdox-html &lt;file&gt;       Write agile documentation in HTML format to file
  --testdox-text &lt;file&gt;       Write agile documentation in Text format to file
  --testdox-xml &lt;file&gt;        Write agile documentation in XML format to file
  --reverse-list              Print defects in reverse order
  --no-logging                Ignore logging configuration

Test Selection Options:

  --list-suites               List available test suites
  --testsuite &lt;name&gt;          Filter which testsuite to run
  --list-groups               List available test groups
  --group &lt;name&gt;              Only runs tests from the specified group(s)
  --exclude-group &lt;name&gt;      Exclude tests from the specified group(s)
  --covers &lt;name&gt;             Only runs tests annotated with &quot;@covers &lt;name&gt;&quot;
  --uses &lt;name&gt;               Only runs tests annotated with &quot;@uses &lt;name&gt;&quot;
  --list-tests                List available tests
  --list-tests-xml &lt;file&gt;     List available tests in XML format
  --filter &lt;pattern&gt;          Filter which tests to run
  --test-suffix &lt;suffixes&gt;    Only search for test in files with specified suffix(es). Default: Test.php,.phpt

Test Execution Options:

  --dont-report-useless-tests Do not report tests that do not test anything
  --strict-coverage           Be strict about @covers annotation usage
  --strict-global-state       Be strict about changes to global state
  --disallow-test-output      Be strict about output during tests
  --disallow-resource-usage   Be strict about resource usage during small tests
  --enforce-time-limit        Enforce time limit based on test size
  --default-time-limit &lt;sec&gt;  Timeout in seconds for tests without @small, @medium or @large
  --disallow-todo-tests       Disallow @todo-annotated tests

  --process-isolation         Run each test in a separate PHP process
  --globals-backup            Backup and restore $GLOBALS for each test
  --static-backup             Backup and restore static attributes for each test

  --colors &lt;flag&gt;             Use colors in output (&quot;never&quot;, &quot;auto&quot; or &quot;always&quot;)
  --columns &lt;n&gt;               Number of columns to use for progress output
  --columns max               Use maximum number of columns for progress output
  --stderr                    Write to STDERR instead of STDOUT
  --stop-on-defect            Stop execution upon first not-passed test
  --stop-on-error             Stop execution upon first error
  --stop-on-failure           Stop execution upon first error or failure
  --stop-on-warning           Stop execution upon first warning
  --stop-on-risky             Stop execution upon first risky test
  --stop-on-skipped           Stop execution upon first skipped test
  --stop-on-incomplete        Stop execution upon first incomplete test
  --fail-on-incomplete        Treat incomplete tests as failures
  --fail-on-risky             Treat risky tests as failures
  --fail-on-skipped           Treat skipped tests as failures
  --fail-on-warning           Treat tests with warnings as failures
  -v|--verbose                Output more verbose information
  --debug                     Display debugging information

  --repeat &lt;times&gt;            Runs the test(s) repeatedly
  --teamcity                  Report test execution progress in TeamCity format
  --testdox                   Report test execution progress in TestDox format
  --testdox-group             Only include tests from the specified group(s)
  --testdox-exclude-group     Exclude tests from the specified group(s)
  --no-interaction            Disable TestDox progress animation
  --printer &lt;printer&gt;         TestListener implementation to use

  --order-by &lt;order&gt;          Run tests in order: default|defects|duration|no-depends|random|reverse|size
  --random-order-seed &lt;N&gt;     Use a specific random seed &lt;N&gt; for random order
  --cache-result              Write test results to cache file
  --do-not-cache-result       Do not write test results to cache file

Configuration Options:

  --prepend &lt;file&gt;            A PHP script that is included as early as possible
  --bootstrap &lt;file&gt;          A PHP script that is included before the tests run
  -c|--configuration &lt;file&gt;   Read configuration from XML file
  --no-configuration          Ignore default configuration file (phpunit.xml)
  --extensions &lt;extensions&gt;   A comma separated list of PHPUnit extensions to load
  --no-extensions             Do not load PHPUnit extensions
  --include-path &lt;path(s)&gt;    Prepend PHP&#039;s include_path with given path(s)
  -d &lt;key[=value]&gt;            Sets a php.ini value
  --cache-result-file &lt;file&gt;  Specify result cache path and filename
  --generate-configuration    Generate configuration file with suggested settings
  --migrate-configuration     Migrate configuration file to current format

Miscellaneous Options:

  -h|--help                   Prints this usage information
  --version                   Prints the version and exits
  --atleast-version &lt;min&gt;     Checks that version is greater than min and exits
  --check-version             Checks whether PHPUnit is the latest version and exits
</div>
        </details>
    </div>
    <div class='suite failure'>
        <h2>performance - ✗ 失敗</h2>
        <p>執行時間: 0.23 秒</p>
        <details>
            <summary>詳細輸出</summary>
            <div class='output'>PHPUnit 9.6.23 by Sebastian Bergmann and contributors.

Usage:
  phpunit [options] UnitTest.php
  phpunit [options] &lt;directory&gt;

Code Coverage Options:

  --coverage-clover &lt;file&gt;    Generate code coverage report in Clover XML format
  --coverage-cobertura &lt;file&gt; Generate code coverage report in Cobertura XML format
  --coverage-crap4j &lt;file&gt;    Generate code coverage report in Crap4J XML format
  --coverage-html &lt;dir&gt;       Generate code coverage report in HTML format
  --coverage-php &lt;file&gt;       Export PHP_CodeCoverage object to file
  --coverage-text=&lt;file&gt;      Generate code coverage report in text format [default: standard output]
  --coverage-xml &lt;dir&gt;        Generate code coverage report in PHPUnit XML format
  --coverage-cache &lt;dir&gt;      Cache static analysis results
  --warm-coverage-cache       Warm static analysis cache
  --coverage-filter &lt;dir&gt;     Include &lt;dir&gt; in code coverage analysis
  --path-coverage             Perform path coverage analysis
  --disable-coverage-ignore   Disable annotations for ignoring code coverage
  --no-coverage               Ignore code coverage configuration

Logging Options:

  --log-junit &lt;file&gt;          Log test execution in JUnit XML format to file
  --log-teamcity &lt;file&gt;       Log test execution in TeamCity format to file
  --testdox-html &lt;file&gt;       Write agile documentation in HTML format to file
  --testdox-text &lt;file&gt;       Write agile documentation in Text format to file
  --testdox-xml &lt;file&gt;        Write agile documentation in XML format to file
  --reverse-list              Print defects in reverse order
  --no-logging                Ignore logging configuration

Test Selection Options:

  --list-suites               List available test suites
  --testsuite &lt;name&gt;          Filter which testsuite to run
  --list-groups               List available test groups
  --group &lt;name&gt;              Only runs tests from the specified group(s)
  --exclude-group &lt;name&gt;      Exclude tests from the specified group(s)
  --covers &lt;name&gt;             Only runs tests annotated with &quot;@covers &lt;name&gt;&quot;
  --uses &lt;name&gt;               Only runs tests annotated with &quot;@uses &lt;name&gt;&quot;
  --list-tests                List available tests
  --list-tests-xml &lt;file&gt;     List available tests in XML format
  --filter &lt;pattern&gt;          Filter which tests to run
  --test-suffix &lt;suffixes&gt;    Only search for test in files with specified suffix(es). Default: Test.php,.phpt

Test Execution Options:

  --dont-report-useless-tests Do not report tests that do not test anything
  --strict-coverage           Be strict about @covers annotation usage
  --strict-global-state       Be strict about changes to global state
  --disallow-test-output      Be strict about output during tests
  --disallow-resource-usage   Be strict about resource usage during small tests
  --enforce-time-limit        Enforce time limit based on test size
  --default-time-limit &lt;sec&gt;  Timeout in seconds for tests without @small, @medium or @large
  --disallow-todo-tests       Disallow @todo-annotated tests

  --process-isolation         Run each test in a separate PHP process
  --globals-backup            Backup and restore $GLOBALS for each test
  --static-backup             Backup and restore static attributes for each test

  --colors &lt;flag&gt;             Use colors in output (&quot;never&quot;, &quot;auto&quot; or &quot;always&quot;)
  --columns &lt;n&gt;               Number of columns to use for progress output
  --columns max               Use maximum number of columns for progress output
  --stderr                    Write to STDERR instead of STDOUT
  --stop-on-defect            Stop execution upon first not-passed test
  --stop-on-error             Stop execution upon first error
  --stop-on-failure           Stop execution upon first error or failure
  --stop-on-warning           Stop execution upon first warning
  --stop-on-risky             Stop execution upon first risky test
  --stop-on-skipped           Stop execution upon first skipped test
  --stop-on-incomplete        Stop execution upon first incomplete test
  --fail-on-incomplete        Treat incomplete tests as failures
  --fail-on-risky             Treat risky tests as failures
  --fail-on-skipped           Treat skipped tests as failures
  --fail-on-warning           Treat tests with warnings as failures
  -v|--verbose                Output more verbose information
  --debug                     Display debugging information

  --repeat &lt;times&gt;            Runs the test(s) repeatedly
  --teamcity                  Report test execution progress in TeamCity format
  --testdox                   Report test execution progress in TestDox format
  --testdox-group             Only include tests from the specified group(s)
  --testdox-exclude-group     Exclude tests from the specified group(s)
  --no-interaction            Disable TestDox progress animation
  --printer &lt;printer&gt;         TestListener implementation to use

  --order-by &lt;order&gt;          Run tests in order: default|defects|duration|no-depends|random|reverse|size
  --random-order-seed &lt;N&gt;     Use a specific random seed &lt;N&gt; for random order
  --cache-result              Write test results to cache file
  --do-not-cache-result       Do not write test results to cache file

Configuration Options:

  --prepend &lt;file&gt;            A PHP script that is included as early as possible
  --bootstrap &lt;file&gt;          A PHP script that is included before the tests run
  -c|--configuration &lt;file&gt;   Read configuration from XML file
  --no-configuration          Ignore default configuration file (phpunit.xml)
  --extensions &lt;extensions&gt;   A comma separated list of PHPUnit extensions to load
  --no-extensions             Do not load PHPUnit extensions
  --include-path &lt;path(s)&gt;    Prepend PHP&#039;s include_path with given path(s)
  -d &lt;key[=value]&gt;            Sets a php.ini value
  --cache-result-file &lt;file&gt;  Specify result cache path and filename
  --generate-configuration    Generate configuration file with suggested settings
  --migrate-configuration     Migrate configuration file to current format

Miscellaneous Options:

  -h|--help                   Prints this usage information
  --version                   Prints the version and exits
  --atleast-version &lt;min&gt;     Checks that version is greater than min and exits
  --check-version             Checks whether PHPUnit is the latest version and exits
</div>
        </details>
    </div>
    <div class='summary'>
        <h2>執行摘要</h2>
        <ul>
            <li>總測試套件: 3</li>
            <li>通過: 0</li>
            <li>失敗: 3</li>
            <li>總執行時間: 0.77 秒</li>
        </ul>
    </div>
</body>
</html>