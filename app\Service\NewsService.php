<?php

namespace App\Service;

use App\Model\NewsArticleModel;
use App\Model\NewsSourceModel;
use Exception;

/**
 * 新聞服務
 * 處理新聞抓取、管理和展示
 */
class NewsService extends BaseService
{
    private NewsArticleModel $articleModel;
    private NewsSourceModel $sourceModel;
    
    public function __construct()
    {
        parent::__construct();
        $this->articleModel = new NewsArticleModel();
        $this->sourceModel = new NewsSourceModel();
    }

    /**
     * 抓取新聞
     * 
     * @param int|null $sourceId 指定新聞源ID，null表示抓取所有
     * @return array
     */
    public function crawlNews(?int $sourceId = null): array
    {
        try {
            $sources = $sourceId ? [$this->sourceModel->find($sourceId)] : $this->getActiveSources();
            
            if (empty($sources)) {
                return $this->formatResponse(false, null, '沒有可用的新聞源');
            }

            $results = [
                'total_sources' => count($sources),
                'success_sources' => 0,
                'failed_sources' => 0,
                'new_articles' => 0,
                'updated_articles' => 0,
                'errors' => []
            ];

            foreach ($sources as $source) {
                if (!$source) continue;
                
                try {
                    $crawlResult = $this->crawlFromSource($source);
                    
                    if ($crawlResult['success']) {
                        $results['success_sources']++;
                        $results['new_articles'] += $crawlResult['new_articles'];
                        $results['updated_articles'] += $crawlResult['updated_articles'];
                        
                        // 更新最後抓取時間
                        $this->sourceModel->update($source['id'], ['lastCrawlAt' => date('Y-m-d H:i:s')]);
                    } else {
                        $results['failed_sources']++;
                        $results['errors'][] = "來源 {$source['name']}: " . $crawlResult['error'];
                    }

                } catch (Exception $e) {
                    $results['failed_sources']++;
                    $results['errors'][] = "來源 {$source['name']}: " . $e->getMessage();
                }
            }

            $this->log('info', '新聞抓取完成', $results);

            return $this->formatResponse(true, $results, '新聞抓取完成');

        } catch (Exception $e) {
            return $this->handleException($e, '抓取新聞');
        }
    }

    /**
     * 取得最新新聞
     * 
     * @param int $limit
     * @param array $filters
     * @return array
     */
    public function getLatestNews(int $limit = 20, array $filters = []): array
    {
        try {
            $conditions = [];
            
            if (!empty($filters['source_id'])) {
                $conditions['sourceId'] = $filters['source_id'];
            }
            
            if (!empty($filters['date_from'])) {
                $conditions['publishedAt >='] = $filters['date_from'];
            }
            
            if (!empty($filters['date_to'])) {
                $conditions['publishedAt <='] = $filters['date_to'];
            }

            $articles = $this->articleModel->findAll($conditions, 'publishedAt DESC', $limit);

            // 加入來源資訊
            foreach ($articles as &$article) {
                $source = $this->sourceModel->find($article['sourceId']);
                $article['source_name'] = $source['name'] ?? '未知來源';
            }

            return $this->formatResponse(true, $articles);

        } catch (Exception $e) {
            return $this->handleException($e, '取得最新新聞');
        }
    }

    /**
     * 取得熱門新聞
     * 
     * @param int $limit
     * @param int $days
     * @return array
     */
    public function getPopularNews(int $limit = 10, int $days = 7): array
    {
        try {
            $articles = $this->articleModel->getPopular($limit, $days);

            return $this->formatResponse(true, $articles);

        } catch (Exception $e) {
            return $this->handleException($e, '取得熱門新聞');
        }
    }

    /**
     * 取得新聞詳情
     * 
     * @param int $articleId
     * @param bool $incrementView 是否增加瀏覽次數
     * @return array
     */
    public function getNewsDetail(int $articleId, bool $incrementView = true): array
    {
        try {
            $article = $this->articleModel->find($articleId);
            
            if (!$article) {
                return $this->formatResponse(false, null, '新聞不存在');
            }

            // 增加瀏覽次數
            if ($incrementView) {
                $this->articleModel->incrementViewCount($articleId);
                $article['viewCount']++;
            }

            // 取得來源資訊
            $source = $this->sourceModel->find($article['sourceId']);
            $article['source'] = $source;

            // 取得相關新聞
            $relatedNews = $this->getRelatedNews($articleId, 5);
            $article['related_news'] = $relatedNews['data'] ?? [];

            return $this->formatResponse(true, $article);

        } catch (Exception $e) {
            return $this->handleException($e, '取得新聞詳情');
        }
    }

    /**
     * 取得相關新聞
     * 
     * @param int $articleId
     * @param int $limit
     * @return array
     */
    public function getRelatedNews(int $articleId, int $limit = 5): array
    {
        try {
            $article = $this->articleModel->find($articleId);
            
            if (!$article) {
                return $this->formatResponse(false, null, '新聞不存在');
            }

            // 基於標題關鍵字找相關新聞
            $keywords = $this->extractKeywordsFromTitle($article['title']);
            
            if (empty($keywords)) {
                return $this->formatResponse(true, []);
            }

            $sql = "
                SELECT na.*, ns.name as source_name
                FROM newsArticle na
                JOIN newsSource ns ON na.sourceId = ns.id
                WHERE na.id != ?
                AND (";
            
            $params = [$articleId];
            $conditions = [];
            
            foreach ($keywords as $keyword) {
                $conditions[] = "na.title LIKE ?";
                $params[] = "%{$keyword}%";
            }
            
            $sql .= implode(' OR ', $conditions);
            $sql .= ") ORDER BY na.publishedAt DESC LIMIT ?";
            $params[] = $limit;

            $relatedNews = $this->db->fetchAll($sql, $params);

            return $this->formatResponse(true, $relatedNews);

        } catch (Exception $e) {
            return $this->handleException($e, '取得相關新聞');
        }
    }

    /**
     * 取得新聞統計
     * 
     * @return array
     */
    public function getNewsStatistics(): array
    {
        try {
            $stats = $this->articleModel->getStatistics();
            $sourceStats = $this->articleModel->getStatisticsBySource();

            $result = array_merge($stats, [
                'source_statistics' => $sourceStats
            ]);

            return $this->formatResponse(true, $result);

        } catch (Exception $e) {
            return $this->handleException($e, '取得新聞統計');
        }
    }

    /**
     * 管理新聞源
     * 
     * @param array $sourceData
     * @return array
     */
    public function addNewsSource(array $sourceData): array
    {
        try {
            $this->validateRequired($sourceData, ['name', 'url']);
            
            $sourceData = $this->sanitizeInput($sourceData);
            
            // 檢查URL是否已存在
            $existing = $this->sourceModel->findFirst(['url' => $sourceData['url']]);
            if ($existing) {
                return $this->formatResponse(false, null, '該新聞源URL已存在');
            }

            // 驗證RSS URL
            if (!empty($sourceData['rssUrl']) && !$this->validateRssUrl($sourceData['rssUrl'])) {
                return $this->formatResponse(false, null, 'RSS URL格式不正確或無法訪問');
            }

            $sourceId = $this->sourceModel->create($sourceData);

            $this->log('info', '新聞源添加成功', ['source_id' => $sourceId, 'name' => $sourceData['name']]);

            return $this->formatResponse(true, ['source_id' => $sourceId], '新聞源添加成功');

        } catch (Exception $e) {
            return $this->handleException($e, '添加新聞源');
        }
    }

    /**
     * 清理舊新聞
     * 
     * @param int $days 保留天數
     * @return array
     */
    public function cleanupOldNews(int $days = 90): array
    {
        try {
            $deletedCount = $this->articleModel->cleanupOldArticles($days);

            $this->log('info', '舊新聞清理完成', ['deleted_count' => $deletedCount, 'days' => $days]);

            return $this->formatResponse(true, [
                'deleted_count' => $deletedCount,
                'retention_days' => $days
            ], '舊新聞清理完成');

        } catch (Exception $e) {
            return $this->handleException($e, '清理舊新聞');
        }
    }

    /**
     * 從指定來源抓取新聞
     * 
     * @param array $source
     * @return array
     */
    private function crawlFromSource(array $source): array
    {
        try {
            if (empty($source['rssUrl'])) {
                return ['success' => false, 'error' => 'RSS URL未設定'];
            }

            // 取得RSS內容
            $rssContent = $this->fetchRssContent($source['rssUrl']);
            
            if (!$rssContent) {
                return ['success' => false, 'error' => '無法取得RSS內容'];
            }

            // 解析RSS
            $articles = $this->parseRssContent($rssContent, $source);
            
            $newArticles = 0;
            $updatedArticles = 0;

            foreach ($articles as $articleData) {
                // 檢查是否已存在
                $existing = $this->articleModel->findByOriginalUrl($articleData['originalUrl']);
                
                if ($existing) {
                    // 更新現有文章
                    $updateData = [
                        'title' => $articleData['title'],
                        'summary' => $articleData['summary'],
                        'content' => $articleData['content']
                    ];
                    
                    $this->articleModel->update($existing['id'], $updateData);
                    $updatedArticles++;
                } else {
                    // 建立新文章
                    $this->articleModel->create($articleData);
                    $newArticles++;
                }
            }

            return [
                'success' => true,
                'new_articles' => $newArticles,
                'updated_articles' => $updatedArticles
            ];

        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * 取得活躍的新聞源
     * 
     * @return array
     */
    private function getActiveSources(): array
    {
        return $this->sourceModel->findAll(['isActive' => 1]);
    }

    /**
     * 取得RSS內容
     * 
     * @param string $rssUrl
     * @return string|null
     */
    private function fetchRssContent(string $rssUrl): ?string
    {
        $context = stream_context_create([
            'http' => [
                'timeout' => $this->config['news']['timeout'],
                'user_agent' => 'Tech.VG News Crawler 1.0'
            ]
        ]);

        $content = @file_get_contents($rssUrl, false, $context);
        
        return $content ?: null;
    }

    /**
     * 解析RSS內容
     * 
     * @param string $rssContent
     * @param array $source
     * @return array
     */
    private function parseRssContent(string $rssContent, array $source): array
    {
        $articles = [];
        
        try {
            $xml = new \SimpleXMLElement($rssContent);
            
            foreach ($xml->channel->item as $item) {
                $title = (string) $item->title;
                $link = (string) $item->link;
                $description = (string) $item->description;
                $pubDate = (string) $item->pubDate;
                
                // 清理HTML標籤
                $summary = strip_tags($description);
                $summary = mb_substr($summary, 0, 500);
                
                $articles[] = [
                    'sourceId' => $source['id'],
                    'title' => $title,
                    'summary' => $summary,
                    'content' => $description,
                    'originalUrl' => $link,
                    'publishedAt' => $this->parseDate($pubDate),
                    'crawledAt' => date('Y-m-d H:i:s'),
                    'contentHash' => hash('sha256', $title . $description),
                    'isIndexed' => 0
                ];
            }

        } catch (Exception $e) {
            $this->log('error', 'RSS解析失敗', ['error' => $e->getMessage()]);
        }

        return $articles;
    }

    /**
     * 解析日期
     * 
     * @param string $dateString
     * @return string
     */
    private function parseDate(string $dateString): string
    {
        try {
            $timestamp = strtotime($dateString);
            return $timestamp ? date('Y-m-d H:i:s', $timestamp) : date('Y-m-d H:i:s');
        } catch (Exception $e) {
            return date('Y-m-d H:i:s');
        }
    }

    /**
     * 從標題提取關鍵字
     * 
     * @param string $title
     * @return array
     */
    private function extractKeywordsFromTitle(string $title): array
    {
        // 移除標點符號
        $cleanTitle = preg_replace('/[^\p{L}\p{N}\s]/u', ' ', $title);
        
        // 提取中文詞彙（2-4個字符）
        preg_match_all('/[\p{Han}]{2,4}/u', $cleanTitle, $chineseWords);
        
        // 提取英文單詞（3個字符以上）
        preg_match_all('/[a-zA-Z]{3,}/u', $cleanTitle, $englishWords);
        
        $keywords = array_merge($chineseWords[0], $englishWords[0]);
        
        // 移除常見停用詞
        $stopWords = ['的', '了', '在', '是', '和', '與', '或', '但', '因為', '所以', 'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for'];
        $keywords = array_diff($keywords, $stopWords);
        
        return array_slice(array_unique($keywords), 0, 5);
    }

    /**
     * 驗證RSS URL
     * 
     * @param string $rssUrl
     * @return bool
     */
    private function validateRssUrl(string $rssUrl): bool
    {
        if (!filter_var($rssUrl, FILTER_VALIDATE_URL)) {
            return false;
        }

        // 嘗試取得RSS內容
        $context = stream_context_create([
            'http' => [
                'timeout' => 10,
                'user_agent' => 'Tech.VG RSS Validator 1.0'
            ]
        ]);

        $content = @file_get_contents($rssUrl, false, $context);
        
        if (!$content) {
            return false;
        }

        // 檢查是否為有效的XML
        try {
            $xml = new \SimpleXMLElement($content);
            return isset($xml->channel);
        } catch (Exception $e) {
            return false;
        }
    }
}