<?php
/**
 * 路由調試腳本
 */

// 錯誤報告設定
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 設定時區
date_default_timezone_set('Asia/Taipei');

// 定義應用程式根目錄
define('APP_ROOT', __DIR__);

// 載入自動載入器
require_once __DIR__ . '/htdocs/vendor/autoload.php';

// 載入環境變數
if (file_exists(APP_ROOT . '/.env')) {
    $lines = file(APP_ROOT . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            [$key, $value] = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

try {
    // 載入配置
    $config = require APP_ROOT . '/config/app.php';
    
    // 載入路由
    $router = require APP_ROOT . '/config/routes.php';
    
    echo "=== 路由調試信息 ===\n";
    echo "時間: " . date('Y-m-d H:i:s') . "\n";
    echo "路由器類型: " . get_class($router) . "\n\n";
    
    // 使用反射來訪問私有屬性
    $reflection = new ReflectionClass($router);
    $routesProperty = $reflection->getProperty('routes');
    $routesProperty->setAccessible(true);
    $routes = $routesProperty->getValue($router);
    
    echo "註冊的路由總數: " . count($routes) . "\n\n";
    
    // 顯示所有路由
    echo "=== 所有註冊的路由 ===\n";
    foreach ($routes as $index => $route) {
        echo sprintf(
            "%d. %s %s -> %s\n",
            $index + 1,
            $route['method'],
            $route['pattern'],
            is_callable($route['handler']) ? 'Closure' : $route['handler']
        );
        echo "   正則表達式: " . $route['regex'] . "\n";
        if (!empty($route['middleware'])) {
            echo "   中間件: " . implode(', ', $route['middleware']) . "\n";
        }
        echo "\n";
    }
    
    // 測試特定路由
    echo "=== 路由匹配測試 ===\n";
    $testRoutes = [
        ['GET', '/api'],
        ['GET', '/api/'],
        ['GET', '/api/health'],
        ['GET', '/'],
        ['GET', '/home']
    ];
    
    foreach ($testRoutes as [$method, $uri]) {
        echo "測試: {$method} {$uri}\n";
        
        // 使用反射調用私有方法
        $findMethod = $reflection->getMethod('findMatchingRoute');
        $findMethod->setAccessible(true);
        $result = $findMethod->invoke($router, $method, $uri);
        
        if ($result) {
            echo "  ✅ 匹配成功\n";
            echo "  處理器: " . (is_callable($result['handler']) ? 'Closure' : $result['handler']) . "\n";
        } else {
            echo "  ❌ 未找到匹配的路由\n";
        }
        echo "\n";
    }
    
} catch (Exception $e) {
    echo "錯誤: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行號: " . $e->getLine() . "\n";
    echo "堆疊追蹤:\n" . $e->getTraceAsString() . "\n";
}
?>
