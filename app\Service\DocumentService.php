<?php

namespace App\Service;

use App\Model\DocumentModel;
use App\Model\DocumentContentModel;
use Exception;

/**
 * 文檔服務
 * 處理文檔上傳、管理和處理相關業務邏輯
 */
class DocumentService extends BaseService
{
    private DocumentModel $documentModel;
    private DocumentContentModel $contentModel;
    private IndexService $indexService;
    
    public function __construct()
    {
        parent::__construct();
        $this->documentModel = new DocumentModel();
        $this->contentModel = new DocumentContentModel();
        $this->indexService = new IndexService();
    }

    /**
     * 上傳文檔
     * 
     * @param array $fileData $_FILES中的文件資料
     * @param array $metadata 文檔元資料
     * @return array
     */
    public function uploadDocument(array $fileData, array $metadata = []): array
    {
        try {
            // 驗證文件
            $validation = $this->validateFile($fileData);
            if (!$validation['valid']) {
                return $this->formatResponse(false, null, $validation['error']);
            }

            // 開始資料庫交易
            $this->db->beginTransaction();

            // 儲存文件
            $filePath = $this->saveFile($fileData);
            
            // 建立文檔記錄
            $documentData = [
                'title' => $metadata['title'] ?? pathinfo($fileData['name'], PATHINFO_FILENAME),
                'fileName' => $fileData['name'],
                'fileType' => $this->getFileType($fileData['name']),
                'fileSize' => $fileData['size'],
                'filePath' => $filePath,
                'uploadedBy' => $metadata['uploadedBy'] ?? null,
                'status' => 'processing'
            ];

            $documentId = $this->documentModel->create($documentData);

            // 處理文檔內容
            $content = $this->extractContent($filePath, $documentData['fileType']);
            
            if ($content) {
                $this->contentModel->updateContent($documentId, $content);
                
                // 建立索引
                $this->indexService->indexDocument($documentId, $content);
                
                // 更新狀態為已索引
                $this->documentModel->updateStatus($documentId, 'indexed');
            } else {
                $this->documentModel->updateStatus($documentId, 'failed');
            }

            $this->db->commit();

            $this->log('info', '文檔上傳成功', ['document_id' => $documentId, 'file_name' => $fileData['name']]);

            return $this->formatResponse(true, [
                'document_id' => $documentId,
                'file_name' => $fileData['name'],
                'status' => $content ? 'indexed' : 'failed'
            ], '文檔上傳成功');

        } catch (Exception $e) {
            $this->db->rollback();
            return $this->handleException($e, '文檔上傳');
        }
    }

    /**
     * 取得文檔列表
     * 
     * @param array $filters 過濾條件
     * @param int $page 頁碼
     * @param int $perPage 每頁數量
     * @return array
     */
    public function getDocuments(array $filters = [], int $page = 1, int $perPage = 20): array
    {
        try {
            $pagination = $this->processPagination($page, $perPage);
            
            // 建立查詢條件
            $conditions = [];
            
            if (!empty($filters['status'])) {
                $conditions['status'] = $filters['status'];
            }
            
            if (!empty($filters['file_type'])) {
                $conditions['fileType'] = $filters['file_type'];
            }
            
            if (!empty($filters['uploaded_by'])) {
                $conditions['uploadedBy'] = $filters['uploaded_by'];
            }

            // 取得分頁資料
            $result = $this->documentModel->paginate($pagination['page'], $pagination['per_page'], $conditions, 'createdAt DESC');

            return $this->formatResponse(true, $result['data'], null, [
                'pagination' => $result['pagination']
            ]);

        } catch (Exception $e) {
            return $this->handleException($e, '取得文檔列表');
        }
    }

    /**
     * 取得文檔詳情
     * 
     * @param int $documentId
     * @param bool $includeContent 是否包含內容
     * @return array
     */
    public function getDocument(int $documentId, bool $includeContent = false): array
    {
        try {
            $document = $this->documentModel->find($documentId);
            
            if (!$document) {
                return $this->formatResponse(false, null, '文檔不存在');
            }

            if ($includeContent) {
                $content = $this->contentModel->findByDocumentId($documentId);
                $document['content'] = $content['content'] ?? null;
                $document['word_count'] = $content['wordCount'] ?? 0;
            }

            return $this->formatResponse(true, $document);

        } catch (Exception $e) {
            return $this->handleException($e, '取得文檔詳情');
        }
    }

    /**
     * 更新文檔
     * 
     * @param int $documentId
     * @param array $data
     * @return array
     */
    public function updateDocument(int $documentId, array $data): array
    {
        try {
            $data = $this->sanitizeInput($data);
            
            $document = $this->documentModel->find($documentId);
            if (!$document) {
                return $this->formatResponse(false, null, '文檔不存在');
            }

            // 只允許更新特定欄位
            $allowedFields = ['title'];
            $updateData = array_intersect_key($data, array_flip($allowedFields));

            if (empty($updateData)) {
                return $this->formatResponse(false, null, '沒有可更新的資料');
            }

            $affected = $this->documentModel->update($documentId, $updateData);

            if ($affected > 0) {
                $this->log('info', '文檔更新成功', ['document_id' => $documentId, 'data' => $updateData]);
                return $this->formatResponse(true, null, '文檔更新成功');
            } else {
                return $this->formatResponse(false, null, '文檔更新失敗');
            }

        } catch (Exception $e) {
            return $this->handleException($e, '更新文檔');
        }
    }

    /**
     * 刪除文檔
     * 
     * @param int $documentId
     * @return array
     */
    public function deleteDocument(int $documentId): array
    {
        try {
            $document = $this->documentModel->find($documentId);
            if (!$document) {
                return $this->formatResponse(false, null, '文檔不存在');
            }

            $this->db->beginTransaction();

            // 刪除文件
            if (file_exists($document['filePath'])) {
                unlink($document['filePath']);
            }

            // 刪除資料庫記錄 (會自動刪除相關的內容和索引記錄)
            $this->documentModel->delete($documentId);

            $this->db->commit();

            $this->log('info', '文檔刪除成功', ['document_id' => $documentId, 'file_name' => $document['fileName']]);

            return $this->formatResponse(true, null, '文檔刪除成功');

        } catch (Exception $e) {
            $this->db->rollback();
            return $this->handleException($e, '刪除文檔');
        }
    }

    /**
     * 取得文檔統計資訊
     * 
     * @return array
     */
    public function getStatistics(): array
    {
        try {
            $stats = $this->documentModel->getStatistics();
            $contentStats = $this->contentModel->getContentStatistics();

            $result = array_merge($stats, $contentStats);
            
            // 格式化檔案大小
            $result['total_size_formatted'] = $this->formatBytes($result['total_size'] ?? 0);
            
            return $this->formatResponse(true, $result);

        } catch (Exception $e) {
            return $this->handleException($e, '取得統計資訊');
        }
    }

    /**
     * 重新建立文檔索引
     * 
     * @param int $documentId
     * @return array
     */
    public function reindexDocument(int $documentId): array
    {
        try {
            $document = $this->documentModel->find($documentId);
            if (!$document) {
                return $this->formatResponse(false, null, '文檔不存在');
            }

            $content = $this->contentModel->findByDocumentId($documentId);
            if (!$content) {
                return $this->formatResponse(false, null, '文檔內容不存在');
            }

            // 重新建立索引
            $this->indexService->reindexDocument($documentId, $content['content']);
            
            // 更新狀態
            $this->documentModel->updateStatus($documentId, 'indexed');

            $this->log('info', '文檔重新索引成功', ['document_id' => $documentId]);

            return $this->formatResponse(true, null, '文檔重新索引成功');

        } catch (Exception $e) {
            return $this->handleException($e, '重新建立索引');
        }
    }

    /**
     * 驗證上傳文件
     * 
     * @param array $fileData
     * @return array
     */
    private function validateFile(array $fileData): array
    {
        $result = ['valid' => false, 'error' => null];

        // 檢查上傳錯誤
        if ($fileData['error'] !== UPLOAD_ERR_OK) {
            $result['error'] = '文件上傳失敗';
            return $result;
        }

        // 檢查文件大小
        $maxSize = $this->config['upload']['max_size'];
        if ($fileData['size'] > $maxSize) {
            $result['error'] = '文件大小超過限制 (' . $this->formatBytes($maxSize) . ')';
            return $result;
        }

        // 檢查文件類型
        $allowedTypes = $this->config['upload']['allowed_types'];
        $fileExtension = strtolower(pathinfo($fileData['name'], PATHINFO_EXTENSION));
        
        if (!in_array($fileExtension, $allowedTypes)) {
            $result['error'] = '不支援的文件類型，僅支援: ' . implode(', ', $allowedTypes);
            return $result;
        }

        // 檢查文件內容
        if (!$this->isValidFileContent($fileData['tmp_name'], $fileExtension)) {
            $result['error'] = '文件內容格式不正確';
            return $result;
        }

        $result['valid'] = true;
        return $result;
    }

    /**
     * 儲存上傳文件
     * 
     * @param array $fileData
     * @return string 文件路徑
     * @throws Exception
     */
    private function saveFile(array $fileData): string
    {
        $uploadDir = $this->config['upload']['storage_path'];
        
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        // 生成唯一文件名
        $extension = pathinfo($fileData['name'], PATHINFO_EXTENSION);
        $fileName = date('Y/m/d/') . uniqid() . '.' . $extension;
        $filePath = $uploadDir . '/' . $fileName;
        
        // 建立目錄
        $fileDir = dirname($filePath);
        if (!is_dir($fileDir)) {
            mkdir($fileDir, 0755, true);
        }

        if (!move_uploaded_file($fileData['tmp_name'], $filePath)) {
            throw new Exception('文件儲存失敗');
        }

        return $filePath;
    }

    /**
     * 提取文件內容
     * 
     * @param string $filePath
     * @param string $fileType
     * @return string|null
     */
    private function extractContent(string $filePath, string $fileType): ?string
    {
        if (!file_exists($filePath)) {
            return null;
        }

        try {
            $content = file_get_contents($filePath);
            
            if ($content === false) {
                return null;
            }

            // 根據文件類型處理內容
            switch ($fileType) {
                case 'txt':
                    return $this->processTxtContent($content);
                case 'markdown':
                case 'md':
                    return $this->processMarkdownContent($content);
                default:
                    return $content;
            }

        } catch (Exception $e) {
            $this->log('error', '文件內容提取失敗', ['file_path' => $filePath, 'error' => $e->getMessage()]);
            return null;
        }
    }

    /**
     * 處理TXT內容
     * 
     * @param string $content
     * @return string
     */
    private function processTxtContent(string $content): string
    {
        // 轉換編碼為UTF-8
        $encoding = mb_detect_encoding($content, ['UTF-8', 'GBK', 'BIG5', 'ASCII'], true);
        if ($encoding && $encoding !== 'UTF-8') {
            $content = mb_convert_encoding($content, 'UTF-8', $encoding);
        }

        // 統一換行符
        $content = str_replace(["\r\n", "\r"], "\n", $content);
        
        // 移除多餘空白
        $content = preg_replace('/\n{3,}/', "\n\n", $content);
        
        return trim($content);
    }

    /**
     * 處理Markdown內容
     * 
     * @param string $content
     * @return string
     */
    private function processMarkdownContent(string $content): string
    {
        // 先處理基本格式
        $content = $this->processTxtContent($content);
        
        // 這裡可以添加Markdown特定的處理邏輯
        // 例如：提取標題、處理連結等
        
        return $content;
    }

    /**
     * 取得文件類型
     * 
     * @param string $fileName
     * @return string
     */
    private function getFileType(string $fileName): string
    {
        $extension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
        
        switch ($extension) {
            case 'md':
                return 'markdown';
            case 'txt':
                return 'txt';
            default:
                return $extension;
        }
    }

    /**
     * 驗證文件內容
     * 
     * @param string $filePath
     * @param string $extension
     * @return bool
     */
    private function isValidFileContent(string $filePath, string $extension): bool
    {
        // 檢查文件是否為文本文件
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $filePath);
        finfo_close($finfo);

        $allowedMimeTypes = [
            'text/plain',
            'text/markdown',
            'application/octet-stream' // 某些系統可能將文本文件識別為此類型
        ];

        return in_array($mimeType, $allowedMimeTypes);
    }

    /**
     * 格式化位元組大小
     * 
     * @param int $bytes
     * @return string
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $factor = floor((strlen($bytes) - 1) / 3);
        
        return sprintf("%.2f", $bytes / pow(1024, $factor)) . ' ' . $units[$factor];
    }
}