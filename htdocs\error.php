<?php
/**
 * 錯誤頁面處理器
 * 根據錯誤代碼顯示對應的錯誤頁面
 */

// 設定錯誤報告
error_reporting(0);
ini_set('display_errors', 0);

// 取得錯誤代碼
$errorCode = $_GET['code'] ?? $_SERVER['REDIRECT_STATUS'] ?? '500';

// 確保錯誤代碼是有效的
$validCodes = ['403', '404', '500'];
if (!in_array($errorCode, $validCodes)) {
    $errorCode = '500';
}

// 設定 HTTP 狀態碼
http_response_code((int)$errorCode);

// 錯誤頁面路徑
$errorPagePath = __DIR__ . '/error/' . $errorCode . '.html';

// 檢查錯誤頁面是否存在
if (file_exists($errorPagePath)) {
    // 讀取錯誤頁面內容
    $content = file_get_contents($errorPagePath);
    
    // 替換頁面中的 SignAttend 為 Tech.VG
    $content = str_replace('SignAttend', 'Tech.VG', $content);
    
    // 根據錯誤類型更新頁面標題和內容
    switch ($errorCode) {
        case '403':
            $content = str_replace(
                '<title>訪問被拒絕 - Tech.VG</title>',
                '<title>403 - 訪問被拒絕 | Tech.VG 科技新知檢索</title>',
                $content
            );
            break;
            
        case '404':
            $content = str_replace(
                '<title>頁面未找到 - Tech.VG</title>',
                '<title>404 - 頁面未找到 | Tech.VG 科技新知檢索</title>',
                $content
            );
            break;
            
        case '500':
            $content = str_replace(
                '<title>伺服器錯誤 - Tech.VG</title>',
                '<title>500 - 伺服器錯誤 | Tech.VG 科技新知檢索</title>',
                $content
            );
            break;
    }
    
    // 輸出錯誤頁面
    echo $content;
    
} else {
    // 如果錯誤頁面不存在，顯示簡單的錯誤訊息
    ?>
    <!DOCTYPE html>
    <html lang="zh-TW">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title><?= $errorCode ?> - 錯誤 | Tech.VG 科技新知檢索</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background-color: #f8f9fa;
                margin: 0;
                padding: 0;
                display: flex;
                justify-content: center;
                align-items: center;
                min-height: 100vh;
            }
            .error-container {
                text-align: center;
                background: white;
                padding: 2rem;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                max-width: 500px;
                margin: 1rem;
            }
            .error-code {
                font-size: 4rem;
                font-weight: bold;
                color: #dc3545;
                margin-bottom: 1rem;
            }
            .error-title {
                font-size: 1.5rem;
                color: #333;
                margin-bottom: 1rem;
            }
            .error-message {
                color: #666;
                margin-bottom: 2rem;
                line-height: 1.5;
            }
            .btn {
                display: inline-block;
                padding: 0.75rem 1.5rem;
                background-color: #007bff;
                color: white;
                text-decoration: none;
                border-radius: 4px;
                transition: background-color 0.3s;
                margin: 0 0.5rem;
            }
            .btn:hover {
                background-color: #0056b3;
            }
            .btn-secondary {
                background-color: #6c757d;
            }
            .btn-secondary:hover {
                background-color: #545b62;
            }
        </style>
    </head>
    <body>
        <div class="error-container">
            <div class="error-code"><?= htmlspecialchars($errorCode) ?></div>
            <div class="error-title">
                <?php
                switch ($errorCode) {
                    case '403':
                        echo '訪問被拒絕';
                        break;
                    case '404':
                        echo '頁面未找到';
                        break;
                    case '500':
                        echo '伺服器內部錯誤';
                        break;
                    default:
                        echo '發生錯誤';
                        break;
                }
                ?>
            </div>
            <div class="error-message">
                <?php
                switch ($errorCode) {
                    case '403':
                        echo '抱歉，您沒有權限訪問此資源。';
                        break;
                    case '404':
                        echo '抱歉，您要尋找的頁面不存在或已被移動。';
                        break;
                    case '500':
                        echo '抱歉，伺服器發生內部錯誤。我們正在努力修復此問題。';
                        break;
                    default:
                        echo '發生了未預期的錯誤，請稍後再試。';
                        break;
                }
                ?>
            </div>
            <div>
                <a href="/" class="btn">返回首頁</a>
                <a href="javascript:history.back()" class="btn btn-secondary">返回上頁</a>
            </div>
        </div>
    </body>
    </html>
    <?php
}

// 記錄錯誤日誌（如果可能）
try {
    $logData = [
        'timestamp' => date('Y-m-d H:i:s'),
        'error_code' => $errorCode,
        'request_uri' => $_SERVER['REQUEST_URI'] ?? '',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
        'ip' => $_SERVER['REMOTE_ADDR'] ?? '',
        'referer' => $_SERVER['HTTP_REFERER'] ?? ''
    ];
    
    $logDir = dirname(__DIR__) . '/storage/logs';
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $logFile = $logDir . '/error.log';
    file_put_contents($logFile, json_encode($logData, JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND | LOCK_EX);
    
} catch (Exception $e) {
    // 忽略日誌錯誤，避免無限循環
}
?>