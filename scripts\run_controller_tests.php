<?php

/**
 * 控制器測試運行腳本
 * 用於執行控制器相關的單元測試和整合測試
 */

require_once __DIR__ . '/../tests/bootstrap.php';

use Tests\TestSuite;
use PHPUnit\TextUI\TestRunner;
use PHPUnit\Framework\TestResult;

/**
 * 測試運行器類別
 */
class ControllerTestRunner
{
    private array $options;
    private TestResult $result;

    public function __construct(array $options = [])
    {
        $this->options = array_merge([
            'verbose' => false,
            'coverage' => false,
            'stop_on_failure' => false,
            'output_format' => 'text'
        ], $options);

        $this->result = new TestResult();
    }

    /**
     * 運行所有控制器測試
     */
    public function runAllControllerTests(): void
    {
        echo "=== 運行所有控制器測試 ===\n\n";

        $suite = TestSuite::controllerTestSuite();
        $this->runTestSuite($suite, '控制器測試');
    }

    /**
     * 運行單元測試
     */
    public function runUnitTests(): void
    {
        echo "=== 運行控制器單元測試 ===\n\n";

        $suite = TestSuite::unitTestSuite();
        $this->runTestSuite($suite, '單元測試');
    }

    /**
     * 運行整合測試
     */
    public function runIntegrationTests(): void
    {
        echo "=== 運行控制器整合測試 ===\n\n";

        $suite = TestSuite::integrationTestSuite();
        $this->runTestSuite($suite, '整合測試');
    }

    /**
     * 運行 API 測試
     */
    public function runApiTests(): void
    {
        echo "=== 運行 API 控制器測試 ===\n\n";

        $suite = TestSuite::apiTestSuite();
        $this->runTestSuite($suite, 'API 測試');
    }

    /**
     * 運行效能測試
     */
    public function runPerformanceTests(): void
    {
        echo "=== 運行效能測試 ===\n\n";

        $suite = TestSuite::performanceTestSuite();
        $this->runTestSuite($suite, '效能測試');
    }

    /**
     * 運行特定測試類別
     */
    public function runSpecificTest(string $testClass): void
    {
        echo "=== 運行特定測試: {$testClass} ===\n\n";

        if (!class_exists($testClass)) {
            echo "錯誤：測試類別 {$testClass} 不存在\n";
            return;
        }

        $suite = new \PHPUnit\Framework\TestSuite($testClass);
        $this->runTestSuite($suite, $testClass);
    }

    /**
     * 運行測試套件
     */
    private function runTestSuite(\PHPUnit\Framework\TestSuite $suite, string $suiteName): void
    {
        $startTime = microtime(true);
        
        echo "開始運行 {$suiteName}...\n";
        echo "測試數量: " . $suite->count() . "\n";
        echo str_repeat('-', 50) . "\n";

        // 設定測試結果監聽器
        $this->setupResultListeners();

        // 運行測試
        $runner = new TestRunner();
        $result = $runner->run($suite);

        $endTime = microtime(true);
        $duration = round($endTime - $startTime, 2);

        // 顯示結果
        $this->displayResults($result, $suiteName, $duration);
    }

    /**
     * 設定測試結果監聽器
     */
    private function setupResultListeners(): void
    {
        // 可以在這裡添加自訂的測試監聽器
        // 例如：進度顯示、日誌記錄等
    }

    /**
     * 顯示測試結果
     */
    private function displayResults(TestResult $result, string $suiteName, float $duration): void
    {
        echo str_repeat('=', 50) . "\n";
        echo "{$suiteName} 執行完成\n";
        echo str_repeat('=', 50) . "\n";

        echo "執行時間: {$duration} 秒\n";
        echo "測試總數: " . $result->count() . "\n";
        echo "成功: " . ($result->count() - $result->errorCount() - $result->failureCount()) . "\n";
        echo "失敗: " . $result->failureCount() . "\n";
        echo "錯誤: " . $result->errorCount() . "\n";
        echo "跳過: " . $result->skippedCount() . "\n";

        if ($result->wasSuccessful()) {
            echo "\n✅ 所有測試通過！\n";
        } else {
            echo "\n❌ 有測試失敗或錯誤\n";
            
            // 顯示失敗詳情
            if ($this->options['verbose']) {
                $this->displayFailures($result);
            }
        }

        echo "\n";
    }

    /**
     * 顯示失敗詳情
     */
    private function displayFailures(TestResult $result): void
    {
        if ($result->failureCount() > 0) {
            echo "\n--- 失敗詳情 ---\n";
            foreach ($result->failures() as $failure) {
                echo "❌ " . $failure->getTestName() . "\n";
                echo "   " . $failure->getExceptionAsString() . "\n\n";
            }
        }

        if ($result->errorCount() > 0) {
            echo "\n--- 錯誤詳情 ---\n";
            foreach ($result->errors() as $error) {
                echo "💥 " . $error->getTestName() . "\n";
                echo "   " . $error->getExceptionAsString() . "\n\n";
            }
        }
    }

    /**
     * 生成測試報告
     */
    public function generateReport(string $outputPath = null): void
    {
        $outputPath = $outputPath ?: __DIR__ . '/../tests/results/controller_test_report.html';
        
        echo "生成測試報告到: {$outputPath}\n";

        // 這裡可以實作 HTML 報告生成
        $reportContent = $this->buildHtmlReport();
        
        // 確保目錄存在
        $dir = dirname($outputPath);
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }

        file_put_contents($outputPath, $reportContent);
        echo "報告生成完成！\n";
    }

    /**
     * 建構 HTML 報告
     */
    private function buildHtmlReport(): string
    {
        $timestamp = date('Y-m-d H:i:s');
        
        return "
        <!DOCTYPE html>
        <html>
        <head>
            <title>控制器測試報告</title>
            <meta charset='utf-8'>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { background: #f5f5f5; padding: 20px; border-radius: 5px; }
                .success { color: green; }
                .failure { color: red; }
                .error { color: orange; }
                table { border-collapse: collapse; width: 100%; margin-top: 20px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
            </style>
        </head>
        <body>
            <div class='header'>
                <h1>Tech.VG 控制器測試報告</h1>
                <p>生成時間: {$timestamp}</p>
            </div>
            
            <h2>測試摘要</h2>
            <table>
                <tr><th>項目</th><th>數量</th></tr>
                <tr><td>總測試數</td><td id='total-tests'>-</td></tr>
                <tr><td class='success'>成功</td><td id='success-tests'>-</td></tr>
                <tr><td class='failure'>失敗</td><td id='failure-tests'>-</td></tr>
                <tr><td class='error'>錯誤</td><td id='error-tests'>-</td></tr>
            </table>
            
            <h2>測試詳情</h2>
            <div id='test-details'>
                <!-- 測試詳情將在這裡顯示 -->
            </div>
        </body>
        </html>
        ";
    }
}

/**
 * 主程式
 */
function main(): void
{
    $options = parseCommandLineOptions();
    $runner = new ControllerTestRunner($options);

    $command = $options['command'] ?? 'all';

    switch ($command) {
        case 'all':
            $runner->runAllControllerTests();
            break;
        case 'unit':
            $runner->runUnitTests();
            break;
        case 'integration':
            $runner->runIntegrationTests();
            break;
        case 'api':
            $runner->runApiTests();
            break;
        case 'performance':
            $runner->runPerformanceTests();
            break;
        case 'specific':
            if (isset($options['class'])) {
                $runner->runSpecificTest($options['class']);
            } else {
                echo "錯誤：請指定要運行的測試類別 (--class=ClassName)\n";
            }
            break;
        case 'report':
            $runner->generateReport($options['output'] ?? null);
            break;
        default:
            showUsage();
            break;
    }
}

/**
 * 解析命令行選項
 */
function parseCommandLineOptions(): array
{
    $options = [];
    
    $args = $_SERVER['argv'] ?? [];
    
    foreach ($args as $arg) {
        if (strpos($arg, '--') === 0) {
            $parts = explode('=', substr($arg, 2), 2);
            $key = $parts[0];
            $value = $parts[1] ?? true;
            $options[$key] = $value;
        } elseif (strpos($arg, '-') === 0) {
            $options[substr($arg, 1)] = true;
        } else {
            $options['command'] = $arg;
        }
    }
    
    return $options;
}

/**
 * 顯示使用說明
 */
function showUsage(): void
{
    echo "控制器測試運行器\n";
    echo "使用方法: php run_controller_tests.php [command] [options]\n\n";
    echo "命令:\n";
    echo "  all          - 運行所有控制器測試 (預設)\n";
    echo "  unit         - 運行單元測試\n";
    echo "  integration  - 運行整合測試\n";
    echo "  api          - 運行 API 測試\n";
    echo "  performance  - 運行效能測試\n";
    echo "  specific     - 運行特定測試類別 (需要 --class 參數)\n";
    echo "  report       - 生成測試報告\n\n";
    echo "選項:\n";
    echo "  --verbose    - 顯示詳細輸出\n";
    echo "  --coverage   - 生成程式碼覆蓋率報告\n";
    echo "  --class=     - 指定要運行的測試類別\n";
    echo "  --output=    - 指定報告輸出路徑\n\n";
    echo "範例:\n";
    echo "  php run_controller_tests.php unit --verbose\n";
    echo "  php run_controller_tests.php specific --class=Tests\\Unit\\Controller\\HomeControllerTest\n";
    echo "  php run_controller_tests.php report --output=/path/to/report.html\n";
}

// 如果直接執行此腳本
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    main();
}