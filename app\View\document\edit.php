<?php
/**
 * 文檔編輯頁面
 */

$document = $document ?? [];
$csrfToken = $csrfToken ?? '';
$error = $error ?? '';

include __DIR__ . '/../layout/header.php';
?>

<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- 返回按鈕 -->
    <div class="mb-6">
        <a href="/documents/<?= $document['id'] ?? '' ?>" class="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors">
            <i class="fas fa-arrow-left mr-2"></i>返回文檔詳情
        </a>
    </div>

    <!-- 頁面標題 -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">編輯文檔</h1>
        <p class="text-gray-600">修改文檔的基本資訊和內容</p>
    </div>

    <!-- 錯誤訊息 -->
    <?php if ($error): ?>
        <div class="bg-red-50 border border-red-200 rounded-md p-4 mb-8">
            <div class="flex">
                <i class="fas fa-exclamation-circle text-red-400 mr-3 mt-1"></i>
                <div class="text-red-700"><?= htmlspecialchars($error) ?></div>
            </div>
        </div>
    <?php endif; ?>

    <div class="bg-white rounded-lg shadow-md p-8">
        <form action="/documents/<?= $document['id'] ?? '' ?>/edit" method="POST" id="editForm">
            <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($csrfToken) ?>">

            <!-- 文檔標題 -->
            <div class="mb-6">
                <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                    文檔標題 <span class="text-red-500">*</span>
                </label>
                <input type="text" 
                       name="title" 
                       id="title" 
                       value="<?= htmlspecialchars($document['title'] ?? $document['filename'] ?? '') ?>"
                       required
                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                       placeholder="請輸入文檔標題">
            </div>

            <!-- 文檔資訊 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">檔案名稱</label>
                    <input type="text" 
                           value="<?= htmlspecialchars($document['filename'] ?? '') ?>"
                           readonly
                           class="w-full border border-gray-300 rounded-md px-3 py-2 bg-gray-50 text-gray-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">檔案類型</label>
                    <input type="text" 
                           value="<?= strtoupper($document['file_type'] ?? 'unknown') ?>"
                           readonly
                           class="w-full border border-gray-300 rounded-md px-3 py-2 bg-gray-50 text-gray-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">檔案大小</label>
                    <input type="text" 
                           value="<?= formatBytes($document['file_size'] ?? 0) ?>"
                           readonly
                           class="w-full border border-gray-300 rounded-md px-3 py-2 bg-gray-50 text-gray-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">上傳時間</label>
                    <input type="text" 
                           value="<?= date('Y-m-d H:i:s', strtotime($document['created_at'] ?? 'now')) ?>"
                           readonly
                           class="w-full border border-gray-300 rounded-md px-3 py-2 bg-gray-50 text-gray-500">
                </div>
            </div>

            <!-- 文檔內容預覽 -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">文檔內容預覽</label>
                <div class="border border-gray-300 rounded-md p-4 bg-gray-50 max-h-96 overflow-y-auto">
                    <?php if (!empty($document['content'])): ?>
                        <pre class="whitespace-pre-wrap text-sm text-gray-800"><?= htmlspecialchars(substr($document['content'], 0, 2000)) ?><?= strlen($document['content']) > 2000 ? '...' : '' ?></pre>
                    <?php else: ?>
                        <p class="text-gray-500 text-sm">無法顯示文檔內容</p>
                    <?php endif; ?>
                </div>
                <p class="text-xs text-gray-500 mt-2">
                    <i class="fas fa-info-circle mr-1"></i>
                    目前僅支援編輯文檔標題，內容編輯功能將在未來版本中提供
                </p>
            </div>

            <!-- 標籤 -->
            <div class="mb-6">
                <label for="tags" class="block text-sm font-medium text-gray-700 mb-2">
                    標籤 <span class="text-gray-400">(選填)</span>
                </label>
                <input type="text" 
                       name="tags" 
                       id="tags" 
                       value="<?= htmlspecialchars($document['tags'] ?? '') ?>"
                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                       placeholder="請輸入標籤，多個標籤請用逗號分隔">
                <p class="text-xs text-gray-500 mt-1">例如：PHP, 程式設計, 教學</p>
            </div>

            <!-- 描述 -->
            <div class="mb-6">
                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                    描述 <span class="text-gray-400">(選填)</span>
                </label>
                <textarea name="description" 
                          id="description" 
                          rows="4"
                          class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="請輸入文檔描述..."><?= htmlspecialchars($document['description'] ?? '') ?></textarea>
            </div>

            <!-- 狀態 -->
            <div class="mb-8">
                <label for="status" class="block text-sm font-medium text-gray-700 mb-2">狀態</label>
                <select name="status" 
                        id="status"
                        class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="active" <?= ($document['status'] ?? '') === 'active' ? 'selected' : '' ?>>啟用</option>
                    <option value="inactive" <?= ($document['status'] ?? '') === 'inactive' ? 'selected' : '' ?>>停用</option>
                </select>
                <p class="text-xs text-gray-500 mt-1">停用的文檔將不會出現在搜尋結果中</p>
            </div>

            <!-- 操作按鈕 -->
            <div class="flex justify-between items-center">
                <div class="flex space-x-4">
                    <a href="/documents/<?= $document['id'] ?? '' ?>" 
                       class="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors">
                        取消
                    </a>
                    
                    <button type="button" 
                            onclick="previewChanges()"
                            class="px-6 py-2 border border-blue-300 text-blue-700 rounded-md hover:bg-blue-50 transition-colors">
                        <i class="fas fa-eye mr-2"></i>預覽變更
                    </button>
                </div>

                <div class="flex space-x-4">
                    <button type="button" 
                            onclick="resetForm()"
                            class="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors">
                        <i class="fas fa-undo mr-2"></i>重置
                    </button>
                    
                    <button type="submit" 
                            class="px-6 py-2 btn-primary text-white rounded-md hover:bg-blue-700 transition-colors">
                        <i class="fas fa-save mr-2"></i>儲存變更
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 預覽變更模態框 -->
<div id="previewModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900">預覽變更</h3>
            <button onclick="closePreviewModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div id="previewContent" class="space-y-4">
            <!-- 預覽內容將在這裡顯示 -->
        </div>
        
        <div class="flex justify-end mt-6 space-x-4">
            <button onclick="closePreviewModal()" 
                    class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors">
                關閉
            </button>
            <button onclick="submitForm()" 
                    class="px-4 py-2 btn-primary text-white rounded-md hover:bg-blue-700 transition-colors">
                確認儲存
            </button>
        </div>
    </div>
</div>

<script>
    // 原始表單數據
    const originalData = {
        title: <?= json_encode($document['title'] ?? $document['filename'] ?? '') ?>,
        tags: <?= json_encode($document['tags'] ?? '') ?>,
        description: <?= json_encode($document['description'] ?? '') ?>,
        status: <?= json_encode($document['status'] ?? 'active') ?>
    };

    // 預覽變更
    function previewChanges() {
        const currentData = {
            title: document.getElementById('title').value,
            tags: document.getElementById('tags').value,
            description: document.getElementById('description').value,
            status: document.getElementById('status').value
        };

        const changes = [];
        
        // 檢查變更
        if (currentData.title !== originalData.title) {
            changes.push({
                field: '標題',
                old: originalData.title,
                new: currentData.title
            });
        }
        
        if (currentData.tags !== originalData.tags) {
            changes.push({
                field: '標籤',
                old: originalData.tags || '(無)',
                new: currentData.tags || '(無)'
            });
        }
        
        if (currentData.description !== originalData.description) {
            changes.push({
                field: '描述',
                old: originalData.description || '(無)',
                new: currentData.description || '(無)'
            });
        }
        
        if (currentData.status !== originalData.status) {
            changes.push({
                field: '狀態',
                old: originalData.status,
                new: currentData.status
            });
        }

        // 顯示預覽
        const previewContent = document.getElementById('previewContent');
        
        if (changes.length === 0) {
            previewContent.innerHTML = '<p class="text-gray-500 text-center py-8">沒有任何變更</p>';
        } else {
            let html = '<div class="space-y-4">';
            changes.forEach(change => {
                html += `
                    <div class="border border-gray-200 rounded-md p-4">
                        <h4 class="font-medium text-gray-900 mb-2">${change.field}</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <p class="text-sm text-gray-500 mb-1">原始值：</p>
                                <p class="text-sm bg-red-50 p-2 rounded border">${change.old}</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500 mb-1">新值：</p>
                                <p class="text-sm bg-green-50 p-2 rounded border">${change.new}</p>
                            </div>
                        </div>
                    </div>
                `;
            });
            html += '</div>';
            previewContent.innerHTML = html;
        }

        document.getElementById('previewModal').classList.remove('hidden');
    }

    // 關閉預覽模態框
    function closePreviewModal() {
        document.getElementById('previewModal').classList.add('hidden');
    }

    // 重置表單
    function resetForm() {
        if (confirm('確定要重置所有變更嗎？')) {
            document.getElementById('title').value = originalData.title;
            document.getElementById('tags').value = originalData.tags;
            document.getElementById('description').value = originalData.description;
            document.getElementById('status').value = originalData.status;
        }
    }

    // 提交表單
    function submitForm() {
        document.getElementById('editForm').submit();
    }

    // 點擊模態框外部關閉
    document.getElementById('previewModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closePreviewModal();
        }
    });

    // 表單驗證
    document.getElementById('editForm').addEventListener('submit', function(e) {
        const title = document.getElementById('title').value.trim();
        
        if (!title) {
            e.preventDefault();
            showNotification('請輸入文檔標題', 'error');
            document.getElementById('title').focus();
            return false;
        }
        
        if (title.length > 255) {
            e.preventDefault();
            showNotification('文檔標題不能超過255個字元', 'error');
            document.getElementById('title').focus();
            return false;
        }
    });

    // 自動儲存草稿（可選功能）
    let autoSaveTimer;
    function autoSave() {
        clearTimeout(autoSaveTimer);
        autoSaveTimer = setTimeout(() => {
            const formData = new FormData(document.getElementById('editForm'));
            formData.append('auto_save', '1');
            
            // 這裡可以實現自動儲存功能
            console.log('Auto save triggered');
        }, 30000); // 30秒後自動儲存
    }

    // 監聽表單變更
    document.getElementById('editForm').addEventListener('input', autoSave);
</script>

<?php include __DIR__ . '/../layout/footer.php'; ?>