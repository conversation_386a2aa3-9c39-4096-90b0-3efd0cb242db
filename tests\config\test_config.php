<?php

/**
 * 測試環境配置文件
 * 定義測試環境的各種設定
 */

return [
    // 資料庫配置
    'database' => [
        'host' => $_ENV['TEST_DB_HOST'] ?? 'localhost',
        'port' => $_ENV['TEST_DB_PORT'] ?? 3306,
        'dbname' => $_ENV['TEST_DB_DATABASE'] ?? 'techvg_test',
        'username' => $_ENV['TEST_DB_USERNAME'] ?? 'test_user',
        'password' => $_ENV['TEST_DB_PASSWORD'] ?? 'test_password',
        'charset' => 'utf8mb4',
        'options' => [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    ],

    // API 配置
    'api' => [
        'base_url' => $_ENV['TEST_API_BASE_URL'] ?? 'http://localhost:8000',
        'version' => 'v1',
        'timeout' => 30,
        'test_api_key' => $_ENV['TEST_API_KEY'] ?? 'test_key_123456',
        'rate_limit' => [
            'requests_per_minute' => 1000,
            'burst_limit' => 100
        ]
    ],

    // 應用程式配置
    'app' => [
        'base_url' => $_ENV['TEST_APP_BASE_URL'] ?? 'http://localhost:8000',
        'debug' => true,
        'log_level' => 'debug',
        'timezone' => 'Asia/Taipei',
        'locale' => 'zh_TW'
    ],

    // 儲存配置
    'storage' => [
        'test_uploads' => __DIR__ . '/../uploads',
        'test_cache' => __DIR__ . '/../cache',
        'test_logs' => __DIR__ . '/../logs',
        'test_results' => __DIR__ . '/../results',
        'max_file_size' => 10 * 1024 * 1024, // 10MB
        'allowed_extensions' => ['txt', 'md', 'pdf', 'doc', 'docx']
    ],

    // 快取配置
    'cache' => [
        'default_ttl' => 3600, // 1小時
        'test_ttl' => 60,      // 測試環境使用較短的 TTL
        'driver' => 'file',
        'prefix' => 'test_'
    ],

    // 搜尋配置
    'search' => [
        'index_path' => __DIR__ . '/../data/search_index',
        'max_results' => 100,
        'min_query_length' => 2,
        'highlight_length' => 200
    ],

    // 測試資料配置
    'test_data' => [
        'documents' => [
            'sample_count' => 50,
            'categories' => ['ai', 'blockchain', 'mobile', 'web', 'security'],
            'file_types' => ['txt', 'md', 'pdf']
        ],
        'news' => [
            'sample_count' => 100,
            'categories' => ['ai', 'blockchain', 'mobile', 'web', 'security'],
            'sources' => ['TechCrunch', 'Wired', 'The Verge', 'Ars Technica']
        ],
        'users' => [
            'sample_count' => 20,
            'roles' => ['admin', 'editor', 'viewer']
        ]
    ],

    // 效能測試配置
    'performance' => [
        'max_response_time' => 1000,    // 毫秒
        'concurrent_users' => 10,
        'requests_per_user' => 20,
        'memory_limit' => '256M',
        'time_limit' => 300             // 5分鐘
    ],

    // 整合測試配置
    'integration' => [
        'external_apis' => [
            'enabled' => false,         // 是否測試外部 API
            'timeout' => 10,
            'retry_count' => 3
        ],
        'email' => [
            'enabled' => false,         // 是否測試郵件功能
            'test_recipient' => '<EMAIL>'
        ],
        'file_operations' => [
            'enabled' => true,
            'test_file_size' => 1024    // 1KB
        ]
    ],

    // 報告配置
    'reporting' => [
        'output_format' => 'html',
        'include_coverage' => true,
        'coverage_threshold' => 80,     // 程式碼覆蓋率閾值
        'screenshot_on_failure' => false,
        'log_sql_queries' => true
    ],

    // 安全測試配置
    'security' => [
        'test_xss' => true,
        'test_sql_injection' => true,
        'test_csrf' => true,
        'test_file_upload' => true,
        'malicious_payloads' => [
            'xss' => [
                '<script>alert("xss")</script>',
                'javascript:alert("xss")',
                '<img src=x onerror=alert("xss")>'
            ],
            'sql_injection' => [
                "' OR '1'='1",
                '; DROP TABLE users; --',
                "' UNION SELECT * FROM users --"
            ]
        ]
    ],

    // 模擬配置
    'mocks' => [
        'external_services' => true,    // 是否模擬外部服務
        'database' => false,           // 是否模擬資料庫
        'file_system' => false,        // 是否模擬檔案系統
        'email' => true,               // 是否模擬郵件服務
        'cache' => false               // 是否模擬快取
    ],

    // 清理配置
    'cleanup' => [
        'auto_cleanup' => true,        // 是否自動清理
        'cleanup_on_failure' => false, // 失敗時是否清理
        'preserve_logs' => true,       // 是否保留日誌
        'cleanup_timeout' => 30        // 清理超時時間（秒）
    ]
];