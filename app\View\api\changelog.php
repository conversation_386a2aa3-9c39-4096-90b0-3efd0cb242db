<?php
/**
 * API 變更日誌頁面
 */

$title = 'Tech.VG API 變更日誌';
$description = 'Tech.VG API 的版本變更歷史和更新記錄';
$changelog = $data['changelog'] ?? [];
$baseUrl = $data['base_url'] ?? '';
?>

<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($title) ?></title>
    <meta name="description" content="<?= htmlspecialchars($description) ?>">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --dark-color: #1e293b;
            --light-color: #f8fafc;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            color: var(--dark-color);
            background: var(--light-color);
        }

        .changelog-header {
            background: linear-gradient(135deg, var(--primary-color), #3b82f6);
            color: white;
            padding: 3rem 0;
        }

        .version-card {
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            overflow: hidden;
        }

        .version-header {
            background: var(--light-color);
            padding: 1.5rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .version-badge {
            background: var(--primary-color);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.875rem;
            font-weight: 600;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-stable { background: #dcfce7; color: #166534; }
        .status-beta { background: #fef3c7; color: #92400e; }
        .status-alpha { background: #fecaca; color: #991b1b; }
        .status-deprecated { background: #f3f4f6; color: #6b7280; }

        .change-section {
            padding: 1.5rem;
        }

        .change-type {
            margin-bottom: 1.5rem;
        }

        .change-type h5 {
            display: flex;
            align-items: center;
            margin-bottom: 0.75rem;
            font-weight: 600;
        }

        .change-type .icon {
            width: 1.25rem;
            height: 1.25rem;
            margin-right: 0.5rem;
        }

        .change-added .icon { color: var(--success-color); }
        .change-changed .icon { color: var(--primary-color); }
        .change-deprecated .icon { color: var(--warning-color); }
        .change-removed .icon { color: var(--danger-color); }
        .change-fixed .icon { color: var(--success-color); }
        .change-security .icon { color: var(--danger-color); }

        .change-list {
            list-style: none;
            padding: 0;
        }

        .change-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #f1f5f9;
            position: relative;
            padding-left: 1.5rem;
        }

        .change-list li:last-child {
            border-bottom: none;
        }

        .change-list li::before {
            content: '•';
            position: absolute;
            left: 0;
            color: var(--secondary-color);
        }

        .breaking-changes {
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
        }

        .breaking-changes h6 {
            color: var(--danger-color);
            margin-bottom: 0.5rem;
        }

        .migration-notes {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
        }

        .migration-notes h6 {
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .known-issues {
            background: #fffbeb;
            border: 1px solid #fed7aa;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
        }

        .known-issues h6 {
            color: var(--warning-color);
            margin-bottom: 0.5rem;
        }

        .timeline {
            position: relative;
            padding-left: 2rem;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 0.5rem;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e2e8f0;
        }

        .timeline-item {
            position: relative;
            margin-bottom: 2rem;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: -0.75rem;
            top: 0.5rem;
            width: 0.75rem;
            height: 0.75rem;
            background: var(--primary-color);
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 0 0 2px var(--primary-color);
        }

        .nav-pills .nav-link {
            border-radius: 0.5rem;
            margin-right: 0.5rem;
        }

        .nav-pills .nav-link.active {
            background: var(--primary-color);
        }
    </style>
</head>
<body>
    <!-- 標題區 -->
    <div class="changelog-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-history"></i> API 變更日誌</h1>
                    <p class="mb-0">追蹤 Tech.VG API 的所有版本變更和更新</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="<?= htmlspecialchars($baseUrl) ?>/api/docs?format=html" class="btn btn-light me-2">
                        <i class="fas fa-book"></i> API 文檔
                    </a>
                    <a href="<?= htmlspecialchars($baseUrl) ?>/api/test?format=html" class="btn btn-outline-light">
                        <i class="fas fa-flask"></i> 測試工具
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container mt-4">
        <!-- 版本導航 -->
        <div class="row mb-4">
            <div class="col-12">
                <ul class="nav nav-pills">
                    <li class="nav-item">
                        <a class="nav-link active" href="#all">所有版本</a>
                    </li>
                    <?php foreach ($changelog as $version => $versionData): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="#<?= $version ?>"><?= htmlspecialchars($versionData['version']) ?></a>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>
        </div>

        <!-- 時間軸 -->
        <div class="timeline">
            <?php foreach ($changelog as $version => $versionData): ?>
                <div class="timeline-item" id="<?= $version ?>">
                    <div class="version-card">
                        <!-- 版本標題 -->
                        <div class="version-header">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h3 class="mb-2">
                                        <span class="version-badge"><?= htmlspecialchars($versionData['version']) ?></span>
                                    </h3>
                                    <div class="d-flex align-items-center gap-2">
                                        <span class="status-badge status-<?= htmlspecialchars($versionData['status']) ?>">
                                            <?= htmlspecialchars($versionData['status']) ?>
                                        </span>
                                        <small class="text-muted">
                                            <i class="fas fa-calendar"></i>
                                            發布日期: <?= htmlspecialchars($versionData['release_date']) ?>
                                        </small>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <a href="<?= htmlspecialchars($baseUrl) ?>/api/<?= $version ?>/changelog" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-download"></i> JSON
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- 變更內容 -->
                        <div class="change-section">
                            <?php if (!empty($versionData['changes']['added'])): ?>
                                <div class="change-type change-added">
                                    <h5>
                                        <i class="fas fa-plus icon"></i>
                                        新增功能
                                    </h5>
                                    <ul class="change-list">
                                        <?php foreach ($versionData['changes']['added'] as $change): ?>
                                            <li><?= htmlspecialchars($change) ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($versionData['changes']['changed'])): ?>
                                <div class="change-type change-changed">
                                    <h5>
                                        <i class="fas fa-edit icon"></i>
                                        變更
                                    </h5>
                                    <ul class="change-list">
                                        <?php foreach ($versionData['changes']['changed'] as $change): ?>
                                            <li><?= htmlspecialchars($change) ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($versionData['changes']['deprecated'])): ?>
                                <div class="change-type change-deprecated">
                                    <h5>
                                        <i class="fas fa-exclamation-triangle icon"></i>
                                        已棄用
                                    </h5>
                                    <ul class="change-list">
                                        <?php foreach ($versionData['changes']['deprecated'] as $change): ?>
                                            <li><?= htmlspecialchars($change) ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($versionData['changes']['removed'])): ?>
                                <div class="change-type change-removed">
                                    <h5>
                                        <i class="fas fa-minus icon"></i>
                                        移除
                                    </h5>
                                    <ul class="change-list">
                                        <?php foreach ($versionData['changes']['removed'] as $change): ?>
                                            <li><?= htmlspecialchars($change) ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($versionData['changes']['fixed'])): ?>
                                <div class="change-type change-fixed">
                                    <h5>
                                        <i class="fas fa-bug icon"></i>
                                        修復
                                    </h5>
                                    <ul class="change-list">
                                        <?php foreach ($versionData['changes']['fixed'] as $change): ?>
                                            <li><?= htmlspecialchars($change) ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($versionData['changes']['security'])): ?>
                                <div class="change-type change-security">
                                    <h5>
                                        <i class="fas fa-shield-alt icon"></i>
                                        安全性
                                    </h5>
                                    <ul class="change-list">
                                        <?php foreach ($versionData['changes']['security'] as $change): ?>
                                            <li><?= htmlspecialchars($change) ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>

                            <!-- 重大變更 -->
                            <?php if (!empty($versionData['breaking_changes'])): ?>
                                <div class="breaking-changes">
                                    <h6><i class="fas fa-exclamation-circle"></i> 重大變更</h6>
                                    <ul class="change-list">
                                        <?php foreach ($versionData['breaking_changes'] as $change): ?>
                                            <li><?= htmlspecialchars($change) ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>

                            <!-- 遷移說明 -->
                            <?php if (!empty($versionData['migration_notes'])): ?>
                                <div class="migration-notes">
                                    <h6><i class="fas fa-route"></i> 遷移說明</h6>
                                    <ul class="change-list">
                                        <?php foreach ($versionData['migration_notes'] as $note): ?>
                                            <li><?= htmlspecialchars($note) ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>

                            <!-- 已知問題 -->
                            <?php if (!empty($versionData['known_issues'])): ?>
                                <div class="known-issues">
                                    <h6><i class="fas fa-info-circle"></i> 已知問題</h6>
                                    <ul class="change-list">
                                        <?php foreach ($versionData['known_issues'] as $issue): ?>
                                            <li><?= htmlspecialchars($issue) ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- 底部資訊 -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <h5>需要協助？</h5>
                        <p class="text-muted">如果您在升級過程中遇到問題，請查看我們的文檔或聯繫支援團隊。</p>
                        <div class="d-flex justify-content-center gap-2">
                            <a href="<?= htmlspecialchars($baseUrl) ?>/api/docs?format=html" class="btn btn-primary">
                                <i class="fas fa-book"></i> 查看文檔
                            </a>
                            <a href="<?= htmlspecialchars($baseUrl) ?>/contact" class="btn btn-outline-primary">
                                <i class="fas fa-envelope"></i> 聯繫支援
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 平滑滾動到版本
        document.querySelectorAll('.nav-link[href^="#"]').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth' });
                    
                    // 更新活動狀態
                    document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
                    this.classList.add('active');
                }
            });
        });

        // 監聽滾動事件來更新導航
        window.addEventListener('scroll', function() {
            const sections = document.querySelectorAll('.timeline-item');
            const navLinks = document.querySelectorAll('.nav-link[href^="#"]');
            
            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (window.pageYOffset >= sectionTop - 200) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });
    </script>
</body>
</html>