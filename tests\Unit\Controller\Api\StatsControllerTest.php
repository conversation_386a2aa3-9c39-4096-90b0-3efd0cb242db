<?php

namespace Tests\Unit\Controller\Api;

use Tests\TestCase;
use App\Controller\Api\StatsController;
use App\Service\DocumentService;
use App\Service\NewsService;
use App\Service\SearchService;

/**
 * 統計控制器單元測試
 */
class StatsControllerTest extends TestCase
{
    private StatsController $controller;
    private $mockDocumentService;
    private $mockNewsService;
    private $mockSearchService;

    protected function setUp(): void
    {
        parent::setUp();
        
        // 建立模擬服務
        $this->mockDocumentService = $this->createMock(DocumentService::class);
        $this->mockNewsService = $this->createMock(NewsService::class);
        $this->mockSearchService = $this->createMock(SearchService::class);
        
        // 建立控制器實例
        $this->controller = new StatsController();
        
        // 注入模擬服務
        $this->injectMockServices();
    }

    /**
     * 注入模擬服務到控制器
     */
    private function injectMockServices(): void
    {
        $reflection = new \ReflectionClass($this->controller);
        
        $documentProperty = $reflection->getProperty('documentService');
        $documentProperty->setAccessible(true);
        $documentProperty->setValue($this->controller, $this->mockDocumentService);
        
        $newsProperty = $reflection->getProperty('newsService');
        $newsProperty->setAccessible(true);
        $newsProperty->setValue($this->controller, $this->mockNewsService);
        
        $searchProperty = $reflection->getProperty('searchService');
        $searchProperty->setAccessible(true);
        $searchProperty->setValue($this->controller, $this->mockSearchService);
    }

    /**
     * 測試取得總體統計
     */
    public function testGetOverallStats(): void
    {
        // 設定模擬服務回應
        $this->mockDocumentService
            ->expects($this->once())
            ->method('getTotalCount')
            ->willReturn(150);

        $this->mockNewsService
            ->expects($this->once())
            ->method('getTotalCount')
            ->willReturn(89);

        $this->mockSearchService
            ->expects($this->once())
            ->method('getTotalSearches')
            ->willReturn(1250);

        ob_start();
        $this->controller->overview();
        $output = ob_get_clean();

        $response = json_decode($output, true);

        // 驗證回應結構
        $this->assertIsArray($response);
        $this->assertTrue($response['success']);
        $this->assertArrayHasKey('data', $response);
        
        $data = $response['data'];
        $this->assertEquals(150, $data['total_documents']);
        $this->assertEquals(89, $data['total_news']);
        $this->assertEquals(1250, $data['total_searches']);
    }

    /**
     * 測試取得文檔統計
     */
    public function testGetDocumentStats(): void
    {
        $mockStats = [
            'total_documents' => 150,
            'documents_by_type' => [
                'pdf' => 65,
                'txt' => 45,
                'md' => 40
            ],
            'documents_by_category' => [
                'ai' => 50,
                'blockchain' => 35,
                'mobile' => 30,
                'web' => 25,
                'security' => 10
            ],
            'most_downloaded' => [
                [
                    'id' => 1,
                    'title' => '機器學習基礎',
                    'downloads' => 245
                ],
                [
                    'id' => 2,
                    'title' => '深度學習指南',
                    'downloads' => 189
                ]
            ],
            'upload_trends' => [
                '2024-01' => 15,
                '2024-02' => 18,
                '2024-03' => 22
            ]
        ];

        $this->mockDocumentService
            ->expects($this->once())
            ->method('getStatistics')
            ->willReturn($mockStats);

        ob_start();
        $this->controller->documents();
        $output = ob_get_clean();

        $response = json_decode($output, true);

        // 驗證文檔統計
        $this->assertIsArray($response);
        $this->assertTrue($response['success']);
        $this->assertEquals($mockStats, $response['data']);
    }

    /**
     * 測試取得新聞統計
     */
    public function testGetNewsStats(): void
    {
        $mockStats = [
            'total_news' => 89,
            'news_by_category' => [
                'ai' => 25,
                'blockchain' => 20,
                'mobile' => 18,
                'web' => 15,
                'security' => 11
            ],
            'news_by_source' => [
                'TechCrunch' => 30,
                'Wired' => 25,
                'The Verge' => 20,
                'Ars Technica' => 14
            ],
            'most_viewed' => [
                [
                    'id' => 1,
                    'title' => 'AI 技術新突破',
                    'views' => 1250
                ],
                [
                    'id' => 2,
                    'title' => '區塊鏈應用擴展',
                    'views' => 980
                ]
            ],
            'publish_trends' => [
                '2024-01' => 28,
                '2024-02' => 31,
                '2024-03' => 30
            ]
        ];

        $this->mockNewsService
            ->expects($this->once())
            ->method('getStatistics')
            ->willReturn($mockStats);

        ob_start();
        $this->controller->news();
        $output = ob_get_clean();

        $response = json_decode($output, true);

        // 驗證新聞統計
        $this->assertIsArray($response);
        $this->assertTrue($response['success']);
        $this->assertEquals($mockStats, $response['data']);
    }

    /**
     * 測試取得搜尋統計
     */
    public function testGetSearchStats(): void
    {
        $mockStats = [
            'total_searches' => 1250,
            'popular_keywords' => [
                'machine learning' => 150,
                'blockchain' => 120,
                'artificial intelligence' => 100,
                'deep learning' => 85,
                'cryptocurrency' => 70
            ],
            'search_trends' => [
                '2024-01' => 380,
                '2024-02' => 420,
                '2024-03' => 450
            ],
            'search_by_category' => [
                'documents' => 750,
                'news' => 500
            ],
            'average_results_per_search' => 12.5,
            'zero_result_searches' => 45
        ];

        $this->mockSearchService
            ->expects($this->once())
            ->method('getStatistics')
            ->willReturn($mockStats);

        ob_start();
        $this->controller->search();
        $output = ob_get_clean();

        $response = json_decode($output, true);

        // 驗證搜尋統計
        $this->assertIsArray($response);
        $this->assertTrue($response['success']);
        $this->assertEquals($mockStats, $response['data']);
    }

    /**
     * 測試取得使用者統計
     */
    public function testGetUserStats(): void
    {
        $mockStats = [
            'total_visitors' => 5420,
            'unique_visitors' => 3280,
            'page_views' => 18750,
            'bounce_rate' => 35.2,
            'average_session_duration' => 245, // 秒
            'top_pages' => [
                '/' => 3500,
                '/documents' => 2800,
                '/news' => 2200,
                '/search' => 1800
            ],
            'traffic_sources' => [
                'direct' => 45.2,
                'search' => 32.8,
                'social' => 15.5,
                'referral' => 6.5
            ],
            'device_types' => [
                'desktop' => 60.5,
                'mobile' => 32.8,
                'tablet' => 6.7
            ]
        ];

        // 使用反射調用私有方法
        $reflection = new \ReflectionClass($this->controller);
        $method = $reflection->getMethod('getUserStatistics');
        $method->setAccessible(true);

        // 模擬方法回應
        $this->controller = $this->getMockBuilder(StatsController::class)
            ->onlyMethods(['getUserStatistics'])
            ->getMock();

        $this->controller
            ->expects($this->once())
            ->method('getUserStatistics')
            ->willReturn($mockStats);

        ob_start();
        $this->controller->users();
        $output = ob_get_clean();

        $response = json_decode($output, true);

        // 驗證使用者統計
        $this->assertIsArray($response);
        $this->assertTrue($response['success']);
        $this->assertEquals($mockStats, $response['data']);
    }

    /**
     * 測試取得系統效能統計
     */
    public function testGetPerformanceStats(): void
    {
        $mockStats = [
            'average_response_time' => 125.5, // 毫秒
            'api_response_times' => [
                'documents' => 95.2,
                'news' => 110.8,
                'search' => 180.3
            ],
            'database_performance' => [
                'average_query_time' => 45.2,
                'slow_queries' => 12,
                'total_queries' => 15420
            ],
            'cache_performance' => [
                'hit_rate' => 85.6,
                'miss_rate' => 14.4,
                'cache_size' => '256MB'
            ],
            'server_resources' => [
                'cpu_usage' => 35.2,
                'memory_usage' => 68.5,
                'disk_usage' => 42.1
            ]
        ];

        // 使用反射調用私有方法
        $reflection = new \ReflectionClass($this->controller);
        $method = $reflection->getMethod('getPerformanceStatistics');
        $method->setAccessible(true);

        // 模擬方法回應
        $this->controller = $this->getMockBuilder(StatsController::class)
            ->onlyMethods(['getPerformanceStatistics'])
            ->getMock();

        $this->controller
            ->expects($this->once())
            ->method('getPerformanceStatistics')
            ->willReturn($mockStats);

        ob_start();
        $this->controller->performance();
        $output = ob_get_clean();

        $response = json_decode($output, true);

        // 驗證效能統計
        $this->assertIsArray($response);
        $this->assertTrue($response['success']);
        $this->assertEquals($mockStats, $response['data']);
    }

    /**
     * 測試取得時間範圍統計
     */
    public function testGetStatsWithTimeRange(): void
    {
        $_GET['start_date'] = '2024-01-01';
        $_GET['end_date'] = '2024-01-31';

        $this->mockDocumentService
            ->expects($this->once())
            ->method('getStatisticsByDateRange')
            ->with('2024-01-01', '2024-01-31')
            ->willReturn([
                'total_documents' => 25,
                'uploads_by_day' => [
                    '2024-01-01' => 2,
                    '2024-01-02' => 3,
                    '2024-01-03' => 1
                ]
            ]);

        ob_start();
        $this->controller->documents();
        $output = ob_get_clean();

        $response = json_decode($output, true);

        // 驗證時間範圍統計
        $this->assertIsArray($response);
        $this->assertTrue($response['success']);
        $this->assertEquals(25, $response['data']['total_documents']);

        // 清理
        unset($_GET['start_date'], $_GET['end_date']);
    }

    /**
     * 測試統計資料匯出
     */
    public function testExportStats(): void
    {
        $_GET['format'] = 'json';
        $_GET['type'] = 'overview';

        $this->mockDocumentService
            ->expects($this->once())
            ->method('getTotalCount')
            ->willReturn(150);

        $this->mockNewsService
            ->expects($this->once())
            ->method('getTotalCount')
            ->willReturn(89);

        ob_start();
        $this->controller->export();
        $output = ob_get_clean();

        $response = json_decode($output, true);

        // 驗證匯出功能
        $this->assertIsArray($response);
        $this->assertTrue($response['success']);
        $this->assertArrayHasKey('export_data', $response['data']);

        // 清理
        unset($_GET['format'], $_GET['type']);
    }

    /**
     * 測試即時統計
     */
    public function testRealTimeStats(): void
    {
        $mockRealTimeStats = [
            'current_online_users' => 25,
            'requests_per_minute' => 45,
            'active_searches' => 8,
            'recent_uploads' => 3,
            'system_load' => 0.65
        ];

        // 使用反射調用私有方法
        $reflection = new \ReflectionClass($this->controller);
        $method = $reflection->getMethod('getRealTimeStatistics');
        $method->setAccessible(true);

        // 模擬方法回應
        $this->controller = $this->getMockBuilder(StatsController::class)
            ->onlyMethods(['getRealTimeStatistics'])
            ->getMock();

        $this->controller
            ->expects($this->once())
            ->method('getRealTimeStatistics')
            ->willReturn($mockRealTimeStats);

        ob_start();
        $this->controller->realtime();
        $output = ob_get_clean();

        $response = json_decode($output, true);

        // 驗證即時統計
        $this->assertIsArray($response);
        $this->assertTrue($response['success']);
        $this->assertEquals($mockRealTimeStats, $response['data']);
    }

    /**
     * 測試錯誤處理
     */
    public function testErrorHandling(): void
    {
        // 設定服務拋出異常
        $this->mockDocumentService
            ->expects($this->once())
            ->method('getTotalCount')
            ->willThrowException(new \Exception('Database connection failed'));

        ob_start();
        $this->controller->overview();
        $output = ob_get_clean();

        $response = json_decode($output, true);

        // 驗證錯誤處理
        $this->assertIsArray($response);
        $this->assertFalse($response['success']);
        $this->assertArrayHasKey('error', $response);
        $this->assertEquals('STATS_ERROR', $response['error']['code']);
    }

    /**
     * 測試 API 回應格式
     */
    public function testApiResponseFormat(): void
    {
        $this->mockDocumentService
            ->expects($this->once())
            ->method('getTotalCount')
            ->willReturn(100);

        $this->mockNewsService
            ->expects($this->once())
            ->method('getTotalCount')
            ->willReturn(50);

        $this->mockSearchService
            ->expects($this->once())
            ->method('getTotalSearches')
            ->willReturn(500);

        ob_start();
        $this->controller->overview();
        $output = ob_get_clean();

        // 驗證 JSON 格式
        $this->assertJson($output);

        $response = json_decode($output, true);

        // 驗證標準 API 回應結構
        $this->assertArrayHasKey('success', $response);
        $this->assertArrayHasKey('message', $response);
        $this->assertArrayHasKey('data', $response);
        $this->assertArrayHasKey('timestamp', $response);
    }

    protected function tearDown(): void
    {
        // 清理全域變數
        unset($_GET);
        
        parent::tearDown();
    }
}