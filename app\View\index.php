

<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tech.VG - 科技新知</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@300;400;500;700&display=swap');
        
        body {
            font-family: 'Noto Sans TC', sans-serif;
            background-color: #f8f9fa;
        }
        
        .article-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        
        .gradient-bg {
            background: linear-gradient(90deg, #3b82f6, #8b5cf6);
        }
        
        .nav-link {
            position: relative;
        }
        
        .nav-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -2px;
            left: 0;
            background-color: #3b82f6;
            transition: width 0.3s ease;
        }
        
        .nav-link:hover::after {
            width: 100%;
        }
        
        .featured-badge {
            background: linear-gradient(90deg, #f43f5e, #ec4899);
        }
    </style>
</head>
<body>
    <!-- 導航欄 -->
    <nav class="bg-white shadow-md sticky top-0 z-50">
        <div class="container mx-auto px-4 py-3">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-2">
                    <div class="gradient-bg text-white p-2 rounded-lg">
                        <i class="fas fa-microchip text-xl"></i>
                    </div>
                    <a href="#" class="text-2xl font-bold text-gray-800">Tech<span class="text-blue-500">.VG</span></a>
                </div>
                
                <div class="hidden md:flex space-x-8">
                    <a href="#" class="nav-link text-gray-700 hover:text-blue-500 font-medium">首頁</a>
                    <a href="#" class="nav-link text-gray-700 hover:text-blue-500 font-medium">最新消息</a>
                    <a href="#" class="nav-link text-gray-700 hover:text-blue-500 font-medium">科技評測</a>
                    <a href="#" class="nav-link text-gray-700 hover:text-blue-500 font-medium">數位生活</a>
                    <a href="#" class="nav-link text-gray-700 hover:text-blue-500 font-medium">AI 專區</a>
                </div>
                
                <div class="flex items-center space-x-4">
                    <div class="relative hidden md:block">
                        <input type="text" placeholder="搜尋科技新知..." class="bg-gray-100 rounded-full py-2 px-4 pl-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:bg-white w-64">
                        <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                    </div>
                    <button class="md:hidden text-gray-700">
                        <i class="fas fa-search text-xl"></i>
                    </button>
                    <button class="md:hidden text-gray-700">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 頭條新聞 -->
    <section class="container mx-auto px-4 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div class="lg:col-span-2 relative rounded-xl overflow-hidden h-96 group">
                <div class="absolute inset-0 bg-gradient-to-t from-black to-transparent opacity-70 z-10"></div>
                <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='800' height='500' viewBox='0 0 800 500'%3E%3Crect width='800' height='500' fill='%233b82f6'/%3E%3Cpath d='M0 250 L800 250 L400 450 Z' fill='%232563eb'/%3E%3Ccircle cx='650' cy='150' r='80' fill='%231d4ed8'/%3E%3Crect x='100' y='100' width='200' height='200' rx='20' fill='%231e40af'/%3E%3C/svg%3E" alt="頭條新聞" class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105">
                <div class="absolute bottom-0 left-0 right-0 p-6 z-20">
                    <span class="featured-badge text-white text-xs font-bold px-3 py-1 rounded-full">頭條新聞</span>
                    <h2 class="text-white text-2xl md:text-3xl font-bold mt-2 mb-2">Apple 發表全新 M4 晶片，效能提升 40%</h2>
                    <p class="text-gray-200 mb-3 line-clamp-2">蘋果公司今日發表全新 M4 晶片，採用 3 奈米製程，效能較上一代提升 40%，同時能耗降低 25%。</p>
                    <div class="flex items-center text-gray-300 text-sm">
                        <span>科技組 · 王小明</span>
                        <span class="mx-2">|</span>
                        <span>2 小時前</span>
                    </div>
                </div>
            </div>
            
            <div class="space-y-4">
                <div class="bg-white rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 h-[11.5rem]">
                    <div class="flex h-full">
                        <div class="w-1/3">
                            <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='300' height='200' viewBox='0 0 300 200'%3E%3Crect width='300' height='200' fill='%238b5cf6'/%3E%3Ccircle cx='150' cy='100' r='50' fill='%237c3aed'/%3E%3Cpath d='M50 150 L250 150 L150 50 Z' fill='%236d28d9'/%3E%3C/svg%3E" alt="新聞圖片" class="w-full h-full object-cover">
                        </div>
                        <div class="w-2/3 p-4">
                            <span class="bg-purple-100 text-purple-800 text-xs font-medium px-2.5 py-0.5 rounded">AI 發展</span>
                            <h3 class="text-lg font-bold mt-2 mb-1 line-clamp-2">Google 推出全新 AI 模型，理解能力超越人類</h3>
                            <p class="text-gray-600 text-sm line-clamp-2">Google DeepMind 最新研發的 AI 模型在多項測試中表現優異，理解能力首次超越人類水平。</p>
                            <div class="text-gray-500 text-xs mt-2">1 天前</div>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 h-[11.5rem]">
                    <div class="flex h-full">
                        <div class="w-1/3">
                            <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='300' height='200' viewBox='0 0 300 200'%3E%3Crect width='300' height='200' fill='%2310b981'/%3E%3Ccircle cx='200' cy='50' r='30' fill='%230d9488'/%3E%3Crect x='50' y='80' width='150' height='70' rx='10' fill='%2314b8a6'/%3E%3C/svg%3E" alt="新聞圖片" class="w-full h-full object-cover">
                        </div>
                        <div class="w-2/3 p-4">
                            <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">永續科技</span>
                            <h3 class="text-lg font-bold mt-2 mb-1 line-clamp-2">特斯拉發表新一代太陽能屋頂，效率提升 35%</h3>
                            <p class="text-gray-600 text-sm line-clamp-2">特斯拉推出第四代太陽能屋頂，採用新型光電材料，轉換效率提升 35%，安裝成本降低 20%。</p>
                            <div class="text-gray-500 text-xs mt-2">3 天前</div>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 h-[11.5rem]">
                    <div class="flex h-full">
                        <div class="w-1/3">
                            <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='300' height='200' viewBox='0 0 300 200'%3E%3Crect width='300' height='200' fill='%23f43f5e'/%3E%3Ccircle cx='100' cy='100' r='50' fill='%23e11d48'/%3E%3Crect x='170' y='70' width='80' height='80' rx='10' fill='%23be123c'/%3E%3C/svg%3E" alt="新聞圖片" class="w-full h-full object-cover">
                        </div>
                        <div class="w-2/3 p-4">
                            <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded">資安警報</span>
                            <h3 class="text-lg font-bold mt-2 mb-1 line-clamp-2">全球最大規模資安漏洞曝光，影響超過 10 億裝置</h3>
                            <p class="text-gray-600 text-sm line-clamp-2">研究人員發現一個影響全球超過 10 億台 Android 裝置的嚴重安全漏洞，Google 緊急發布修補程式。</p>
                            <div class="text-gray-500 text-xs mt-2">5 天前</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 分類標籤 -->
    <section class="container mx-auto px-4 py-6">
        <div class="flex flex-wrap gap-3 justify-center">
            <button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-full transition-colors">全部文章</button>
            <button class="bg-gray-100 hover:bg-gray-200 text-gray-800 px-4 py-2 rounded-full transition-colors">人工智慧</button>
            <button class="bg-gray-100 hover:bg-gray-200 text-gray-800 px-4 py-2 rounded-full transition-colors">手機平板</button>
            <button class="bg-gray-100 hover:bg-gray-200 text-gray-800 px-4 py-2 rounded-full transition-colors">電腦硬體</button>
            <button class="bg-gray-100 hover:bg-gray-200 text-gray-800 px-4 py-2 rounded-full transition-colors">軟體應用</button>
            <button class="bg-gray-100 hover:bg-gray-200 text-gray-800 px-4 py-2 rounded-full transition-colors">遊戲電競</button>
            <button class="bg-gray-100 hover:bg-gray-200 text-gray-800 px-4 py-2 rounded-full transition-colors">科技趨勢</button>
        </div>
    </section>

    <!-- 最新文章 -->
    <section class="container mx-auto px-4 py-8">
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-2xl font-bold text-gray-800">最新文章</h2>
            <a href="#" class="text-blue-500 hover:text-blue-700 flex items-center">
                查看更多 <i class="fas fa-chevron-right ml-1 text-sm"></i>
            </a>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- 文章卡片 1 -->
            <div class="article-card bg-white rounded-xl overflow-hidden shadow-md transition-all duration-300">
                <div class="relative h-48">
                    <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='400' height='200' viewBox='0 0 400 200'%3E%3Crect width='400' height='200' fill='%230ea5e9'/%3E%3Ccircle cx='300' cy='50' r='30' fill='%230284c7'/%3E%3Crect x='50' y='80' width='200' height='70' rx='10' fill='%230369a1'/%3E%3C/svg%3E" alt="文章圖片" class="w-full h-full object-cover">
                    <div class="absolute top-3 left-3">
                        <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">手機平板</span>
                    </div>
                </div>
                <div class="p-5">
                    <h3 class="text-xl font-bold mb-2">三星發表 Galaxy Z Fold 6，摺疊螢幕更耐用</h3>
                    <p class="text-gray-600 mb-4 line-clamp-3">三星最新一代摺疊手機 Galaxy Z Fold 6 正式發表，採用全新摺疊技術，螢幕耐用度提升 50%，同時搭載最新的 Snapdragon 8 Gen 3 處理器。</p>
                    <div class="flex justify-between items-center">
                        <div class="flex items-center">
                            <div class="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white font-bold">L</div>
                            <span class="ml-2 text-sm text-gray-600">李小明</span>
                        </div>
                        <span class="text-sm text-gray-500">2023/06/15</span>
                    </div>
                </div>
            </div>
            
            <!-- 文章卡片 2 -->
            <div class="article-card bg-white rounded-xl overflow-hidden shadow-md transition-all duration-300">
                <div class="relative h-48">
                    <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='400' height='200' viewBox='0 0 400 200'%3E%3Crect width='400' height='200' fill='%23a855f7'/%3E%3Ccircle cx='100' cy='50' r='30' fill='%239333ea'/%3E%3Cpath d='M150 100 L350 100 L250 180 Z' fill='%238b5cf6'/%3E%3C/svg%3E" alt="文章圖片" class="w-full h-full object-cover">
                    <div class="absolute top-3 left-3">
                        <span class="bg-purple-100 text-purple-800 text-xs font-medium px-2.5 py-0.5 rounded">人工智慧</span>
                    </div>
                </div>
                <div class="p-5">
                    <h3 class="text-xl font-bold mb-2">OpenAI 推出 GPT-5，理解能力大幅提升</h3>
                    <p class="text-gray-600 mb-4 line-clamp-3">OpenAI 正式發布 GPT-5 模型，相較於 GPT-4，新模型在理解長文本、解決複雜問題和創意生成方面都有顯著提升，同時大幅降低了幻覺產生的機率。</p>
                    <div class="flex justify-between items-center">
                        <div class="flex items-center">
                            <div class="w-8 h-8 rounded-full bg-purple-500 flex items-center justify-center text-white font-bold">C</div>
                            <span class="ml-2 text-sm text-gray-600">陳大明</span>
                        </div>
                        <span class="text-sm text-gray-500">2023/06/12</span>
                    </div>
                </div>
            </div>
            
            <!-- 文章卡片 3 -->
            <div class="article-card bg-white rounded-xl overflow-hidden shadow-md transition-all duration-300">
                <div class="relative h-48">
                    <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='400' height='200' viewBox='0 0 400 200'%3E%3Crect width='400' height='200' fill='%23f97316'/%3E%3Ccircle cx='200' cy='100' r='50' fill='%23ea580c'/%3E%3Crect x='50' y='50' width='100' height='100' rx='10' fill='%23c2410c'/%3E%3C/svg%3E" alt="文章圖片" class="w-full h-full object-cover">
                    <div class="absolute top-3 left-3">
                        <span class="bg-orange-100 text-orange-800 text-xs font-medium px-2.5 py-0.5 rounded">電腦硬體</span>
                    </div>
                </div>
                <div class="p-5">
                    <h3 class="text-xl font-bold mb-2">NVIDIA 發表 RTX 5090，效能提升 70%</h3>
                    <p class="text-gray-600 mb-4 line-clamp-3">NVIDIA 正式發表新一代旗艦顯示卡 RTX 5090，採用全新 Blackwell 架構，效能較上一代提升 70%，同時支援全新的 DLSS 4.0 技術，大幅提升遊戲畫質與效能。</p>
                    <div class="flex justify-between items-center">
                        <div class="flex items-center">
                            <div class="w-8 h-8 rounded-full bg-orange-500 flex items-center justify-center text-white font-bold">W</div>
                            <span class="ml-2 text-sm text-gray-600">吳小華</span>
                        </div>
                        <span class="text-sm text-gray-500">2023/06/10</span>
                    </div>
                </div>
            </div>
            
            <!-- 文章卡片 4 -->
            <div class="article-card bg-white rounded-xl overflow-hidden shadow-md transition-all duration-300">
                <div class="relative h-48">
                    <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='400' height='200' viewBox='0 0 400 200'%3E%3Crect width='400' height='200' fill='%2322c55e'/%3E%3Ccircle cx='300' cy='150' r='30' fill='%2316a34a'/%3E%3Cpath d='M50 50 L150 50 L100 150 Z' fill='%2315803d'/%3E%3C/svg%3E" alt="文章圖片" class="w-full h-full object-cover">
                    <div class="absolute top-3 left-3">
                        <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">軟體應用</span>
                    </div>
                </div>
                <div class="p-5">
                    <h3 class="text-xl font-bold mb-2">微軟推出 Windows 12，全面整合 AI 功能</h3>
                    <p class="text-gray-600 mb-4 line-clamp-3">微軟正式發布 Windows 12 作業系統，全面整合 AI 助手 Copilot，提供更智能的使用體驗，同時優化系統效能，降低資源占用，提升電池續航力。</p>
                    <div class="flex justify-between items-center">
                        <div class="flex items-center">
                            <div class="w-8 h-8 rounded-full bg-green-500 flex items-center justify-center text-white font-bold">Z</div>
                            <span class="ml-2 text-sm text-gray-600">張小芳</span>
                        </div>
                        <span class="text-sm text-gray-500">2023/06/08</span>
                    </div>
                </div>
            </div>
            
            <!-- 文章卡片 5 -->
            <div class="article-card bg-white rounded-xl overflow-hidden shadow-md transition-all duration-300">
                <div class="relative h-48">
                    <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='400' height='200' viewBox='0 0 400 200'%3E%3Crect width='400' height='200' fill='%23ec4899'/%3E%3Ccircle cx='100' cy='100' r='50' fill='%23db2777'/%3E%3Crect x='200' y='50' width='150' height='100' rx='10' fill='%23be185d'/%3E%3C/svg%3E" alt="文章圖片" class="w-full h-full object-cover">
                    <div class="absolute top-3 left-3">
                        <span class="bg-pink-100 text-pink-800 text-xs font-medium px-2.5 py-0.5 rounded">遊戲電競</span>
                    </div>
                </div>
                <div class="p-5">
                    <h3 class="text-xl font-bold mb-2">索尼發表 PlayStation 6，支援 8K 遊戲</h3>
                    <p class="text-gray-600 mb-4 line-clamp-3">索尼正式發表 PlayStation 6 遊戲主機，支援 8K 遊戲輸出，採用全新的 AMD 處理器與顯示晶片，同時推出全新的 VR2 Pro 頭戴裝置，提供更沉浸的遊戲體驗。</p>
                    <div class="flex justify-between items-center">
                        <div class="flex items-center">
                            <div class="w-8 h-8 rounded-full bg-pink-500 flex items-center justify-center text-white font-bold">H</div>
                            <span class="ml-2 text-sm text-gray-600">黃小龍</span>
                        </div>
                        <span class="text-sm text-gray-500">2023/06/05</span>
                    </div>
                </div>
            </div>
            
            <!-- 文章卡片 6 -->
            <div class="article-card bg-white rounded-xl overflow-hidden shadow-md transition-all duration-300">
                <div class="relative h-48">
                    <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='400' height='200' viewBox='0 0 400 200'%3E%3Crect width='400' height='200' fill='%23eab308'/%3E%3Ccircle cx='200' cy='50' r='30' fill='%23ca8a04'/%3E%3Cpath d='M100 100 L300 100 L200 180 Z' fill='%23a16207'/%3E%3C/svg%3E" alt="文章圖片" class="w-full h-full object-cover">
                    <div class="absolute top-3 left-3">
                        <span class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded">科技趨勢</span>
                    </div>
                </div>
                <div class="p-5">
                    <h3 class="text-xl font-bold mb-2">量子電腦突破：IBM 實現 1000 量子位元</h3>
                    <p class="text-gray-600 mb-4 line-clamp-3">IBM 宣布成功開發出擁有 1000 量子位元的量子電腦，這是量子運算領域的重大突破，有望加速解決傳統電腦難以處理的複雜問題，如藥物研發、氣候模擬等領域。</p>
                    <div class="flex justify-between items-center">
                        <div class="flex items-center">
                            <div class="w-8 h-8 rounded-full bg-yellow-500 flex items-center justify-center text-white font-bold">L</div>
                            <span class="ml-2 text-sm text-gray-600">林小雨</span>
                        </div>
                        <span class="text-sm text-gray-500">2023/06/01</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 訂閱區塊 -->
    <section class="container mx-auto px-4 py-12">
        <div class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 md:p-12 text-center text-white">
            <h2 class="text-3xl font-bold mb-4">訂閱 Tech.VG 電子報</h2>
            <p class="text-lg mb-6 max-w-2xl mx-auto">獲取最新科技新知、獨家內容和專家分析，每週精選內容直送您的信箱。</p>
            <div class="flex flex-col md:flex-row gap-4 max-w-md mx-auto">
                <input type="email" placeholder="請輸入您的電子郵件" class="flex-grow px-4 py-3 rounded-lg text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-300">
                <button class="bg-white text-blue-600 hover:bg-blue-50 font-bold px-6 py-3 rounded-lg transition-colors">立即訂閱</button>
            </div>
            <p class="text-sm mt-4 text-blue-100">我們尊重您的隱私，您可以隨時取消訂閱。</p>
        </div>
    </section>

    <!-- 熱門主題 -->
    <section class="container mx-auto px-4 py-8">
        <h2 class="text-2xl font-bold text-gray-800 mb-6">熱門主題</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div class="bg-gradient-to-br from-blue-500 to-blue-700 rounded-xl p-6 text-white hover:shadow-lg transition-shadow">
                <div class="mb-4">
                    <i class="fas fa-robot text-4xl"></i>
                </div>
                <h3 class="text-xl font-bold mb-2">人工智慧</h3>
                <p class="mb-4">探索 AI 最新發展，從機器學習到深度學習，了解 AI 如何改變我們的生活與工作。</p>
                <a href="#" class="inline-flex items-center text-blue-100 hover:text-white">
                    查看更多 <i class="fas fa-arrow-right ml-1"></i>
                </a>
            </div>
            
            <div class="bg-gradient-to-br from-purple-500 to-purple-700 rounded-xl p-6 text-white hover:shadow-lg transition-shadow">
                <div class="mb-4">
                    <i class="fas fa-vr-cardboard text-4xl"></i>
                </div>
                <h3 class="text-xl font-bold mb-2">元宇宙</h3>
                <p class="mb-4">深入了解虛擬實境、擴增實境和混合實境技術，以及它們如何塑造未來的數位體驗。</p>
                <a href="#" class="inline-flex items-center text-purple-100 hover:text-white">
                    查看更多 <i class="fas fa-arrow-right ml-1"></i>
                </a>
            </div>
            
            <div class="bg-gradient-to-br from-green-500 to-green-700 rounded-xl p-6 text-white hover:shadow-lg transition-shadow">
                <div class="mb-4">
                    <i class="fas fa-leaf text-4xl"></i>
                </div>
                <h3 class="text-xl font-bold mb-2">永續科技</h3>
                <p class="mb-4">探索如何利用科技解決環境問題，從再生能源到碳捕捉技術，科技如何幫助地球更永續。</p>
                <a href="#" class="inline-flex items-center text-green-100 hover:text-white">
                    查看更多 <i class="fas fa-arrow-right ml-1"></i>
                </a>
            </div>
            
            <div class="bg-gradient-to-br from-red-500 to-red-700 rounded-xl p-6 text-white hover:shadow-lg transition-shadow">
                <div class="mb-4">
                    <i class="fas fa-shield-alt text-4xl"></i>
                </div>
                <h3 class="text-xl font-bold mb-2">資訊安全</h3>
                <p class="mb-4">了解最新的網路安全威脅與防護技術，保護您的數位資產和個人隱私不受侵害。</p>
                <a href="#" class="inline-flex items-center text-red-100 hover:text-white">
                    查看更多 <i class="fas fa-arrow-right ml-1"></i>
                </a>
            </div>
        </div>
    </section>

    <!-- 頁尾 -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center space-x-2 mb-4">
                        <div class="gradient-bg p-2 rounded-lg">
                            <i class="fas fa-microchip text-xl"></i>
                        </div>
                        <span class="text-2xl font-bold">Tech<span class="text-blue-400">.VG</span></span>
                    </div>
                    <p class="text-gray-400 mb-4">提供最新、最深入的科技新知與分析，讓您掌握科技脈動。</p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fab fa-facebook-f text-lg"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fab fa-twitter text-lg"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fab fa-instagram text-lg"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fab fa-youtube text-lg"></i>
                        </a>
                    </div>
                </div>
                
                <div>
                    <h3 class="text-lg font-bold mb-4">內容分類</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">最新消息</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">科技評測</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">數位生活</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">AI 專區</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">科技趨勢</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-bold mb-4">關於我們</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">關於 Tech.VG</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">編輯團隊</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">聯絡我們</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">廣告合作</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">徵才資訊</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-bold mb-4">訂閱電子報</h3>
                    <p class="text-gray-400 mb-4">每週獲取精選科技新知，掌握最新趨勢。</p>
                    <div class="flex">
                        <input type="email" placeholder="您的電子郵件" class="bg-gray-800 text-white px-4 py-2 rounded-l-lg focus:outline-none focus:ring-1 focus:ring-blue-500 w-full">
                        <button class="bg-blue-500 hover:bg-blue-600 px-4 py-2 rounded-r-lg transition-colors">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
                <p class="text-gray-400 text-sm mb-4 md:mb-0">© 2023 Tech.VG 科技新知. 版權所有.</p>
                <div class="flex space-x-4 text-sm">
                    <a href="#" class="text-gray-400 hover:text-white transition-colors">隱私政策</a>
                    <a href="#" class="text-gray-400 hover:text-white transition-colors">使用條款</a>
                    <a href="#" class="text-gray-400 hover:text-white transition-colors">Cookie 政策</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- 返回頂部按鈕 -->
    <button id="backToTop" class="fixed bottom-6 right-6 bg-blue-500 hover:bg-blue-600 text-white rounded-full p-3 shadow-lg transition-all opacity-0 invisible">
        <i class="fas fa-arrow-up"></i>
    </button>

    <script>
        // 返回頂部按鈕
        const backToTopButton = document.getElementById('backToTop');
        
        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                backToTopButton.classList.remove('opacity-0', 'invisible');
                backToTopButton.classList.add('opacity-100', 'visible');
            } else {
                backToTopButton.classList.remove('opacity-100', 'visible');
                backToTopButton.classList.add('opacity-0', 'invisible');
            }
        });
        
        backToTopButton.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
        
        // 分類標籤切換
        const categoryButtons = document.querySelectorAll('section:nth-of-type(2) button');
        
        categoryButtons.forEach(button => {
            button.addEventListener('click', () => {
                categoryButtons.forEach(btn => {
                    btn.classList.remove('bg-blue-500', 'hover:bg-blue-600', 'text-white');
                    btn.classList.add('bg-gray-100', 'hover:bg-gray-200', 'text-gray-800');
                });
                
                button.classList.remove('bg-gray-100', 'hover:bg-gray-200', 'text-gray-800');
                button.classList.add('bg-blue-500', 'hover:bg-blue-600', 'text-white');
            });
        });
    </script>
</body>
</html>
