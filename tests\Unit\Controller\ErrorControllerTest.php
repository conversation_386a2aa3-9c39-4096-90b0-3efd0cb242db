<?php

namespace Tests\Unit\Controller;

use Tests\TestCase;
use App\Controller\ErrorController;

/**
 * 錯誤控制器單元測試
 */
class ErrorControllerTest extends TestCase
{
    private ErrorController $controller;

    protected function setUp(): void
    {
        parent::setUp();
        $this->controller = new ErrorController();
    }

    /**
     * 測試 404 錯誤頁面
     */
    public function testShow404(): void
    {
        ob_start();
        $this->controller->show404();
        $output = ob_get_clean();

        // 驗證 404 頁面內容
        $this->assertStringContainsString('404', $output);
        $this->assertStringContainsString('找不到頁面', $output);
        $this->assertNotEmpty($output);
    }

    /**
     * 測試 403 錯誤頁面
     */
    public function testShow403(): void
    {
        ob_start();
        $this->controller->show403();
        $output = ob_get_clean();

        // 驗證 403 頁面內容
        $this->assertStringContainsString('403', $output);
        $this->assertStringContainsString('禁止存取', $output);
        $this->assertNotEmpty($output);
    }

    /**
     * 測試 500 錯誤頁面
     */
    public function testShow500(): void
    {
        ob_start();
        $this->controller->show500();
        $output = ob_get_clean();

        // 驗證 500 頁面內容
        $this->assertStringContainsString('500', $output);
        $this->assertStringContainsString('伺服器錯誤', $output);
        $this->assertNotEmpty($output);
    }

    /**
     * 測試 503 錯誤頁面
     */
    public function testShow503(): void
    {
        ob_start();
        $this->controller->show503();
        $output = ob_get_clean();

        // 驗證 503 頁面內容
        $this->assertStringContainsString('503', $output);
        $this->assertStringContainsString('服務暫時無法使用', $output);
        $this->assertNotEmpty($output);
    }

    /**
     * 測試通用錯誤處理方法
     */
    public function testHandleError(): void
    {
        $errorCode = 418;
        $errorMessage = "I'm a teapot";

        ob_start();
        $this->controller->handleError($errorCode, $errorMessage);
        $output = ob_get_clean();

        // 驗證通用錯誤處理
        $this->assertStringContainsString('418', $output);
        $this->assertStringContainsString("I'm a teapot", $output);
        $this->assertNotEmpty($output);
    }

    /**
     * 測試錯誤日誌記錄
     */
    public function testErrorLogging(): void
    {
        $errorMessage = 'Test error message';
        $errorCode = 500;
        $errorFile = '/test/file.php';
        $errorLine = 123;

        // 使用反射調用私有方法
        $reflection = new \ReflectionClass($this->controller);
        $method = $reflection->getMethod('logError');
        $method->setAccessible(true);

        // 執行錯誤日誌記錄
        $result = $method->invoke(
            $this->controller,
            $errorMessage,
            $errorCode,
            $errorFile,
            $errorLine
        );

        // 驗證日誌記錄成功
        $this->assertTrue($result);
    }

    /**
     * 測試開發模式下的錯誤顯示
     */
    public function testDevelopmentModeError(): void
    {
        // 設定開發模式
        $_ENV['APP_DEBUG'] = 'true';

        $exception = new \Exception('Test exception', 500);

        ob_start();
        $this->controller->handleException($exception);
        $output = ob_get_clean();

        // 在開發模式下應該顯示詳細錯誤資訊
        $this->assertStringContainsString('Test exception', $output);
        $this->assertStringContainsString('Exception', $output);

        // 清理
        unset($_ENV['APP_DEBUG']);
    }

    /**
     * 測試生產模式下的錯誤顯示
     */
    public function testProductionModeError(): void
    {
        // 設定生產模式
        $_ENV['APP_DEBUG'] = 'false';

        $exception = new \Exception('Sensitive error information', 500);

        ob_start();
        $this->controller->handleException($exception);
        $output = ob_get_clean();

        // 在生產模式下不應該顯示敏感錯誤資訊
        $this->assertStringNotContainsString('Sensitive error information', $output);
        $this->assertStringContainsString('伺服器錯誤', $output);

        // 清理
        unset($_ENV['APP_DEBUG']);
    }

    /**
     * 測試 AJAX 請求的錯誤回應
     */
    public function testAjaxErrorResponse(): void
    {
        // 模擬 AJAX 請求
        $_SERVER['HTTP_X_REQUESTED_WITH'] = 'XMLHttpRequest';

        ob_start();
        $this->controller->show404();
        $output = ob_get_clean();

        // 驗證 JSON 回應
        $response = json_decode($output, true);
        $this->assertIsArray($response);
        $this->assertEquals(404, $response['error']['code']);
        $this->assertStringContainsString('找不到', $response['error']['message']);

        // 清理
        unset($_SERVER['HTTP_X_REQUESTED_WITH']);
    }

    /**
     * 測試錯誤統計
     */
    public function testErrorStatistics(): void
    {
        // 記錄多個錯誤
        $this->controller->show404();
        $this->controller->show404();
        $this->controller->show500();

        // 使用反射取得錯誤統計
        $reflection = new \ReflectionClass($this->controller);
        $method = $reflection->getMethod('getErrorStatistics');
        $method->setAccessible(true);

        $stats = $method->invoke($this->controller);

        // 驗證統計資料
        $this->assertIsArray($stats);
        $this->assertArrayHasKey('404', $stats);
        $this->assertArrayHasKey('500', $stats);
    }

    /**
     * 測試自訂錯誤頁面
     */
    public function testCustomErrorPage(): void
    {
        $customMessage = '自訂錯誤訊息';
        $customCode = 422;

        ob_start();
        $this->controller->showCustomError($customCode, $customMessage);
        $output = ob_get_clean();

        // 驗證自訂錯誤頁面
        $this->assertStringContainsString($customMessage, $output);
        $this->assertStringContainsString('422', $output);
        $this->assertNotEmpty($output);
    }

    /**
     * 測試錯誤頁面的 HTTP 狀態碼設定
     */
    public function testHttpStatusCodeSetting(): void
    {
        // 由於在測試環境中無法直接檢查 HTTP 狀態碼
        // 我們測試狀態碼設定方法是否正確調用
        $reflection = new \ReflectionClass($this->controller);
        $method = $reflection->getMethod('setHttpStatusCode');
        $method->setAccessible(true);

        // 測試不同的狀態碼
        $result404 = $method->invoke($this->controller, 404);
        $result500 = $method->invoke($this->controller, 500);

        // 驗證方法執行成功
        $this->assertTrue($result404);
        $this->assertTrue($result500);
    }

    protected function tearDown(): void
    {
        // 清理全域變數
        unset($_SERVER['HTTP_X_REQUESTED_WITH']);
        unset($_ENV['APP_DEBUG']);
        
        parent::tearDown();
    }
}