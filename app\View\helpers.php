<?php
/**
 * 視圖輔助函數
 * 提供視圖中常用的功能函數
 */

/**
 * 高亮搜尋關鍵字
 * 
 * @param string $text 原始文字
 * @param string $query 搜尋關鍵字
 * @return string 高亮後的文字
 */
function highlightSearchTerms($text, $query) {
    if (empty($query) || empty($text)) {
        return htmlspecialchars($text);
    }
    
    $text = htmlspecialchars($text);
    $keywords = explode(' ', $query);
    
    foreach ($keywords as $keyword) {
        $keyword = trim($keyword);
        if (!empty($keyword)) {
            $text = preg_replace(
                '/(' . preg_quote($keyword, '/') . ')/iu',
                '<mark class="bg-yellow-200 px-1 rounded">$1</mark>',
                $text
            );
        }
    }
    
    return $text;
}

/**
 * 格式化檔案大小
 * 
 * @param int $bytes 位元組數
 * @return string 格式化後的大小
 */
function formatBytes($bytes) {
    if ($bytes === 0) return '0 Bytes';
    
    $k = 1024;
    $sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    $i = floor(log($bytes) / log($k));
    
    return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
}

/**
 * 截斷文字
 * 
 * @param string $text 原始文字
 * @param int $length 最大長度
 * @param string $suffix 後綴
 * @return string 截斷後的文字
 */
function truncateText($text, $length = 100, $suffix = '...') {
    if (mb_strlen($text) <= $length) {
        return $text;
    }
    
    return mb_substr($text, 0, $length) . $suffix;
}

/**
 * 格式化時間差
 * 
 * @param string $datetime 日期時間
 * @return string 格式化後的時間差
 */
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) {
        return '剛剛';
    } elseif ($time < 3600) {
        return floor($time / 60) . ' 分鐘前';
    } elseif ($time < 86400) {
        return floor($time / 3600) . ' 小時前';
    } elseif ($time < 2592000) {
        return floor($time / 86400) . ' 天前';
    } else {
        return date('Y-m-d', strtotime($datetime));
    }
}

/**
 * 生成分頁連結
 * 
 * @param array $pagination 分頁資訊
 * @param array $params 額外參數
 * @return string 分頁HTML
 */
function generatePagination($pagination, $params = []) {
    if (empty($pagination) || $pagination['total_pages'] <= 1) {
        return '';
    }
    
    $currentPage = $pagination['current_page'];
    $totalPages = $pagination['total_pages'];
    $queryString = http_build_query($params);
    
    $html = '<nav class="flex items-center justify-center space-x-2">';
    
    // 上一頁
    if ($currentPage > 1) {
        $prevParams = array_merge($params, ['page' => $currentPage - 1]);
        $prevQuery = http_build_query($prevParams);
        $html .= '<a href="?' . $prevQuery . '" class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">上一頁</a>';
    }
    
    // 頁碼
    $start = max(1, $currentPage - 2);
    $end = min($totalPages, $currentPage + 2);
    
    for ($i = $start; $i <= $end; $i++) {
        $pageParams = array_merge($params, ['page' => $i]);
        $pageQuery = http_build_query($pageParams);
        
        if ($i == $currentPage) {
            $html .= '<span class="px-3 py-2 text-sm font-medium text-white bg-blue-600 border border-blue-600 rounded-md">' . $i . '</span>';
        } else {
            $html .= '<a href="?' . $pageQuery . '" class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">' . $i . '</a>';
        }
    }
    
    // 下一頁
    if ($currentPage < $totalPages) {
        $nextParams = array_merge($params, ['page' => $currentPage + 1]);
        $nextQuery = http_build_query($nextParams);
        $html .= '<a href="?' . $nextQuery . '" class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">下一頁</a>';
    }
    
    $html .= '</nav>';
    
    return $html;
}

/**
 * 生成麵包屑導航
 * 
 * @param array $breadcrumbs 麵包屑數據
 * @return string 麵包屑HTML
 */
function generateBreadcrumbs($breadcrumbs) {
    if (empty($breadcrumbs)) {
        return '';
    }
    
    $html = '<nav class="flex" aria-label="Breadcrumb">';
    $html .= '<ol class="inline-flex items-center space-x-1 md:space-x-3">';
    
    foreach ($breadcrumbs as $index => $crumb) {
        $isLast = ($index === count($breadcrumbs) - 1);
        
        $html .= '<li class="inline-flex items-center">';
        
        if ($index > 0) {
            $html .= '<svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path></svg>';
        }
        
        if ($isLast) {
            $html .= '<span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">' . htmlspecialchars($crumb['title']) . '</span>';
        } else {
            $html .= '<a href="' . htmlspecialchars($crumb['url']) . '" class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2">' . htmlspecialchars($crumb['title']) . '</a>';
        }
        
        $html .= '</li>';
    }
    
    $html .= '</ol>';
    $html .= '</nav>';
    
    return $html;
}

/**
 * 生成狀態標籤
 * 
 * @param string $status 狀態
 * @param array $statusConfig 狀態配置
 * @return string 狀態標籤HTML
 */
function generateStatusBadge($status, $statusConfig = []) {
    $defaultConfig = [
        'active' => ['class' => 'bg-green-100 text-green-800', 'text' => '啟用'],
        'inactive' => ['class' => 'bg-red-100 text-red-800', 'text' => '停用'],
        'processing' => ['class' => 'bg-yellow-100 text-yellow-800', 'text' => '處理中'],
        'pending' => ['class' => 'bg-blue-100 text-blue-800', 'text' => '待處理'],
    ];
    
    $config = array_merge($defaultConfig, $statusConfig);
    $statusInfo = $config[$status] ?? ['class' => 'bg-gray-100 text-gray-800', 'text' => '未知'];
    
    return '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ' . $statusInfo['class'] . '">' . $statusInfo['text'] . '</span>';
}

/**
 * 生成社群分享按鈕
 * 
 * @param string $url 分享URL
 * @param string $title 分享標題
 * @return string 分享按鈕HTML
 */
function generateShareButtons($url, $title = '') {
    $encodedUrl = urlencode($url);
    $encodedTitle = urlencode($title);
    
    $html = '<div class="flex items-center space-x-3">';
    $html .= '<span class="text-gray-600 text-sm">分享：</span>';
    
    // Facebook
    $html .= '<a href="https://www.facebook.com/sharer/sharer.php?u=' . $encodedUrl . '" target="_blank" rel="noopener" class="text-blue-600 hover:text-blue-800 transition-colors">';
    $html .= '<i class="fab fa-facebook-f"></i>';
    $html .= '</a>';
    
    // Twitter
    $html .= '<a href="https://twitter.com/intent/tweet?url=' . $encodedUrl . '&text=' . $encodedTitle . '" target="_blank" rel="noopener" class="text-blue-400 hover:text-blue-600 transition-colors">';
    $html .= '<i class="fab fa-twitter"></i>';
    $html .= '</a>';
    
    // LinkedIn
    $html .= '<a href="https://www.linkedin.com/sharing/share-offsite/?url=' . $encodedUrl . '" target="_blank" rel="noopener" class="text-blue-700 hover:text-blue-900 transition-colors">';
    $html .= '<i class="fab fa-linkedin-in"></i>';
    $html .= '</a>';
    
    // 複製連結
    $html .= '<button onclick="copyToClipboard(\'' . $url . '\')" class="text-gray-600 hover:text-gray-800 transition-colors">';
    $html .= '<i class="fas fa-link"></i>';
    $html .= '</button>';
    
    $html .= '</div>';
    
    return $html;
}

/**
 * 檢查當前路徑是否匹配
 * 
 * @param string $path 要檢查的路徑
 * @param bool $exact 是否精確匹配
 * @return bool
 */
function isCurrentPath($path, $exact = false) {
    $currentPath = $_SERVER['REQUEST_URI'] ?? '';
    $currentPath = parse_url($currentPath, PHP_URL_PATH);
    
    if ($exact) {
        return $currentPath === $path;
    } else {
        return strpos($currentPath, $path) === 0;
    }
}

/**
 * 生成隨機顏色類別
 * 
 * @return string CSS顏色類別
 */
function getRandomColorClass() {
    $colors = [
        'bg-red-100 text-red-800',
        'bg-blue-100 text-blue-800',
        'bg-green-100 text-green-800',
        'bg-yellow-100 text-yellow-800',
        'bg-purple-100 text-purple-800',
        'bg-pink-100 text-pink-800',
        'bg-indigo-100 text-indigo-800',
    ];
    
    return $colors[array_rand($colors)];
}

/**
 * 清理HTML標籤
 * 
 * @param string $text 原始文字
 * @param array $allowedTags 允許的標籤
 * @return string 清理後的文字
 */
function cleanHtml($text, $allowedTags = []) {
    if (empty($allowedTags)) {
        return strip_tags($text);
    }
    
    return strip_tags($text, '<' . implode('><', $allowedTags) . '>');
}

/**
 * 生成CSRF隱藏欄位
 * 
 * @param string $token CSRF令牌
 * @return string HTML隱藏欄位
 */
function csrfField($token) {
    return '<input type="hidden" name="csrf_token" value="' . htmlspecialchars($token) . '">';
}

/**
 * 生成提示訊息
 * 
 * @param string $message 訊息內容
 * @param string $type 訊息類型 (success, error, warning, info)
 * @return string 提示訊息HTML
 */
function generateAlert($message, $type = 'info') {
    $typeClasses = [
        'success' => 'bg-green-50 border-green-200 text-green-700',
        'error' => 'bg-red-50 border-red-200 text-red-700',
        'warning' => 'bg-yellow-50 border-yellow-200 text-yellow-700',
        'info' => 'bg-blue-50 border-blue-200 text-blue-700',
    ];
    
    $typeIcons = [
        'success' => 'fas fa-check-circle text-green-400',
        'error' => 'fas fa-exclamation-circle text-red-400',
        'warning' => 'fas fa-exclamation-triangle text-yellow-400',
        'info' => 'fas fa-info-circle text-blue-400',
    ];
    
    $class = $typeClasses[$type] ?? $typeClasses['info'];
    $icon = $typeIcons[$type] ?? $typeIcons['info'];
    
    return '<div class="' . $class . ' border rounded-md p-4 mb-4 alert-auto-hide">
        <div class="flex">
            <i class="' . $icon . ' mr-3 mt-1"></i>
            <div>' . htmlspecialchars($message) . '</div>
        </div>
    </div>';
}