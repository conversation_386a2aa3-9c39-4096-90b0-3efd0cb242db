{"name": "phpunit/phpunit", "description": "The PHP Unit Testing framework.", "type": "library", "keywords": ["phpunit", "xunit", "testing"], "homepage": "https://phpunit.de/", "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "support": {"issues": "https://github.com/sebastian<PERSON>mann/phpunit/issues", "security": "https://github.com/sebastian<PERSON>mann/phpunit/security/policy"}, "prefer-stable": true, "require": {"php": ">=7.3", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "ext-xmlwriter": "*", "doctrine/instantiator": "^1.5.0 || ^2", "myclabs/deep-copy": "^1.13.1", "phar-io/manifest": "^2.0.4", "phar-io/version": "^3.2.1", "phpunit/php-code-coverage": "^9.2.32", "phpunit/php-file-iterator": "^3.0.6", "phpunit/php-invoker": "^3.1.1", "phpunit/php-text-template": "^2.0.4", "phpunit/php-timer": "^5.0.3", "sebastian/cli-parser": "^1.0.2", "sebastian/code-unit": "^1.0.8", "sebastian/comparator": "^4.0.8", "sebastian/diff": "^4.0.6", "sebastian/environment": "^5.1.5", "sebastian/exporter": "^4.0.6", "sebastian/global-state": "^5.0.7", "sebastian/object-enumerator": "^4.0.4", "sebastian/resource-operations": "^3.0.4", "sebastian/type": "^3.2.1", "sebastian/version": "^3.0.2"}, "config": {"platform": {"php": "7.3.0"}, "classmap-authoritative": true, "optimize-autoloader": true, "sort-packages": true}, "suggest": {"ext-soap": "To be able to generate mocks based on WSDL files", "ext-xdebug": "PHP extension that provides line coverage as well as branch and path coverage"}, "bin": ["phpunit"], "autoload": {"classmap": ["src/"], "files": ["src/Framework/Assert/Functions.php"]}, "autoload-dev": {"classmap": ["tests/"], "files": ["tests/_files/CoverageNamespacedFunctionTest.php", "tests/_files/CoveredFunction.php", "tests/_files/NamespaceCoveredFunction.php"]}, "extra": {"branch-alias": {"dev-master": "9.6-dev"}}}