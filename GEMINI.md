# Tech.VG 科技新知檢索網站 - Gemini AI 代理指南

這份文件提供了針對 Gemini AI 代理的專案特定指南和慣例。

## 1. 專案概述

Tech.VG 是一個科技新知檢索網站，使用 PHP MVC 框架構建，支援關鍵字檢索和文檔上傳功能。

## 2. 技術棧

*   **後端**: PHP 8.0+ (MVC 架構)
*   **前端**: HTML5 + Tailwind CSS + JavaScript
*   **資料庫**: MySQL 8.0+
*   **文檔處理**: 支援 TXT/Markdown 格式
*   **檢索技術**: MySQL 全文檢索 + 關鍵字匹配

## 3. 專案架構概述

專案遵循 MVC (Model-View-Controller) 架構模式，並包含一個獨立的服務層 (Service Layer)。

*   `app/Controller/`: 處理 HTTP 請求，協調模型和視圖。
*   `app/Model/`: 負責與資料庫互動，包含 `BaseModel` 提供基礎 CRUD 操作。
*   `app/View/`: 包含所有視圖檔案，用於渲染 HTML 頁面。
*   `app/Service/`: 包含核心業務邏輯，例如 `DocumentService`、`NewsService`、`SearchService` 和 `IndexService`。
*   `app/Database/`: 資料庫連接管理。
*   `app/Router/`: 處理 URL 路由和請求分發。
*   `config/`: 應用程式配置檔案 (例如 `app.php`, `database.php`, `routes.php`)。
*   `database/`: 資料庫 Schema (`schema.sql`) 和其他資料庫相關檔案。
*   `htdocs/`: 網站的公開入口點，所有請求都透過 `index.php` 處理。
*   `storage/`: 用於儲存日誌檔案等。
*   `tests/`: 包含單元測試和整合測試。

## 4. 編碼風格與慣例

*   **語言**: PHP
*   **縮排**: 4 個空格
*   **命名慣例**:
    *   類別 (Classes): `PascalCase` (例如 `HomeController`, `DocumentModel`)
    *   方法/函數 (Methods/Functions): `camelCase` (例如 `index()`, `getLatestNews()`)
    *   變數 (Variables): `camelCase` (例如 `$searchService`, `$latestNews`)
    *   資料庫欄位 (Database Columns): `snake_case` (例如 `created_at`, `file_name`)
*   **註解**: 盡量使用 PHPDoc 格式，解釋程式碼的用途和複雜邏輯。
*   **檔案編碼**: UTF-8

## 5. 開發與測試命令

*   **執行所有測試**: `php scripts/run_tests.php`
*   **執行控制器測試**: `php scripts/run_controller_tests.php`
*   **執行 PHPUnit 測試**: `vendor/bin/phpunit` (需在本地安裝 Composer 依賴)
*   **資料庫初始化**:
    1.  建立資料庫: `mysqladmin -u root create techvg` (假設用戶名為 root)
    2.  執行 Schema: `mysql -u root techvg < database/schema.sql`

## 6. 特定注意事項

*   **共享主機環境**:
    *   **無法直接執行 `composer` 命令**: 由於部署在共享主機上，無法直接在伺服器上執行 `composer install` 或 `composer update`。
    *   **Composer 依賴管理流程**: 所有 Composer 相關的依賴更新和自動載入器生成，都必須在**本地開發環境**完成。完成後，將本地的 `vendor` 目錄完整地**上傳**到共享主機的專案根目錄。
    *   **快取問題**: 共享主機可能存在 PHP 或 OPcache 快取，導致檔案更新後不立即生效。如果遇到 `Class not found` 或其他檔案載入問題，請嘗試檢查主機控制面板是否有清除 PHP 快取的功能。
*   **資料庫操作**: 任何修改資料庫結構的操作，請務必先備份 `database/schema.sql`。
*   **環境變數**: 應用程式設定透過 `.env` 檔案管理，請勿將敏感資訊硬編碼在程式碼中。

* 所有的回答請你用中文回覆。如果你需要創建或寫入文件，因為你一次性輸出的token不能太長，所以請你先創建文件，然後分10多次寫入，要不容易失敗！如果你有思維鏈，也請中文回覆輸出。我使用的是powershell，在PowerShell中，我們需要使用分號而不是&&。也不是使用mkdir 來建立目錄，建立專案目錄可以透過 New-Item -ItemType Directory 指令實現，你不應該亂刪除現有的函數，以免原程序運行不了，你在做修改的時候，都要顧及到全局需要修改的地方。 生成的目錄以及程式碼請放置在程式專案建立的目錄下。資料表欄位說明請用繁體中文

## 7. 常用檔案路徑

*   **應用程式配置**: `config/app.php`
*   **資料庫配置**: `config/database.php`
*   **路由配置**: `config/routes.php`
*   **資料庫 Schema**: `database/schema.sql`
*   **環境變數範本**: `.env.example`
*   **錯誤日誌**: `storage/logs/error.log`
