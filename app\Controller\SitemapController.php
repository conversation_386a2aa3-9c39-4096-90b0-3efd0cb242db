<?php

namespace App\Controller;

use App\Service\NewsService;
use App\Service\DocumentService;

/**
 * Sitemap 控制器
 * 生成網站地圖
 */
class SitemapController extends BaseController
{
    private NewsService $newsService;
    private DocumentService $documentService;

    public function __construct()
    {
        $this->newsService = new NewsService();
        $this->documentService = new DocumentService();
    }

    /**
     * 生成 XML Sitemap
     */
    public function index(): void
    {
        // 設定 XML 標頭
        header('Content-Type: application/xml; charset=utf-8');

        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

        // 靜態頁面
        $staticPages = [
            ['url' => '/', 'priority' => '1.0', 'changefreq' => 'daily'],
            ['url' => '/search', 'priority' => '0.9', 'changefreq' => 'daily'],
            ['url' => '/documents', 'priority' => '0.8', 'changefreq' => 'daily'],
            ['url' => '/news', 'priority' => '0.8', 'changefreq' => 'hourly'],
            ['url' => '/about', 'priority' => '0.5', 'changefreq' => 'monthly'],
            ['url' => '/contact', 'priority' => '0.5', 'changefreq' => 'monthly'],
            ['url' => '/privacy', 'priority' => '0.3', 'changefreq' => 'yearly'],
            ['url' => '/terms', 'priority' => '0.3', 'changefreq' => 'yearly'],
        ];

        foreach ($staticPages as $page) {
            $xml .= $this->generateUrlXml(
                url($page['url']),
                $page['priority'],
                $page['changefreq']
            );
        }

        // 新聞頁面
        try {
            $news = $this->newsService->getLatestNews(1000);
            if (isset($news['data'])) {
                foreach ($news['data'] as $article) {
                    $xml .= $this->generateUrlXml(
                        url('/news/' . $article['id']),
                        '0.7',
                        'weekly',
                        $article['published_at'] ?? null
                    );
                }
            }
        } catch (\Exception $e) {
            // 忽略新聞獲取錯誤
        }

        // 文檔頁面
        try {
            $documents = $this->documentService->getAllDocuments(1000);
            if (isset($documents['data'])) {
                foreach ($documents['data'] as $document) {
                    $xml .= $this->generateUrlXml(
                        url('/documents/' . $document['id']),
                        '0.6',
                        'monthly',
                        $document['created_at'] ?? null
                    );
                }
            }
        } catch (\Exception $e) {
            // 忽略文檔獲取錯誤
        }

        $xml .= '</urlset>';

        echo $xml;
    }

    /**
     * 生成單個 URL 的 XML
     */
    private function generateUrlXml(string $url, string $priority, string $changefreq, ?string $lastmod = null): string
    {
        $xml = '<url>' . "\n";
        $xml .= '<loc>' . htmlspecialchars($url) . '</loc>' . "\n";
        $xml .= '<priority>' . $priority . '</priority>' . "\n";
        $xml .= '<changefreq>' . $changefreq . '</changefreq>' . "\n";
        
        if ($lastmod) {
            $xml .= '<lastmod>' . date('c', strtotime($lastmod)) . '</lastmod>' . "\n";
        } else {
            $xml .= '<lastmod>' . date('c') . '</lastmod>' . "\n";
        }
        
        $xml .= '</url>' . "\n";

        return $xml;
    }
}