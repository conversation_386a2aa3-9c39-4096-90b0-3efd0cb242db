<?php
/**
 * API 文檔頁面
 * 提供互動式 API 文檔介面
 */

$title = 'Tech.VG API 文檔';
$description = '完整的 Tech.VG API 文檔，包含所有端點說明、參數和範例';

// 取得 API 文檔資料
$apiDocs = $data['endpoints'] ?? [];
$baseUrl = $data['base_url'] ?? '';
?>

<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($title) ?></title>
    <meta name="description" content="<?= htmlspecialchars($description) ?>">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --dark-color: #1e293b;
            --light-color: #f8fafc;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            color: var(--dark-color);
        }

        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: 300px;
            background: var(--light-color);
            border-right: 1px solid #e2e8f0;
            overflow-y: auto;
            z-index: 1000;
        }

        .main-content {
            margin-left: 300px;
            padding: 2rem;
        }

        .api-header {
            background: linear-gradient(135deg, var(--primary-color), #3b82f6);
            color: white;
            padding: 2rem;
            margin-bottom: 2rem;
            border-radius: 0.5rem;
        }

        .endpoint-card {
            border: 1px solid #e2e8f0;
            border-radius: 0.5rem;
            margin-bottom: 1.5rem;
            overflow: hidden;
        }

        .endpoint-header {
            background: var(--light-color);
            padding: 1rem;
            border-bottom: 1px solid #e2e8f0;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .endpoint-header:hover {
            background: #f1f5f9;
        }

        .endpoint-content {
            padding: 1.5rem;
            display: none;
        }

        .endpoint-content.active {
            display: block;
        }

        .method-badge {
            font-size: 0.75rem;
            font-weight: 600;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            text-transform: uppercase;
        }

        .method-get { background: var(--success-color); color: white; }
        .method-post { background: var(--primary-color); color: white; }
        .method-put { background: var(--warning-color); color: white; }
        .method-delete { background: var(--danger-color); color: white; }

        .parameter-table {
            margin-top: 1rem;
        }

        .parameter-table th {
            background: var(--light-color);
            font-weight: 600;
            border: 1px solid #e2e8f0;
        }

        .parameter-table td {
            border: 1px solid #e2e8f0;
        }

        .required-badge {
            background: var(--danger-color);
            color: white;
            font-size: 0.625rem;
            padding: 0.125rem 0.25rem;
            border-radius: 0.125rem;
        }

        .optional-badge {
            background: var(--secondary-color);
            color: white;
            font-size: 0.625rem;
            padding: 0.125rem 0.25rem;
            border-radius: 0.125rem;
        }

        .code-block {
            background: #1e293b;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
            margin: 1rem 0;
        }

        .try-it-section {
            background: var(--light-color);
            padding: 1.5rem;
            border-radius: 0.5rem;
            margin-top: 1.5rem;
        }

        .response-section {
            margin-top: 1rem;
            padding: 1rem;
            background: #f8fafc;
            border-radius: 0.5rem;
            border-left: 4px solid var(--success-color);
        }

        .nav-link {
            color: var(--secondary-color);
            text-decoration: none;
            padding: 0.5rem 1rem;
            display: block;
            border-radius: 0.25rem;
            margin: 0.125rem 0;
        }

        .nav-link:hover {
            background: #e2e8f0;
            color: var(--primary-color);
        }

        .nav-link.active {
            background: var(--primary-color);
            color: white;
        }

        .search-box {
            padding: 1rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .search-input {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #e2e8f0;
            border-radius: 0.25rem;
            font-size: 0.875rem;
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s;
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .mobile-menu-btn {
                position: fixed;
                top: 1rem;
                left: 1rem;
                z-index: 1001;
                background: var(--primary-color);
                color: white;
                border: none;
                padding: 0.5rem;
                border-radius: 0.25rem;
            }
        }
    </style>
</head>
<body>
    <!-- 手機選單按鈕 -->
    <button class="mobile-menu-btn d-md-none" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <!-- 側邊欄 -->
    <div class="sidebar" id="sidebar">
        <div class="search-box">
            <input type="text" class="search-input" placeholder="搜尋 API..." onkeyup="searchEndpoints(this.value)">
        </div>
        
        <div class="p-3">
            <h6 class="text-muted mb-3">API 端點</h6>
            <nav id="api-nav">
                <?php foreach ($apiDocs as $category => $categoryData): ?>
                    <div class="mb-3">
                        <h6 class="text-primary mb-2"><?= htmlspecialchars($categoryData['title']) ?></h6>
                        <?php foreach ($categoryData['endpoints'] as $index => $endpoint): ?>
                            <a href="#<?= $category ?>-<?= $index ?>" class="nav-link" onclick="scrollToEndpoint('<?= $category ?>-<?= $index ?>')">
                                <span class="method-badge method-<?= strtolower($endpoint['method']) ?>"><?= htmlspecialchars($endpoint['method']) ?></span>
                                <?= htmlspecialchars($endpoint['path']) ?>
                            </a>
                        <?php endforeach; ?>
                    </div>
                <?php endforeach; ?>
            </nav>
        </div>
    </div>

    <!-- 主要內容 -->
    <div class="main-content">
        <!-- API 標題 -->
        <div class="api-header">
            <h1><i class="fas fa-code"></i> Tech.VG API 文檔</h1>
            <p class="mb-0">完整的 RESTful API 文檔，提供科技新知檢索和管理功能</p>
            <div class="mt-3">
                <span class="badge bg-light text-dark me-2">版本: v1</span>
                <span class="badge bg-light text-dark me-2">基礎 URL: <?= htmlspecialchars($baseUrl) ?>/api</span>
                <span class="badge bg-success">線上服務</span>
            </div>
        </div>

        <!-- 快速開始 -->
        <div class="card mb-4">
            <div class="card-header">
                <h3><i class="fas fa-rocket"></i> 快速開始</h3>
            </div>
            <div class="card-body">
                <p>歡迎使用 Tech.VG API！以下是一個簡單的範例來開始使用我們的 API：</p>
                
                <div class="code-block">
                    <pre><code># 搜尋內容
curl -X GET "<?= htmlspecialchars($baseUrl) ?>/api/search?q=人工智慧"

# 取得文檔列表
curl -X GET "<?= htmlspecialchars($baseUrl) ?>/api/documents"

# 取得最新新聞
curl -X GET "<?= htmlspecialchars($baseUrl) ?>/api/news/latest"</code></pre>
                </div>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>提示：</strong> 大部分的讀取操作不需要認證，但上傳和修改操作需要 API 金鑰。
                </div>
            </div>
        </div>

        <!-- API 端點 -->
        <?php foreach ($apiDocs as $category => $categoryData): ?>
            <div class="card mb-4">
                <div class="card-header">
                    <h3 id="<?= $category ?>"><i class="fas fa-folder"></i> <?= htmlspecialchars($categoryData['title']) ?></h3>
                    <p class="mb-0 text-muted"><?= htmlspecialchars($categoryData['description']) ?></p>
                </div>
                <div class="card-body">
                    <?php foreach ($categoryData['endpoints'] as $index => $endpoint): ?>
                        <div class="endpoint-card" id="<?= $category ?>-<?= $index ?>">
                            <div class="endpoint-header" onclick="toggleEndpoint('<?= $category ?>-<?= $index ?>')">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <span class="method-badge method-<?= strtolower(explode('/', $endpoint['method'])[0]) ?>">
                                            <?= htmlspecialchars($endpoint['method']) ?>
                                        </span>
                                        <strong class="ms-2"><?= htmlspecialchars($endpoint['path']) ?></strong>
                                    </div>
                                    <i class="fas fa-chevron-down"></i>
                                </div>
                                <p class="mb-0 mt-2 text-muted"><?= htmlspecialchars($endpoint['description']) ?></p>
                            </div>
                            
                            <div class="endpoint-content" id="content-<?= $category ?>-<?= $index ?>">
                                <?php if (!empty($endpoint['parameters'])): ?>
                                    <h5>參數</h5>
                                    <table class="table parameter-table">
                                        <thead>
                                            <tr>
                                                <th>參數名稱</th>
                                                <th>類型</th>
                                                <th>必填</th>
                                                <th>說明</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($endpoint['parameters'] as $paramName => $param): ?>
                                                <tr>
                                                    <td><code><?= htmlspecialchars($paramName) ?></code></td>
                                                    <td><span class="badge bg-secondary"><?= htmlspecialchars($param['type']) ?></span></td>
                                                    <td>
                                                        <?php if ($param['required']): ?>
                                                            <span class="required-badge">必填</span>
                                                        <?php else: ?>
                                                            <span class="optional-badge">選填</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td><?= htmlspecialchars($param['description']) ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                <?php endif; ?>

                                <!-- 範例請求 -->
                                <h5>範例請求</h5>
                                <div class="code-block">
                                    <pre><code><?= generateCurlExample($endpoint, $baseUrl) ?></code></pre>
                                </div>

                                <!-- 範例響應 -->
                                <h5>範例響應</h5>
                                <div class="code-block">
                                    <pre><code><?= generateResponseExample($endpoint) ?></code></pre>
                                </div>

                                <!-- 試用工具 -->
                                <div class="try-it-section">
                                    <h5><i class="fas fa-play"></i> 試用此 API</h5>
                                    <form onsubmit="tryEndpoint(event, '<?= $category ?>-<?= $index ?>')">
                                        <?php if (!empty($endpoint['parameters'])): ?>
                                            <div class="row">
                                                <?php foreach ($endpoint['parameters'] as $paramName => $param): ?>
                                                    <div class="col-md-6 mb-3">
                                                        <label class="form-label">
                                                            <?= htmlspecialchars($paramName) ?>
                                                            <?php if ($param['required']): ?>
                                                                <span class="text-danger">*</span>
                                                            <?php endif; ?>
                                                        </label>
                                                        <input type="text" 
                                                               class="form-control" 
                                                               name="<?= htmlspecialchars($paramName) ?>"
                                                               placeholder="<?= htmlspecialchars($param['description']) ?>"
                                                               <?= $param['required'] ? 'required' : '' ?>>
                                                    </div>
                                                <?php endforeach; ?>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-paper-plane"></i> 發送請求
                                        </button>
                                    </form>
                                    
                                    <div class="response-section" id="response-<?= $category ?>-<?= $index ?>" style="display: none;">
                                        <h6>響應結果</h6>
                                        <pre><code id="response-content-<?= $category ?>-<?= $index ?>"></code></pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endforeach; ?>

        <!-- 錯誤代碼 -->
        <div class="card mb-4">
            <div class="card-header">
                <h3><i class="fas fa-exclamation-triangle"></i> 錯誤代碼</h3>
            </div>
            <div class="card-body">
                <table class="table">
                    <thead>
                        <tr>
                            <th>HTTP 狀態碼</th>
                            <th>錯誤代碼</th>
                            <th>說明</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><span class="badge bg-warning">400</span></td>
                            <td>BAD_REQUEST</td>
                            <td>請求參數錯誤或格式不正確</td>
                        </tr>
                        <tr>
                            <td><span class="badge bg-warning">401</span></td>
                            <td>UNAUTHORIZED</td>
                            <td>未提供有效的 API 金鑰</td>
                        </tr>
                        <tr>
                            <td><span class="badge bg-warning">403</span></td>
                            <td>FORBIDDEN</td>
                            <td>沒有權限存取此資源</td>
                        </tr>
                        <tr>
                            <td><span class="badge bg-danger">404</span></td>
                            <td>NOT_FOUND</td>
                            <td>請求的資源不存在</td>
                        </tr>
                        <tr>
                            <td><span class="badge bg-danger">429</span></td>
                            <td>RATE_LIMIT_EXCEEDED</td>
                            <td>請求頻率超過限制</td>
                        </tr>
                        <tr>
                            <td><span class="badge bg-danger">500</span></td>
                            <td>INTERNAL_ERROR</td>
                            <td>伺服器內部錯誤</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- SDK 和工具 -->
        <div class="card mb-4">
            <div class="card-header">
                <h3><i class="fas fa-tools"></i> SDK 和工具</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="fab fa-js-square fa-3x text-warning mb-3"></i>
                                <h5>JavaScript SDK</h5>
                                <p class="text-muted">適用於前端和 Node.js 的 SDK</p>
                                <a href="#" class="btn btn-outline-primary">下載</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="fab fa-python fa-3x text-primary mb-3"></i>
                                <h5>Python SDK</h5>
                                <p class="text-muted">適用於 Python 應用程式的 SDK</p>
                                <a href="#" class="btn btn-outline-primary">下載</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-file-code fa-3x text-success mb-3"></i>
                                <h5>OpenAPI 規格</h5>
                                <p class="text-muted">標準的 OpenAPI 3.0 規格檔案</p>
                                <a href="<?= htmlspecialchars($baseUrl) ?>/api/docs/openapi" class="btn btn-outline-primary">下載</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    
    <script>
        // 切換側邊欄
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('show');
        }

        // 切換端點內容
        function toggleEndpoint(endpointId) {
            const content = document.getElementById('content-' + endpointId);
            const icon = content.previousElementSibling.querySelector('.fa-chevron-down');
            
            if (content.classList.contains('active')) {
                content.classList.remove('active');
                icon.style.transform = 'rotate(0deg)';
            } else {
                content.classList.add('active');
                icon.style.transform = 'rotate(180deg)';
            }
        }

        // 滾動到端點
        function scrollToEndpoint(endpointId) {
            const element = document.getElementById(endpointId);
            if (element) {
                element.scrollIntoView({ behavior: 'smooth' });
                
                // 自動展開內容
                const content = document.getElementById('content-' + endpointId);
                if (!content.classList.contains('active')) {
                    toggleEndpoint(endpointId);
                }
            }
        }

        // 搜尋端點
        function searchEndpoints(query) {
            const nav = document.getElementById('api-nav');
            const links = nav.querySelectorAll('.nav-link');
            
            links.forEach(link => {
                const text = link.textContent.toLowerCase();
                if (text.includes(query.toLowerCase())) {
                    link.style.display = 'block';
                } else {
                    link.style.display = 'none';
                }
            });
        }

        // 試用 API
        async function tryEndpoint(event, endpointId) {
            event.preventDefault();
            
            const form = event.target;
            const formData = new FormData(form);
            const params = new URLSearchParams();
            
            for (let [key, value] of formData.entries()) {
                if (value.trim()) {
                    params.append(key, value);
                }
            }
            
            const responseDiv = document.getElementById('response-' + endpointId);
            const responseContent = document.getElementById('response-content-' + endpointId);
            
            try {
                responseContent.textContent = '發送請求中...';
                responseDiv.style.display = 'block';
                
                // 這裡需要根據實際的端點來構建 URL
                const url = '<?= htmlspecialchars($baseUrl) ?>/api/search?' + params.toString();
                
                const response = await fetch(url);
                const data = await response.json();
                
                responseContent.textContent = JSON.stringify(data, null, 2);
                
                // 使用 Prism 高亮顯示
                Prism.highlightElement(responseContent);
                
            } catch (error) {
                responseContent.textContent = '錯誤: ' + error.message;
            }
        }

        // 頁面載入完成後的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 高亮顯示所有程式碼區塊
            Prism.highlightAll();
            
            // 設定活動導航連結
            const hash = window.location.hash;
            if (hash) {
                const endpointId = hash.substring(1);
                scrollToEndpoint(endpointId);
            }
        });
    </script>
</body>
</html>

<?php
/**
 * 生成 cURL 範例
 */
function generateCurlExample($endpoint, $baseUrl): string {
    $method = explode('/', $endpoint['method'])[0];
    $path = $endpoint['path'];
    $url = $baseUrl . '/api' . $path;
    
    $curl = "curl -X {$method} \"{$url}\"";
    
    if (!empty($endpoint['parameters'])) {
        $params = [];
        foreach ($endpoint['parameters'] as $name => $param) {
            if ($param['required']) {
                $example = getExampleValue($param['type']);
                $params[] = "{$name}={$example}";
            }
        }
        
        if (!empty($params)) {
            if ($method === 'GET') {
                $curl .= '?' . implode('&', $params);
            } else {
                $curl .= " \\\n  -d \"" . implode('&', $params) . "\"";
            }
        }
    }
    
    return $curl;
}

/**
 * 生成響應範例
 */
function generateResponseExample($endpoint): string {
    return json_encode([
        'success' => true,
        'message' => '操作成功',
        'data' => [
            'example' => '這是範例資料'
        ],
        'timestamp' => '2024-01-15T10:30:00Z',
        'api_version' => 'v1'
    ], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
}

/**
 * 取得範例值
 */
function getExampleValue($type): string {
    switch ($type) {
        case 'string':
            return 'example';
        case 'integer':
            return '1';
        case 'boolean':
            return 'true';
        case 'file':
            return '@file.txt';
        default:
            return 'value';
    }
}
?>