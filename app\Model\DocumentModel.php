<?php

namespace App\Model;

/**
 * 文檔模型
 * 處理文檔相關的資料操作
 */
class DocumentModel extends BaseModel
{
    protected string $table = 'document';
    
    protected array $fillable = [
        'title',
        'fileName',
        'fileType',
        'fileSize',
        'filePath',
        'uploadedBy',
        'status'
    ];
    
    protected array $casts = [
        'id' => 'int',
        'fileSize' => 'int',
        'uploadedBy' => 'int',
        'createdAt' => 'datetime',
        'updatedAt' => 'datetime'
    ];

    /**
     * 根據狀態查找文檔
     * 
     * @param string $status
     * @param int $limit
     * @return array
     */
    public function findByStatus(string $status, int $limit = 50): array
    {
        return $this->findAll(['status' => $status], 'createdAt DESC', $limit);
    }

    /**
     * 根據上傳者查找文檔
     * 
     * @param int $userId
     * @param int $limit
     * @return array
     */
    public function findByUploader(int $userId, int $limit = 50): array
    {
        return $this->findAll(['uploadedBy' => $userId], 'createdAt DESC', $limit);
    }

    /**
     * 根據檔案類型查找文檔
     * 
     * @param string $fileType
     * @param int $limit
     * @return array
     */
    public function findByFileType(string $fileType, int $limit = 50): array
    {
        return $this->findAll(['fileType' => $fileType], 'createdAt DESC', $limit);
    }

    /**
     * 搜尋文檔標題
     * 
     * @param string $keyword
     * @param int $limit
     * @return array
     */
    public function searchByTitle(string $keyword, int $limit = 50): array
    {
        $sql = "SELECT * FROM {$this->table} WHERE title LIKE ? ORDER BY createdAt DESC LIMIT ?";
        $results = $this->db->fetchAll($sql, ["%{$keyword}%", $limit]);
        
        return array_map([$this, 'castAttributes'], $results);
    }

    /**
     * 取得文檔統計資訊
     * 
     * @return array
     */
    public function getStatistics(): array
    {
        $sql = "
            SELECT 
                COUNT(*) as total,
                COUNT(CASE WHEN status = 'indexed' THEN 1 END) as indexed,
                COUNT(CASE WHEN status = 'processing' THEN 1 END) as processing,
                COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed,
                SUM(fileSize) as total_size,
                COUNT(CASE WHEN fileType = 'txt' THEN 1 END) as txt_files,
                COUNT(CASE WHEN fileType = 'markdown' THEN 1 END) as markdown_files
            FROM {$this->table}
        ";
        
        return $this->db->fetchOne($sql) ?: [];
    }

    /**
     * 更新文檔狀態
     * 
     * @param int $id
     * @param string $status
     * @return int
     */
    public function updateStatus(int $id, string $status): int
    {
        return $this->update($id, ['status' => $status]);
    }

    /**
     * 取得最近上傳的文檔
     * 
     * @param int $limit
     * @return array
     */
    public function getRecent(int $limit = 10): array
    {
        return $this->findAll([], 'createdAt DESC', $limit);
    }

    /**
     * 根據檔案大小範圍查找文檔
     * 
     * @param int $minSize
     * @param int $maxSize
     * @return array
     */
    public function findBySizeRange(int $minSize, int $maxSize): array
    {
        $conditions = [
            'fileSize >=' => $minSize,
            'fileSize <=' => $maxSize
        ];
        
        return $this->findAll($conditions, 'fileSize DESC');
    }
}