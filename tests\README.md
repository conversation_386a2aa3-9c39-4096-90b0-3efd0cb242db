# Tech.VG 網站測試文檔

本文檔說明如何運行和管理 Tech.VG 網站的測試套件。

## 測試結構

```
tests/
├── Unit/                    # 單元測試
│   └── Controller/         # 控制器單元測試
│       ├── Api/           # API 控制器測試
│       └── ...
├── Integration/            # 整合測試
│   ├── Api/               # API 整合測試
│   └── ...
├── Performance/            # 效能測試
├── config/                 # 測試配置
├── data/                   # 測試資料
├── helpers/                # 測試輔助工具
└── results/                # 測試結果
```

## 測試類型

### 1. 單元測試 (Unit Tests)
測試個別的類別和方法，使用模擬物件隔離依賴。

**已完成的單元測試：**
- `HomeControllerTest` - 首頁控制器測試
- `ErrorControllerTest` - 錯誤控制器測試
- `DocumentControllerTest` - 文檔 API 控制器測試
- `NewsControllerTest` - 新聞 API 控制器測試
- `SearchControllerTest` - 搜尋 API 控制器測試
- `CacheControllerTest` - 快取 API 控制器測試
- `StatsControllerTest` - 統計 API 控制器測試

### 2. 整合測試 (Integration Tests)
測試多個組件之間的互動和完整的工作流程。

**已完成的整合測試：**
- `ControllerIntegrationTest` - 控制器整合測試
- `ApiIntegrationTest` - API 整合測試

### 3. 效能測試 (Performance Tests)
測試系統的效能和負載能力。

**已完成的效能測試：**
- `ApiPerformanceTest` - API 效能測試

## 環境設定

### 1. 資料庫設定
建立測試資料庫：

```sql
CREATE DATABASE techvg_test CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'test_user'@'localhost' IDENTIFIED BY 'test_password';
GRANT ALL PRIVILEGES ON techvg_test.* TO 'test_user'@'localhost';
FLUSH PRIVILEGES;
```

### 2. 環境變數
複製並修改測試環境配置：

```bash
cp tests/.env.testing.example tests/.env.testing
```

編輯 `tests/.env.testing` 文件：

```
TEST_DB_HOST=localhost
TEST_DB_DATABASE=techvg_test
TEST_DB_USERNAME=test_user
TEST_DB_PASSWORD=test_password
TEST_API_BASE_URL=http://localhost:8000
TEST_APP_BASE_URL=http://localhost:8000
```

### 3. 目錄權限
確保測試目錄有適當的權限：

```bash
chmod -R 755 tests/
mkdir -p tests/{data,uploads,cache,logs,results}
chmod -R 777 tests/{uploads,cache,logs,results}
```

## 運行測試

### 使用測試運行腳本

```bash
# 運行所有控制器測試
php scripts/run_controller_tests.php all

# 運行單元測試
php scripts/run_controller_tests.php unit

# 運行整合測試
php scripts/run_controller_tests.php integration

# 運行 API 測試
php scripts/run_controller_tests.php api

# 運行效能測試
php scripts/run_controller_tests.php performance

# 運行特定測試類別
php scripts/run_controller_tests.php specific --class=Tests\\Unit\\Controller\\HomeControllerTest

# 生成測試報告
php scripts/run_controller_tests.php report --output=tests/results/report.html
```

### 使用 PHPUnit 直接運行

```bash
# 運行所有測試
./vendor/bin/phpunit

# 運行特定測試套件
./vendor/bin/phpunit --testsuite=unit
./vendor/bin/phpunit --testsuite=integration
./vendor/bin/phpunit --testsuite=performance

# 運行特定測試文件
./vendor/bin/phpunit tests/Unit/Controller/HomeControllerTest.php

# 生成程式碼覆蓋率報告
./vendor/bin/phpunit --coverage-html tests/results/coverage
```

## 測試配置

### 主要配置文件
- `tests/config/test_config.php` - 主要測試配置
- `tests/.env.testing` - 環境變數配置
- `phpunit.xml` - PHPUnit 配置

### 配置選項

**資料庫配置：**
```php
'database' => [
    'host' => 'localhost',
    'dbname' => 'techvg_test',
    'username' => 'test_user',
    'password' => 'test_password'
]
```

**API 配置：**
```php
'api' => [
    'base_url' => 'http://localhost:8000',
    'timeout' => 30,
    'test_api_key' => 'test_key_123456'
]
```

**效能測試配置：**
```php
'performance' => [
    'max_response_time' => 1000,  // 毫秒
    'concurrent_users' => 10,
    'requests_per_user' => 20
]
```

## 測試資料

### 自動生成測試資料
測試套件包含自動資料生成器，可以建立：

- 測試文檔（50個預設）
- 測試新聞（100個預設）
- 測試搜尋歷史（50個預設）

### 手動建立測試資料

```php
// 在測試中使用資料生成器
$documents = $this->dataGenerator->generateDocuments(10);
$documentIds = $this->dataGenerator->insertDocuments($documents);

$news = $this->dataGenerator->generateNews(20);
$newsIds = $this->dataGenerator->insertNews($news);
```

### 測試資料清理
測試資料會在每個測試後自動清理，確保測試之間的獨立性。

## 測試最佳實踐

### 1. 測試命名
- 使用描述性的測試方法名稱
- 遵循 `test[功能][情境]` 的命名模式
- 例如：`testGetDocumentsList()`, `testCreateDocumentWithInvalidData()`

### 2. 測試結構
- 使用 Arrange-Act-Assert 模式
- 每個測試只測試一個功能點
- 使用模擬物件隔離依賴

### 3. 斷言
- 使用具體的斷言方法
- 提供有意義的錯誤訊息
- 驗證預期的行為和狀態

### 4. 測試資料
- 使用最小必要的測試資料
- 避免硬編碼的值
- 使用資料生成器建立一致的測試資料

## 持續整合

### GitHub Actions 配置
```yaml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.1'
      - name: Install dependencies
        run: composer install
      - name: Run tests
        run: php scripts/run_controller_tests.php all
```

### 測試報告
測試運行後會生成以下報告：
- HTML 測試報告
- 程式碼覆蓋率報告
- 效能測試報告
- 錯誤日誌

## 故障排除

### 常見問題

**1. 資料庫連接失敗**
```
解決方案：
- 檢查資料庫服務是否運行
- 驗證連接參數
- 確認用戶權限
```

**2. 檔案權限錯誤**
```
解決方案：
- 檢查測試目錄權限
- 確保 web 服務器用戶有寫入權限
- 使用 chmod 修正權限
```

**3. 記憶體不足**
```
解決方案：
- 增加 PHP 記憶體限制
- 優化測試資料大小
- 分批運行測試
```

**4. 測試超時**
```
解決方案：
- 增加測試超時時間
- 優化測試邏輯
- 檢查外部依賴
```

### 除錯技巧

**1. 啟用詳細輸出**
```bash
php scripts/run_controller_tests.php unit --verbose
```

**2. 運行單一測試**
```bash
./vendor/bin/phpunit --filter testSpecificMethod
```

**3. 檢查測試日誌**
```bash
tail -f tests/logs/test.log
```

## 貢獻指南

### 添加新測試
1. 在適當的目錄建立測試文件
2. 繼承 `Tests\TestCase` 基礎類別
3. 實作測試方法
4. 更新測試套件配置
5. 運行測試確保通過

### 測試審查清單
- [ ] 測試覆蓋所有重要功能
- [ ] 使用適當的模擬物件
- [ ] 測試邊界條件和錯誤情況
- [ ] 測試方法命名清晰
- [ ] 包含適當的文檔註釋
- [ ] 測試執行時間合理
- [ ] 清理測試資料

## 聯絡資訊

如有測試相關問題，請聯絡開發團隊或建立 Issue。