<?php

namespace Tests\Performance;

use Tests\TestCase;

/**
 * API 效能測試
 * 測試 API 端點的效能和負載能力
 */
class ApiPerformanceTest extends TestCase
{
    private array $performanceResults = [];
    private int $maxResponseTime = 1000; // 最大響應時間（毫秒）
    private int $concurrentUsers = 10;   // 並發用戶數
    private int $requestsPerUser = 20;   // 每用戶請求數

    protected function setUp(): void
    {
        parent::setUp();
        
        // 建立大量測試資料
        $this->createLargeTestDataset();
    }

    /**
     * 建立大量測試資料
     */
    private function createLargeTestDataset(): void
    {
        // 建立 100 個測試文檔
        for ($i = 1; $i <= 100; $i++) {
            $this->createTestDocument([
                'title' => "Performance Test Document {$i}",
                'content' => $this->generateRandomContent(1000),
                'file_type' => $i % 2 === 0 ? 'txt' : 'md'
            ]);
        }

        // 建立 200 個測試新聞
        for ($i = 1; $i <= 200; $i++) {
            $this->createTestNews([
                'title' => "Performance Test News {$i}",
                'summary' => $this->generateRandomContent(200),
                'content' => $this->generateRandomContent(2000),
                'category' => ['ai', 'blockchain', 'mobile', 'web', 'security'][rand(0, 4)],
                'views' => rand(1, 1000)
            ]);
        }
    }

    /**
     * 測試搜尋 API 效能
     */
    public function testSearchApiPerformance(): void
    {
        $testCases = [
            ['q' => 'performance'],
            ['q' => 'test'],
            ['q' => 'document'],
            ['q' => 'news'],
            ['q' => 'artificial intelligence']
        ];

        foreach ($testCases as $params) {
            $this->measureEndpointPerformance('GET', '/api/search', $params, 'Search API');
        }

        // 測試進階搜尋效能
        $advancedSearchData = [
            'query' => 'performance test',
            'type' => 'document',
            'sort_by' => 'relevance'
        ];
        
        $this->measureEndpointPerformance('POST', '/api/search/advanced', $advancedSearchData, 'Advanced Search API');

        // 驗證效能要求
        $this->assertPerformanceRequirements('Search API');
    }

    /**
     * 測試文檔 API 效能
     */
    public function testDocumentApiPerformance(): void
    {
        // 測試文檔列表效能
        $listParams = [
            ['page' => 1, 'per_page' => 20],
            ['page' => 2, 'per_page' => 50],
            ['page' => 1, 'per_page' => 100]
        ];

        foreach ($listParams as $params) {
            $this->measureEndpointPerformance('GET', '/api/documents', $params, 'Document List API');
        }

        // 測試單一文檔效能
        for ($i = 1; $i <= 10; $i++) {
            $this->measureEndpointPerformance('GET', "/api/documents/{$i}", [], 'Document Detail API');
        }

        // 測試文檔統計效能
        $this->measureEndpointPerformance('GET', '/api/documents/stats', [], 'Document Stats API');

        $this->assertPerformanceRequirements('Document API');
    }

    /**
     * 測試新聞 API 效能
     */
    public function testNewsApiPerformance(): void
    {
        // 測試新聞列表效能
        $this->measureEndpointPerformance('GET', '/api/news', ['per_page' => 20], 'News List API');
        
        // 測試最新新聞效能
        $this->measureEndpointPerformance('GET', '/api/news/latest', ['limit' => 10], 'Latest News API');
        
        // 測試熱門新聞效能
        $this->measureEndpointPerformance('GET', '/api/news/popular', ['limit' => 10], 'Popular News API');
        
        // 測試新聞分類效能
        $this->measureEndpointPerformance('GET', '/api/news/categories', [], 'News Categories API');

        $this->assertPerformanceRequirements('News API');
    }

    /**
     * 測試統計 API 效能
     */
    public function testStatsApiPerformance(): void
    {
        $statsEndpoints = [
            '/api/stats/overview',
            '/api/stats/documents',
            '/api/stats/news',
            '/api/stats/search',
            '/api/stats/realtime'
        ];

        foreach ($statsEndpoints as $endpoint) {
            $this->measureEndpointPerformance('GET', $endpoint, [], 'Stats API');
        }

        $this->assertPerformanceRequirements('Stats API');
    }

    /**
     * 測試並發負載
     */
    public function testConcurrentLoad(): void
    {
        $endpoints = [
            ['method' => 'GET', 'path' => '/api/search', 'params' => ['q' => 'load test']],
            ['method' => 'GET', 'path' => '/api/documents', 'params' => ['per_page' => 20]],
            ['method' => 'GET', 'path' => '/api/news/latest', 'params' => ['limit' => 10]],
            ['method' => 'GET', 'path' => '/api/stats/overview', 'params' => []]
        ];

        $results = [];

        foreach ($endpoints as $endpoint) {
            $concurrentResults = $this->runConcurrentRequests(
                $endpoint['method'],
                $endpoint['path'],
                $endpoint['params']
            );
            
            $results[$endpoint['path']] = $concurrentResults;
        }

        // 分析並發測試結果
        $this->analyzeConcurrentResults($results);
    }

    /**
     * 測試記憶體使用量
     */
    public function testMemoryUsage(): void
    {
        $initialMemory = memory_get_usage(true);
        
        // 執行大量 API 請求
        for ($i = 0; $i < 100; $i++) {
            $this->makeHttpRequest('GET', '/api/search', ['q' => "memory test {$i}"]);
            
            // 每 10 次請求檢查記憶體使用量
            if ($i % 10 === 0) {
                $currentMemory = memory_get_usage(true);
                $memoryIncrease = $currentMemory - $initialMemory;
                
                // 記憶體增長不應超過 50MB
                $this->assertLessThan(50 * 1024 * 1024, $memoryIncrease, 
                    "Memory usage should not increase significantly during API requests");
            }
        }
    }

    /**
     * 測試資料庫查詢效能
     */
    public function testDatabaseQueryPerformance(): void
    {
        if (!$this->testDb) {
            $this->markTestSkipped('Database not available for performance testing');
        }

        $queries = [
            "SELECT COUNT(*) FROM documents WHERE status = 'active'",
            "SELECT * FROM documents ORDER BY created_at DESC LIMIT 20",
            "SELECT * FROM news_articles WHERE category = 'ai' ORDER BY published_at DESC LIMIT 10",
            "SELECT COUNT(*) as total_searches FROM search_history WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)"
        ];

        foreach ($queries as $query) {
            $startTime = microtime(true);
            
            $stmt = $this->testDb->prepare($query);
            $stmt->execute();
            $result = $stmt->fetchAll();
            
            $executionTime = (microtime(true) - $startTime) * 1000;
            
            // 資料庫查詢應在 100ms 內完成
            $this->assertLessThan(100, $executionTime, 
                "Database query should complete within 100ms: {$query}");
            
            $this->performanceResults['Database Queries'][] = [
                'query' => $query,
                'execution_time' => $executionTime,
                'result_count' => count($result)
            ];
        }
    }

    /**
     * 測試快取效能
     */
    public function testCachePerformance(): void
    {
        $cacheTestEndpoint = '/api/stats/overview';
        
        // 第一次請求（無快取）
        $firstRequestTime = $this->measureSingleRequest('GET', $cacheTestEndpoint);
        
        // 第二次請求（應該有快取）
        $secondRequestTime = $this->measureSingleRequest('GET', $cacheTestEndpoint);
        
        // 快取應該顯著提升效能
        $improvementRatio = $firstRequestTime / $secondRequestTime;
        $this->assertGreaterThan(1.2, $improvementRatio, 
            'Cache should improve response time by at least 20%');
        
        $this->performanceResults['Cache Performance'] = [
            'first_request' => $firstRequestTime,
            'cached_request' => $secondRequestTime,
            'improvement_ratio' => $improvementRatio
        ];
    }

    /**
     * 測試大資料集處理效能
     */
    public function testLargeDatasetPerformance(): void
    {
        // 測試大分頁查詢
        $largePageSizes = [50, 100, 200];
        
        foreach ($largePageSizes as $pageSize) {
            $responseTime = $this->measureSingleRequest('GET', '/api/documents', [
                'per_page' => $pageSize,
                'page' => 1
            ]);
            
            // 即使是大分頁，響應時間也不應超過 2 秒
            $this->assertLessThan(2000, $responseTime, 
                "Large page size ({$pageSize}) should still respond within 2 seconds");
        }

        // 測試複雜搜尋查詢
        $complexSearchTime = $this->measureSingleRequest('POST', '/api/search/advanced', [
            'query' => 'performance test document',
            'type' => 'all',
            'sort_by' => 'relevance',
            'date_from' => '2024-01-01',
            'date_to' => '2024-12-31'
        ]);
        
        $this->assertLessThan(1500, $complexSearchTime, 
            'Complex search should complete within 1.5 seconds');
    }

    /**
     * 測量端點效能
     * 
     * @param string $method
     * @param string $endpoint
     * @param array $params
     * @param string $category
     */
    private function measureEndpointPerformance(string $method, string $endpoint, array $params, string $category): void
    {
        $times = [];
        $errors = 0;
        
        // 執行多次測試取平均值
        for ($i = 0; $i < 10; $i++) {
            $startTime = microtime(true);
            
            $response = $this->makeHttpRequest($method, $endpoint, $params);
            
            $endTime = microtime(true);
            $responseTime = ($endTime - $startTime) * 1000; // 轉換為毫秒
            
            if ($response['success'] && ($response['data']['success'] ?? false)) {
                $times[] = $responseTime;
            } else {
                $errors++;
            }
        }
        
        if (!empty($times)) {
            $avgTime = array_sum($times) / count($times);
            $minTime = min($times);
            $maxTime = max($times);
            
            $this->performanceResults[$category][] = [
                'endpoint' => $endpoint,
                'method' => $method,
                'avg_time' => $avgTime,
                'min_time' => $minTime,
                'max_time' => $maxTime,
                'error_count' => $errors,
                'success_rate' => (count($times) / 10) * 100
            ];
        }
    }

    /**
     * 測量單一請求時間
     * 
     * @param string $method
     * @param string $endpoint
     * @param array $params
     * @return float
     */
    private function measureSingleRequest(string $method, string $endpoint, array $params = []): float
    {
        $startTime = microtime(true);
        $this->makeHttpRequest($method, $endpoint, $params);
        $endTime = microtime(true);
        
        return ($endTime - $startTime) * 1000; // 毫秒
    }

    /**
     * 執行並發請求
     * 
     * @param string $method
     * @param string $endpoint
     * @param array $params
     * @return array
     */
    private function runConcurrentRequests(string $method, string $endpoint, array $params): array
    {
        $results = [];
        $startTime = microtime(true);
        
        // 模擬並發請求（在實際環境中應使用多進程或多線程）
        for ($user = 0; $user < $this->concurrentUsers; $user++) {
            $userResults = [];
            
            for ($request = 0; $request < $this->requestsPerUser; $request++) {
                $requestStart = microtime(true);
                $response = $this->makeHttpRequest($method, $endpoint, $params);
                $requestEnd = microtime(true);
                
                $userResults[] = [
                    'response_time' => ($requestEnd - $requestStart) * 1000,
                    'success' => $response['success'] && ($response['data']['success'] ?? false)
                ];
            }
            
            $results[] = $userResults;
        }
        
        $totalTime = (microtime(true) - $startTime) * 1000;
        
        return [
            'total_time' => $totalTime,
            'user_results' => $results,
            'total_requests' => $this->concurrentUsers * $this->requestsPerUser
        ];
    }

    /**
     * 分析並發測試結果
     * 
     * @param array $results
     */
    private function analyzeConcurrentResults(array $results): void
    {
        foreach ($results as $endpoint => $result) {
            $allResponseTimes = [];
            $successCount = 0;
            $totalRequests = $result['total_requests'];
            
            foreach ($result['user_results'] as $userResults) {
                foreach ($userResults as $requestResult) {
                    $allResponseTimes[] = $requestResult['response_time'];
                    if ($requestResult['success']) {
                        $successCount++;
                    }
                }
            }
            
            $avgResponseTime = array_sum($allResponseTimes) / count($allResponseTimes);
            $successRate = ($successCount / $totalRequests) * 100;
            $throughput = $totalRequests / ($result['total_time'] / 1000); // 每秒請求數
            
            // 效能斷言
            $this->assertLessThan($this->maxResponseTime, $avgResponseTime, 
                "Average response time for {$endpoint} should be under {$this->maxResponseTime}ms");
            
            $this->assertGreaterThan(95, $successRate, 
                "Success rate for {$endpoint} should be above 95%");
            
            $this->performanceResults['Concurrent Load'][$endpoint] = [
                'avg_response_time' => $avgResponseTime,
                'success_rate' => $successRate,
                'throughput' => $throughput,
                'total_requests' => $totalRequests
            ];
        }
    }

    /**
     * 斷言效能要求
     * 
     * @param string $category
     */
    private function assertPerformanceRequirements(string $category): void
    {
        if (!isset($this->performanceResults[$category])) {
            return;
        }
        
        foreach ($this->performanceResults[$category] as $result) {
            $this->assertLessThan($this->maxResponseTime, $result['avg_time'], 
                "Average response time for {$result['endpoint']} should be under {$this->maxResponseTime}ms");
            
            $this->assertGreaterThan(95, $result['success_rate'], 
                "Success rate for {$result['endpoint']} should be above 95%");
        }
    }

    /**
     * 生成隨機內容
     * 
     * @param int $length
     * @return string
     */
    private function generateRandomContent(int $length): string
    {
        $words = [
            'performance', 'test', 'document', 'content', 'artificial', 'intelligence',
            'machine', 'learning', 'deep', 'neural', 'network', 'algorithm',
            'data', 'science', 'technology', 'innovation', 'research', 'development'
        ];
        
        $content = '';
        while (strlen($content) < $length) {
            $content .= $words[array_rand($words)] . ' ';
        }
        
        return substr($content, 0, $length);
    }

    /**
     * 輸出效能報告
     */
    protected function tearDown(): void
    {
        // 輸出效能測試結果
        if (!empty($this->performanceResults)) {
            echo "\n\n=== Performance Test Results ===\n";
            foreach ($this->performanceResults as $category => $results) {
                echo "\n{$category}:\n";
                if (is_array($results) && isset($results[0]['endpoint'])) {
                    foreach ($results as $result) {
                        echo sprintf(
                            "  %s %s: Avg: %.2fms, Min: %.2fms, Max: %.2fms, Success: %.1f%%\n",
                            $result['method'],
                            $result['endpoint'],
                            $result['avg_time'],
                            $result['min_time'],
                            $result['max_time'],
                            $result['success_rate']
                        );
                    }
                } else {
                    echo "  " . json_encode($results, JSON_PRETTY_PRINT) . "\n";
                }
            }
            echo "\n=== End Performance Results ===\n\n";
        }
        
        parent::tearDown();
    }
}