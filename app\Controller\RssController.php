<?php

namespace App\Controller;

use App\Service\NewsService;

/**
 * RSS 訂閱控制器
 * 提供 RSS 格式的新聞訂閱
 */
class RssController extends BaseController
{
    private NewsService $newsService;

    public function __construct()
    {
        $this->newsService = new NewsService();
    }

    /**
     * 主要 RSS 訂閱
     */
    public function index(): void
    {
        // 設定 RSS 標頭
        header('Content-Type: application/rss+xml; charset=utf-8');

        // 取得最新新聞
        $news = $this->newsService->getLatestNews(20);

        $rssContent = $this->generateRssXml([
            'title' => 'Tech.VG 科技新知',
            'description' => '最新的科技新聞和技術資訊',
            'link' => url('/'),
            'items' => $news['data'] ?? []
        ]);

        echo $rssContent;
    }

    /**
     * 新聞 RSS 訂閱
     */
    public function news(): void
    {
        // 設定 RSS 標頭
        header('Content-Type: application/rss+xml; charset=utf-8');

        // 取得最新新聞
        $news = $this->newsService->getLatestNews(50);

        $rssContent = $this->generateRssXml([
            'title' => 'Tech.VG 科技新聞',
            'description' => '最新的科技新聞資訊',
            'link' => url('/news'),
            'items' => $news['data'] ?? []
        ]);

        echo $rssContent;
    }

    /**
     * 生成 RSS XML
     */
    private function generateRssXml(array $config): string
    {
        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $xml .= '<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">' . "\n";
        $xml .= '<channel>' . "\n";
        
        // 頻道資訊
        $xml .= '<title>' . htmlspecialchars($config['title']) . '</title>' . "\n";
        $xml .= '<description>' . htmlspecialchars($config['description']) . '</description>' . "\n";
        $xml .= '<link>' . htmlspecialchars($config['link']) . '</link>' . "\n";
        $xml .= '<atom:link href="' . htmlspecialchars($_SERVER['REQUEST_URI'] ?? '') . '" rel="self" type="application/rss+xml" />' . "\n";
        $xml .= '<language>zh-TW</language>' . "\n";
        $xml .= '<lastBuildDate>' . date('r') . '</lastBuildDate>' . "\n";
        $xml .= '<generator>Tech.VG RSS Generator</generator>' . "\n";

        // 新聞項目
        foreach ($config['items'] as $item) {
            $xml .= '<item>' . "\n";
            $xml .= '<title>' . htmlspecialchars($item['title'] ?? '') . '</title>' . "\n";
            $xml .= '<description>' . htmlspecialchars($item['summary'] ?? $item['content'] ?? '') . '</description>' . "\n";
            $xml .= '<link>' . htmlspecialchars(url('/news/' . ($item['id'] ?? ''))) . '</link>' . "\n";
            $xml .= '<guid>' . htmlspecialchars(url('/news/' . ($item['id'] ?? ''))) . '</guid>' . "\n";
            
            if (isset($item['published_at'])) {
                $xml .= '<pubDate>' . date('r', strtotime($item['published_at'])) . '</pubDate>' . "\n";
            }
            
            if (isset($item['category'])) {
                $xml .= '<category>' . htmlspecialchars($item['category']) . '</category>' . "\n";
            }
            
            $xml .= '</item>' . "\n";
        }

        $xml .= '</channel>' . "\n";
        $xml .= '</rss>';

        return $xml;
    }
}