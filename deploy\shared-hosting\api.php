<?php
/**
 * 共享主機 API 入口點
 * 
 * 這個檔案處理所有 /api/* 請求
 * 應該放在 htdocs/api/index.php
 */

// 錯誤報告設定
error_reporting(0);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// 設定時區
date_default_timezone_set('Asia/Taipei');

// 定義路徑常數
define('ROOT_PATH', dirname(__DIR__));
define('APP_PATH', ROOT_PATH . '/app');
define('CONFIG_PATH', ROOT_PATH . '/config');
define('STORAGE_PATH', ROOT_PATH . '/storage');

// 載入環境配置
if (file_exists(ROOT_PATH . '/.env')) {
    $envContent = file_get_contents(ROOT_PATH . '/.env');
    $envLines = explode("\n", $envContent);
    
    foreach ($envLines as $line) {
        $line = trim($line);
        if (empty($line) || strpos($line, '#') === 0) {
            continue;
        }
        
        if (strpos($line, '=') !== false) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

// 設定錯誤處理
if (isset($_ENV['APP_DEBUG']) && $_ENV['APP_DEBUG'] === 'true') {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
}

// 自動載入器
spl_autoload_register(function ($className) {
    $className = str_replace('\\', '/', $className);
    $className = str_replace('App/', '', $className);
    
    $file = APP_PATH . '/' . $className . '.php';
    
    if (file_exists($file)) {
        require_once $file;
    }
});

// 載入輔助函數
if (file_exists(APP_PATH . '/helpers.php')) {
    require_once APP_PATH . '/helpers.php';
}

// 設定 CORS 標頭
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-API-Key');

// 處理 OPTIONS 請求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // 載入路由配置
    if (file_exists(CONFIG_PATH . '/routes.php')) {
        $router = require CONFIG_PATH . '/routes.php';
        
        // 取得當前 URI
        $requestUri = $_SERVER['REQUEST_URI'] ?? '/';
        $requestMethod = $_SERVER['REQUEST_METHOD'] ?? 'GET';
        
        // 移除查詢字串
        $uri = parse_url($requestUri, PHP_URL_PATH);
        
        // 確保 URI 以 /api 開頭
        if (strpos($uri, '/api') !== 0) {
            $uri = '/api' . $uri;
        }
        
        // 處理路由
        $router->dispatch($requestMethod, $uri);
    } else {
        throw new Exception('路由配置檔案不存在');
    }
    
} catch (Exception $e) {
    // API 錯誤響應
    http_response_code(500);
    header('Content-Type: application/json');
    
    $response = [
        'success' => false,
        'message' => '伺服器內部錯誤',
        'timestamp' => date('c')
    ];
    
    if (isset($_ENV['APP_DEBUG']) && $_ENV['APP_DEBUG'] === 'true') {
        $response['debug'] = [
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ];
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    
    // 記錄錯誤
    error_log('API Error: ' . $e->getMessage() . ' in ' . $e->getFile() . ':' . $e->getLine());
}