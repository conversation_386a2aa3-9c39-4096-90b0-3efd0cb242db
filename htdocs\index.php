<?php

/**
 * Tech.VG 科技新知檢索網站
 * 應用程式入口點
 */

// 錯誤報告設定
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 設定時區
date_default_timezone_set('Asia/Taipei');

// 定義應用程式根目錄
define('APP_ROOT', dirname(__DIR__));

// 自動載入器
require_once __DIR__ . '/vendor/autoload.php';

// 載入環境變數 (如果存在 .env 文件)
if (file_exists(APP_ROOT . '/.env')) {
    $lines = file(APP_ROOT . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            [$key, $value] = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

// 啟動會話
session_start();

try {
    // 載入配置
    $config = require APP_ROOT . '/config/app.php';
    
    // 設定錯誤處理
    if (!$config['debug']) {
        error_reporting(0);
        ini_set('display_errors', 0);
    }
    
    // 載入路由
    $router = require APP_ROOT . '/config/routes.php';
    
    // 分發請求
    $router->dispatch();
    
} catch (Exception $e) {
    // 錯誤處理
    $errorCode = $e->getCode() ?: 500;
    http_response_code($errorCode);
    
    if (isset($config) && $config['debug']) {
        // 開發模式：顯示詳細錯誤
        echo "<div style='font-family: monospace; padding: 20px; background: #f8f9fa; border: 1px solid #dee2e6;'>";
        echo "<h1 style='color: #dc3545;'>Error {$errorCode}</h1>";
        echo "<p style='color: #6c757d;'>{$e->getMessage()}</p>";
        echo "<h3 style='color: #495057; margin-top: 20px;'>Stack Trace:</h3>";
        echo "<pre style='background: #e9ecef; padding: 15px; border-radius: 5px; overflow-x: auto;'>{$e->getTraceAsString()}</pre>";
        echo "<h3 style='color: #495057; margin-top: 20px;'>Request Info:</h3>";
        echo "<pre style='background: #e9ecef; padding: 15px; border-radius: 5px;'>";
        echo "URI: " . ($_SERVER['REQUEST_URI'] ?? 'N/A') . "\n";
        echo "Method: " . ($_SERVER['REQUEST_METHOD'] ?? 'N/A') . "\n";
        echo "Time: " . date('Y-m-d H:i:s') . "\n";
        echo "</pre>";
        echo "</div>";
    } else {
        // 生產模式：顯示友善錯誤頁面
        try {
            // 嘗試使用ErrorController
            require_once APP_ROOT . '/app/Controller/BaseController.php';
            require_once APP_ROOT . '/app/Controller/ErrorController.php';
            
            $errorController = new \App\Controller\ErrorController();
            $errorController->handleError($errorCode, $e->getMessage());
        } catch (Exception $fallbackException) {
            // 如果ErrorController也失敗，使用靜態錯誤頁面
            switch ($errorCode) {
                case 403:
                    if (file_exists(APP_ROOT . '/app/View/errors/403.php')) {
                        include APP_ROOT . '/app/View/errors/403.php';
                    } else {
                        echo "<h1>403 - 拒絕訪問</h1><p>您沒有權限訪問此資源。</p>";
                    }
                    break;
                case 404:
                    if (file_exists(APP_ROOT . '/app/View/errors/404.php')) {
                        include APP_ROOT . '/app/View/errors/404.php';
                    } else {
                        echo "<h1>404 - 頁面不存在</h1><p>您要尋找的頁面不存在。</p>";
                    }
                    break;
                case 503:
                    if (file_exists(APP_ROOT . '/app/View/errors/503.php')) {
                        include APP_ROOT . '/app/View/errors/503.php';
                    } else {
                        echo "<h1>503 - 服務暫時不可用</h1><p>網站正在維護中。</p>";
                    }
                    break;
                case 500:
                default:
                    if (file_exists(APP_ROOT . '/app/View/errors/500.php')) {
                        include APP_ROOT . '/app/View/errors/500.php';
                    } else {
                        echo "<h1>500 - 伺服器錯誤</h1><p>伺服器發生內部錯誤。</p>";
                    }
                    break;
            }
        }
    }
    
    // 記錄錯誤日誌
    if (isset($config)) {
        $logFile = $config['log']['path'] . '/error.log';
        $logDir = dirname($logFile);
        
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString(),
            'request_uri' => $_SERVER['REQUEST_URI'] ?? '',
            'request_method' => $_SERVER['REQUEST_METHOD'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'ip' => $_SERVER['REMOTE_ADDR'] ?? ''
        ];
        
        file_put_contents($logFile, json_encode($logData, JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND | LOCK_EX);
    }
}